<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>mkimage</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>image.h</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/image.h</locationURI>
		</link>
		<link>
			<name>mkimage.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/mkimage.c</locationURI>
		</link>
		<link>
			<name>sw_aes.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/../../../sdk/platform/core_modules/crypto/sw_aes.c</locationURI>
		</link>
		<link>
			<name>sw_aes.h</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/../../../sdk/platform/core_modules/crypto/sw_aes.h</locationURI>
		</link>
		<link>
			<name>sw_aes_license.txt</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/../../../sdk/platform/core_modules/crypto/sw_aes_license.txt</locationURI>
		</link>
	</linkedResources>
</projectDescription>
