/*
 * SerialInterface.c
 *
 *  Created on: 13-Sep-2023
 *      Author: admin
 */

#include <string.h>
#include "user_periph_setup.h"
#include "uart.h"
#include "ring_buf.h"
#include "Wireless.h"
#include "Comm_Manager.h"
#include "SerialInterface.h"

#ifdef DEBUG_ENABLE
uint8_t breakCount = 0;
#endif

static void uart_send_cb(uint16_t length);
static void uart_receive_cb(uint16_t length);
static void uart_error_cb(uart_t * Uart_Instance, uint8_t err_no);

static uart_cfg_t uart_cfg = {
    .baud_rate = UART_BAUDRATE_230400,
    .data_bits = UART2_DATABITS,
    .parity = UART_PARITY_NONE,
    .stop_bits = UART2_STOPBITS,
    .auto_flow_control = UART2_AFCE,
    .use_fifo = UART2_FIFO,
    .tx_fifo_tr_lvl = UART2_TX_FIFO_LEVEL,
    .rx_fifo_tr_lvl = UART2_RX_FIFO_LEVEL,
    .intr_priority = 2,
	  .uart_err_cb = uart_error_cb,
	  .uart_tx_cb = uart_send_cb,
		.uart_rx_cb = uart_receive_cb,
};

void sendBreakCmd(void){} // dummy function
	
void WirelessUartTx(uint8_t *data, uint32_t len)
{
	uart_send(UART1, data, len, UART_OP_BLOCKING);
}

void Close_Rbuffer(void)
{

}

void Open_Rbuffer(void)
{

}

void uart_trigger(void)
{		
	NVIC_DisableIRQ(ASM_GPIO_IRQ);
	
	bool Edge = GPIO_GetPinStatus(GPIO_PORT_0, GPIO_PIN_4);
	
	if(Edge)
	{
		// handler code 
		COM_STWakeup(D_High);
		// end of handler code 
		// interrupt handling 
		GPIO_ResetIRQ(ASM_GPIO_IRQ);
		NVIC_ClearPendingIRQ(ASM_GPIO_IRQ);
		GPIO_EnableIRQ(GPIO_PORT_0, GPIO_PIN_4, ASM_GPIO_IRQ, true, true, 0);
		
	}
	else
	{
		// handler code 	
		COM_STWakeup(D_Low);		
		// end of handler code 
		// interrupt handling
		GPIO_ResetIRQ(ASM_GPIO_IRQ);
		NVIC_ClearPendingIRQ(ASM_GPIO_IRQ);
		GPIO_EnableIRQ(GPIO_PORT_0, GPIO_PIN_4, ASM_GPIO_IRQ, false, true, 0);
		
		
	}
		
	NVIC_EnableIRQ(ASM_GPIO_IRQ);
}

void port_open(uint8_t state)
{
	if(state)
	{
		UART_OPEN_PORT_INIT();
	}
	else
	{
		UART_CLOSE_PORT_INIT();
	}
}

void wakeupWireless(uint8_t state)
{
	if(state)
	{
		GPIO_SetActive(GPIO_PORT_0, GPIO_PIN_3);
	}
	else
	{
		GPIO_SetInactive(GPIO_PORT_0, GPIO_PIN_3);

	}
}


static void uart_send_cb(uint16_t length);
static void uart_receive_cb(uint16_t length);
static void uart_error_cb(uart_t * Uart_Instance, uint8_t err_no);

static void uart_send_cb(uint16_t length)
{
	
}
static void uart_receive_cb(uint16_t length)
{
	
}
static void uart_error_cb(uart_t * Uart_Instance, uint8_t err_no)
{
	if(err_no & UART_BI)
	{
#ifdef DEBUG_ENABLE
		breakCount++;
		uint8_t val = uart_read_rbr(UART1);
		bool Edge = GPIO_GetPinStatus(GPIO_PORT_0, UART1_RX_PIN);
		uint8_t msg[4] = {'B','B','B','B'};
		msg[1] = val;
		msg[2] = err_no;
		msg[3] = Edge;
		Send_To_Gatt_Client(msg,4, 0x16, NULL); //NODO: debug log
#else
		uart_read_rbr(UART1);
#endif
		COM_BreakEvent();
	}
}


void Open_Master_tPort()
{
	NVIC_DisableIRQ(UART_INTR(UART1));
	GPIO_ConfigurePin(UART1_TX_PORT, UART1_TX_PIN, OUTPUT, PID_UART1_TX, false);
  GPIO_ConfigurePin(UART1_RX_PORT, UART1_RX_PIN, INPUT, PID_UART1_RX, false);
  GPIO_set_pad_latch_en(true);

	uart_initialize(UART1, &uart_cfg);
	
	// Wake up slave ST 
	GPIO_SetActive(GPIO_PORT_0, GPIO_PIN_3);
	
}

void Open_Master_rPort()
{
	uart_rls_error_getf(UART1);
	uart_read_rbr(UART1);

  uart_rxdata_intr_setf(UART1, UART_BIT_EN);    // Enable receive interrupts
	uart_rls_intr_setf(UART1, UART_BIT_EN);       // Enable BREAK
	
	NVIC_SetPriority(UART_INTR(UART1), uart_cfg.intr_priority);// Enable interrupt priority
	NVIC_EnableIRQ(UART_INTR(UART1));  	// Enable NVIC interrupts
}

void Open_Slave_Port()
{
	NVIC_DisableIRQ(UART_INTR(UART1));
	GPIO_ConfigurePin(UART1_TX_PORT, UART1_TX_PIN, OUTPUT, PID_UART1_TX, false);
  GPIO_ConfigurePin(UART1_RX_PORT, UART1_RX_PIN, INPUT, PID_UART1_RX, false);
  GPIO_set_pad_latch_en(true);

	uart_initialize(UART1, &uart_cfg);
	uart_rxdata_intr_setf(UART1, UART_BIT_EN);    // Enable receive interrupts
	uart_rls_intr_setf(UART1, UART_BIT_EN);       // Enable BREAK
	// Enable interrupt priority
	NVIC_SetPriority(UART_INTR(UART1), uart_cfg.intr_priority);
	// Enable NVIC interrupts
	NVIC_EnableIRQ(UART_INTR(UART1));
	
	// Signal ST slave active
	GPIO_SetActive(GPIO_PORT_0, GPIO_PIN_3);
}

void hClose_Port()
{
	// Signal ST master half close
	GPIO_SetInactive(GPIO_PORT_0, GPIO_PIN_3);
}

void Close_Port()
{
    NVIC_DisableIRQ(UART_INTR(UART1));
		uart_rxdata_intr_setf(UART1, UART_BIT_DIS);
    uart_rls_intr_setf(UART1, UART_BIT_DIS);
		uart_disable(UART1);
	  // Signal ST close
	  GPIO_SetInactive(GPIO_PORT_0, GPIO_PIN_3);
}

void Reset_Port()
{
    NVIC_DisableIRQ(UART_INTR(UART1));
		uart_rxdata_intr_setf(UART1, UART_BIT_DIS);
    uart_rls_intr_setf(UART1, UART_BIT_DIS);
		uart_disable(UART1);
}

uint32_t get_crc(uint8_t *data, uint32_t len)
{
	uint32_t crc = 0xFFFFFFFF;
	uint32_t b = 0;
	uint8_t ch = 0x00;
	for(uint32_t index = 0; index < len ; index++)
	{
		ch = data[index];
		for(uint8_t j=0; j<8 ; j++)
		{
			b =(ch^crc)&1;
			crc>>=1;
			if(b)
				crc=crc^0xEDB88320;
			ch>>=1;
		}
	}
	return ~crc ;
}

void EnableRFSwitch(void)
{
	GPIO_SetActive(GPIO_PORT_0, GPIO_PIN_5);
}

void COM_STWakeup(enum Signal signal)
{
	Comm_Queue_t msg;
	msg.module 			= m_isr;
	msg.event.tt.trigger 		= (signal == D_High) ? st_wakeup:peer_close;
	triggerCommMngr(&msg);
}

int _platform_transmit(uint8_t *data, uint16_t len)
{
	WirelessUartTx(data,len);
	COM_TXCEvent();
	return 1;
}

int _platform_break_handle(void)
{
	volatile uint16_t stBleLineStatus = ((*(uint16_t*)0x50003000 & (0x01<<4)));
#if DEBUGGER_UART1_PINCONFIG
	volatile uint16_t rxLineStatus = ((*(uint16_t*)0x50003000 & (0x01<<5)));
#else
	volatile uint16_t rxLineStatus = ((*(uint16_t*)0x50003000 & (0x01<<1)));
#endif
	
	if(stBleLineStatus)
	{
		if(rxLineStatus)
		{
			enableBreak();
			COM_RXEvent();
			/*	CHECK. To call RX event from break interrupt?	
				return handle_rx_event();
			*/
		}
		else
		{
			//port Closed
			return STATE_CHANGE;
		}
  }
	else
	{
		//half close to close
		// ignore the break;
		return STATE_WAIT;
	}
////////////////////////////
}
