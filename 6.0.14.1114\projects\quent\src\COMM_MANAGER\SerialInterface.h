
#ifndef SERIAL_INTERFACE_H_
#define SERIAL_INTERFACE_H_

#include <stdint.h>
#include "uart.h"
#define MAX_DATA			   			 649

void COM_STWakeup(enum Signal signal);
void sendBreakCmd(void);
int _platform_transmit(uint8_t *data, uint16_t len);
int _platform_break_handle(void);

void WirelessUartTx(uint8_t *data, uint32_t len);
int32_t rbuf_get(uint8_t *buffer, uint16_t len);
void port_open(uint8_t state);
void wakeupWireless(uint8_t state);

void UART_CLOSE_PORT_INIT(void);
void UART_OPEN_PORT_INIT(void);
void FLUSH_RING_BUFFER(void);

void Open_Master_tPort(void);
void Open_Master_rPort(void);
void Open_Slave_Port(void);
void hClose_Port(void);
void Close_Port(void);
void Reset_Port(void);
uint32_t get_crc(uint8_t *data, uint32_t len);

#endif
