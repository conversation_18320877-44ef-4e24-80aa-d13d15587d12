/*
 * TransportManager.c
 *
 *  Created on: 09-Sep-2023
 *      Author: admin
 */

#include"Comm_Manager.h"
#include"SerialInterface.h"
#include"UpperLayerInterface.h"
#include"TransportQueue.h"
#include"Comm_Debug.h"

#define TRANSPORT_FAIL
#undef TRANSPORT_TESTING

#if TRANSPORT_MANAGER_DEBUG
#define DEBUG_PRINTS
#else
#undef DEBUG_PRINTS
#endif

#ifdef DEBUG_PRINTS
static void print_buffer(unsigned char *buffer, unsigned int length)
{
	unsigned int j = 0;
	COM_PRINTI("[%d] ",j);
	for(unsigned int i=0; i < length; i++)
	{
		if(buffer[i]<=0x0F)
		{
			COM_PRINTI("0%X ", buffer[i]);
		}
		else
		{
			COM_PRINTI("%X ", buffer[i]);
		}
		if(i%16 == 0)
		{
			j++;
			COM_PRINTI("\r\n");
			COM_PRINTI("[%d] ",j);
		}
	}
}


void print_header(struct _Exchange *packet)
{
	(void)packet;
	COM_PRINTI("Header = %x\r\nID = %d\r\nSUB_ID =%d\r\nLen=%d\r\n",
			packet->Header,packet->ID,
			packet->SUB_ID, packet->size);

}
void print_payload(struct _Exchange *packet)
{
	COM_PRINTI("printing payload\r\n");
	print_buffer(packet->Data,packet->size);
	COM_PRINTI("\r\nexiting printing payload\r\n");
}

void print_footer(struct _Exchange *packet)
{
	(void)packet;
	COM_PRINTI("CRC %x Footer %x \r\n",*((unsigned int*)(packet->Data+packet->size)),*((unsigned char*)(packet->Data+(packet->size+4))));
}
#endif


//#define CHECK_HDR(packet) (packet.Header == HEADER_HDR && (packet.ID <= BUFFERTX))
#define CHECK_HDR(packet) (packet.Header == HEADER_HDR)
#define CHECK_FTR(packet) (packet.Data[packet.size+4] == HEADER_FTR)
#define CHECK_CRC(packet) ( *( (uint32_t *)(packet.Data + packet.size) ) )
#define CHECK_COMACK_PACKET(packet) ((packet.Header == HEADER_HDR) && (packet.ID == comACK) && (packet.Data[4] == HEADER_FTR))
#define CHECK_ACKFIELD(packet) (((packet.size & 0xFF) == ACK) || ((packet.size & 0xFF) == NACK))
#define CHECK_CONTEXT(packet)((ipacket.size >> 8) == ID && ipacket.SUB_ID == SUB_ID)
#define GET_ACK(packet) (packet.size & 0xFF)
#define GET_aID(packet) (packet.size >> 8)


#define RESET_STATE_MACHINE()  do{                                  \
		                          t_changeState(t_idle);            \
		                          handle_change_state_event();      \
		                          return(STATE_WAIT);               \
                                 }while(0)

#define TIMEOUT_STATE_MACHINE()  do{                                  \
		                          t_changeState(t_timeouts);            \
		                          handle_change_state_event();      \
		                          return(STATE_WAIT);               \
                                 }while(0)


enum t_states
{
	t_close 	= 0x00,
	t_idle	    = 0x01,
	master_init	= 0x02,
	m_tx		= 0x03,
	m_rx		= 0x04,
	m_proc		= 0x05,
	slave_init	= 0x06,
	s_rxh		= 0x07,
	s_rxp		= 0x08,
	s_proc		= 0x09,
	s_tx		= 0x0A,
	t_hclose    = 0x0B,
	t_timeouts,
	t_invalidstate
};

#ifdef DOUBLE_BUFFER
typedef struct _Exchange Ipacket;
typedef struct _Exchange Opacket;
static Ipacket ipacket;
static Opacket opacket ;
#else
static struct _Exchange Buffer;
#define ipacket Buffer
#define opacket Buffer
#endif

static uint8_t pending_timeout = 0;
static uint16_t SUB_ID;
static uint8_t  ID;
static enum _ttrigger Trigger = t_invalid;
static enum _ttrigger currentEvent = t_invalid;
enum t_states currentState = t_close;
enum t_states prevState = t_close;
static enum t_ack_status ack_status = t_ack_none;
static struct Buffer_Xfer bufferxfer_context = {0,0,0,0,0,0,0,0};
static uint32_t buffer_transfer_flag = 0;

static int handle_invalid();
static int handle_close();
static int handle_half_close();
static int handle_timeout();
static int handle_idle();
static int handle_master_init();
static int handle_master_tx();
static int buffer_transfer_send();
static int handle_master_rx();
static int handle_master_proc();
static int handle_slave_init();
static int handle_slave_rxh();
static int handle_slave_rxp();
static int handle_slave_proc();
static int handle_slave_tx();

typedef int (*_tstate_function)();

static const _tstate_function tstate_function[]={
		handle_close,
		handle_idle,
		handle_master_init,
		handle_master_tx,
		handle_master_rx,
		handle_master_proc,
		handle_slave_init,
		handle_slave_rxh,
		handle_slave_rxp,
		handle_slave_proc,
		handle_slave_tx,
		handle_half_close,
		handle_timeout,
		handle_invalid
};

static int handle_tx_event(void);
static int handle_st_wakeup_event(void);
static int handle_tx_comp_event(void);
static int handle_rx_event(void);
static int handle_peer_close_event(void);
static int handle_close_event(void);
static int handle_change_state_event(void);
static int handle_ack_event(void);
static int handle_nack_event(void);
static int handle_break_event(void);
static int handle_timeout_event();
static int handle_invalid_event(void);


typedef int (*_ttrigger_function)();

static const _ttrigger_function ttrigger_function[]={
		handle_tx_event,
		handle_st_wakeup_event,
		handle_tx_comp_event,
		handle_rx_event,
		handle_peer_close_event,
		handle_close_event,
		handle_change_state_event,
		handle_ack_event,
		handle_nack_event,
		handle_break_event,
		handle_timeout_event,
		handle_invalid_event
};

static int 	t_changeState(enum t_states state);
static enum	t_states t_nextState();
static int32_t Check_CommCrc(struct _Exchange *packet);

static int handle_invalid()
{
	// handle the invalid state
	return 1;
}

static int handle_change_state_event(void)
{
#ifdef IF_S_TX_TIMING
	if(prevState == s_tx)
		ADJUST_SLAVE_TIMING();
#endif
	int ret = tstate_function[currentState]();
	if(ret)
	{
		ret = t_changeState(t_nextState());
	}
	return ret;
}

static int handle_tx_event(void)
{
	if(currentState == t_close || currentState == t_hclose)
	{
		return t_changeState(master_init);
	}
	else if(currentState == t_idle)
	{
		return t_changeState(m_tx);
	}
	else
	{
		return -1;// invalide state
	}
}

static int handle_st_wakeup_event(void)
{

	if(currentState == t_close)
	{
		return t_changeState(slave_init);
	}
	else if(currentState == master_init)
	{
		return t_changeState(m_tx);
	}
	else
	{
		return -1;// Invalid state.
	}
}


static inline int _slave_tx_complete(void)
{
	switch(ID)
	{
	case COMMAND:
	case RESPONSE:    SetPrimitiveTrigger_Data((ack_status == t_ack_s)?cr_ack_s:cr_nack_s, NULL, 0, comm_NONE);
	break;

	case INDICATION:  SetPrimitiveTrigger_Data((ack_status == t_ack_s)?ind_ack_s:ind_nack_s, NULL, 0, comm_NONE);
	break;

	case BUFFERTX:
	{
		if (ack_status == t_nack_s)
		{
			COM_PRINTE("Bufferxfer complete, sending ind_nack_s \n");
			SetPrimitiveTrigger_Data(ind_nack_s, NULL, 0, comm_NONE);
		}
		else
		{
			if(bufferxfer_context.current_segment < bufferxfer_context.max_segments-1)
			{
				COM_PRINTI("ack sent for seg number [%d]\n", bufferxfer_context.current_segment);
				return (STATE_WAIT);
			}
			else
			{
				COM_PRINTI("Bufferxfer complete, sending ind_ack_s \n");
				SetPrimitiveTrigger_Data(ind_ack_s, NULL, 0, comm_NONE);
			}
		}
	}break;

	default: break;

	}
	return t_changeState(t_idle);

}

static inline int _master_tx_complete(void)
{
	return STATE_WAIT;
}

/******************************************************************************************
 * Tx complete trigger will be called from uart DMA callback or transmit over
 ******************************************************************************************/
static int handle_tx_comp_event(void)
{
	sendBreakCmd();
	if(currentState == s_tx)
	{
		return _slave_tx_complete();
	}
	else if(currentState == m_tx)
	{
		return _master_tx_complete();
	}
	return -1;
}

static int handle_rx_event(void)
{
	if(currentState == slave_init)
	{
			return t_changeState(s_rxh);
	}
	else if(currentState == s_rxh)
	{
		if(handle_slave_rxh())
		{
			return t_changeState(s_rxp);
		}
		else
		{
			return STATE_WAIT;
		}
	}
	else if(currentState == s_rxp)
	{
		if(handle_slave_rxp())
		{
			return t_changeState(s_proc);
		}
		else
		{
			return STATE_WAIT;
		}
	}
	else if(currentState == m_tx)
	{
		return t_changeState(m_rx);
	}
	else if(currentState == m_rx) // TODO may be removed this event will not come in PacketBuffer mode
	{
		if(handle_master_rx())
		{
			return t_changeState(m_proc);
		}
		else
		{
			return STATE_WAIT;
		}
	}
	else if (currentState == t_idle)
	{

		return t_changeState(s_rxh);
	}
	else if (currentState == t_hclose)
	{
		wakeupWireless(1);
		return t_changeState(s_rxh);
	}
	else if (currentState == s_tx)
	{
		if(bufferxfer_context.max_segments!=0 && (bufferxfer_context.current_segment < bufferxfer_context.max_segments))
		{
			return t_changeState(s_rxh);
		}
		else
		{
			return -1; // TODO handle error.
		}
	}
	else
	{
		return -1; // invalid use case.
	}
	return -1;
}

static int handle_peer_close_event(void)
{

#ifdef  TRANSPORT_FAIL
	switch(currentState)
	{
	case m_tx:
		Comm_Sleep_Set();
		return 0;
	break;

	case t_idle:
	case t_timeouts:
	case t_hclose:
	case slave_init:
	case s_rxh:
	case s_rxp:
	case s_proc:
	case m_rx:
	case m_proc:
		return t_changeState(t_close);
		break;

	default: return -1;

	}

#else
	if((currentState == t_hclose) || (currentState == t_idle) || (currentState == t_timeouts))
	{
		return t_changeState(t_close);
	}
	else if (currentState == m_tx)
	{
		return 0;
	}
	return -1;
#endif
}

static int handle_close_event(void)
{
	if(currentState == t_idle || currentState == m_tx || currentState == m_rx)
	{
		return t_changeState(t_hclose);
	}
	return -1;
}

static int handle_ack_event(void)
{
	if(currentState == s_proc)
	{
		ack_status = t_ack_s;
		return t_changeState(s_tx);
	}
	return -1;
}

static int handle_nack_event(void)
{
	if(currentState == s_proc)
	{
		ack_status = t_nack_s;
		return t_changeState(s_tx);
	}
	return -1;
}

static int handle_break_event(void)
{
	if(_platform_break_handle() == STATE_CHANGE)
	{
#ifdef TRANSPORT_FAIL
		if(IsTransportMaster()) // slave received packets with error and reset the port
		{
			enum _ptrigger ptrigger = p_invalid;
			switch(ID)
			{
			case BUFFERTX:
			case INDICATION: ptrigger = ind_transp_f;
			break;
			case RESPONSE:
			case COMMAND:    ptrigger = cr_transp_f;
			break;
			default:break;
			}

			COM_PRINTI("Port closed by slave trigger[%d] PM sent transport failure",ptrigger);
			SetPrimitiveTrigger_Data(ptrigger, NULL, 0, comm_NONE);
		}
#else
		return STATE_WAIT;
#endif
		return t_changeState(t_close);
	}
	return STATE_WAIT;
}

static int handle_timeout_event()
{
	switch(currentState)
	{
	case slave_init:
	case s_rxh:
	case s_rxp:
	case s_proc:
	case s_tx:
	{
		pending_timeout =1;
		return STATE_WAIT;
	}
	case t_idle:
	case master_init:
	case m_tx:
	case m_rx:
	case m_proc:
	{
		return t_changeState(t_timeouts);
	}
	default: return STATE_WAIT;
	}
}


static int handle_invalid_event(void)
{
	return -1;
}

static int handle_close()
{
	COM_PRINTI("In handle_close\n");
	if(prevState == t_timeouts)
	{
		wakeupWireless(0);
	}
	else
	{
		Close_Port();
	}
	resetTransportManager();
	Comm_Sleep_Set();
	return(STATE_WAIT);
}

static int handle_half_close()
{
	COM_PRINTI("In handle_half_close\n");
	hClose_Port();
	return STATE_WAIT;
}

static int handle_timeout()
{
	COM_PRINTI("In handle_timeout state \r\n");
	Reset_Port();
	return STATE_WAIT;

}

static int handle_idle()
{
	COM_PRINTI("In handle_idle\n");

	if(buffer_transfer_flag)
	{
		buffer_transfer_flag =0;
		memset(&bufferxfer_context, 0, sizeof(bufferxfer_context));
	}

	if(pending_timeout)
	{
		pending_timeout = 0;
		return t_changeState(t_timeouts);
	}
	return(STATE_WAIT);
}

/*******************************************************************************************************************************
 * MAster init must wait for a task notify from  GPIO interrupt handler, Especially a St_wakeup trigger.
 * Once St_wakeup Trigger is received, change the state to master_tx.
 * Exit the function.
 ********************************************************************************************************************************/
static int handle_master_init()
{
	COM_PRINTI("In handle_master_init\n");
	Open_Master_tPort();
	return (STATE_WAIT);
}

static int handle_master_tx()
{
	COM_PRINTI("In handle_master_tx\n");
	struct _Exchange* packet = GetTManagerContext();

	if(prevState == master_init) // only tx enabled in master_init
	{
		Open_Master_rPort();
	}

	if(buffer_transfer_flag  || packet->ID == BUFFERTX)
	{
		buffer_transfer_send();
	}
	else
	{
		packet->Header = HEADER_HDR;
		struct footer *pfooter = NULL;
		pfooter = (struct footer*)(packet->Data + packet->size);
		pfooter->crc = get_crc(&packet->ID, (uint32_t)(packet->size + HDR_SIZE - 1));
		pfooter->footer = HEADER_FTR;
		SUB_ID = packet->SUB_ID;
		ID = packet->ID;
		_platform_transmit((uint8_t *)packet,(uint16_t)(packet->size + HDR_SIZE + FTR_SIZE));
	}
	return(STATE_WAIT);
}

static int buffer_transfer_send()
{
	struct _Exchange* packet = GetTManagerContext();
	uint16_t mtu = 0;
	uint32_t size = 0 ;

	COM_PRINTI("In buffer_transfer_send\n");
	if(!buffer_transfer_flag)
	{
		SUB_ID = packet->SUB_ID;
		ID = packet->ID;
		mtu = getMtuSize(SUB_ID);
		// payload addr
		bufferxfer_context.payload 			= (uint8_t*)*((uint32_t *)((&(packet->Header))+6));
		bufferxfer_context.tot_len			= (*((uint16_t *)((&(packet->Header))+10 )));
		bufferxfer_context.max_segments     = (uint8_t)((bufferxfer_context.tot_len % mtu) ? (bufferxfer_context.tot_len/mtu) + 1: (bufferxfer_context.tot_len/mtu));
		bufferxfer_context.mtu				= mtu;
		bufferxfer_context.current_segment	= 0;
		bufferxfer_context.timestamp		= getRequesterTimeStmap();
		bufferxfer_context.crc				= 0;
		bufferxfer_context.requester		= getCommRequester();
		buffer_transfer_flag = 1;
	}
	else
	{
		mtu = getMtuSize(SUB_ID);
	}

	if(mtu < (uint16_t)(bufferxfer_context.tot_len - (uint32_t)(mtu *bufferxfer_context.current_segment )))
	{
		size = mtu;
	}
	else
	{
		size = (uint16_t)(bufferxfer_context.tot_len - (uint32_t)(mtu *bufferxfer_context.current_segment ));
	}

	packet->Header = HEADER_HDR;
	packet->SUB_ID = SUB_ID;
	packet->ID     = ID;
	packet->Data[CUR_SEG] 	  = bufferxfer_context.current_segment;
	packet->Data[MTU_LSB]     = (uint8_t)(bufferxfer_context.mtu);
	packet->Data[MTU_MSB]     = (uint8_t)(bufferxfer_context.mtu >> 8);
	packet->Data[TOTAL_LEN_LSB0]	  = (uint8_t)(bufferxfer_context.tot_len);
	packet->Data[TOTAL_LEN_LSB1] = (uint8_t)(bufferxfer_context.tot_len>>8);
	packet->Data[TOTAL_LEN_MSB] = (uint8_t)(bufferxfer_context.tot_len >>16);
	packet->Data[DATA_ORIGIN] = APP; // TODO Change later.
	bufferxfer_context.crc	  = get_crc(bufferxfer_context.payload, bufferxfer_context.tot_len);
	memcpy(&packet->Data[TIMESTAMP_LSB0], &bufferxfer_context.timestamp, 4);
	memcpy(&packet->Data[CRC_LSB0], &bufferxfer_context.crc, 4);
	memcpy(packet->Data + BUFF_TX_HDR, bufferxfer_context.payload + (bufferxfer_context.current_segment * size), size);
	packet->size = (uint16_t)(size + BUFF_TX_HDR);
	struct footer *pfooter = NULL;
	pfooter = (struct footer*)(packet->Data + packet->size);
	pfooter->crc = get_crc(&packet->ID, (uint32_t)(packet->size + HDR_SIZE - 1));
	pfooter->footer = HEADER_FTR;
	COM_PRINTI("BufferTX Packet (%d) |%x |%x |%x |%x |%x\n",packet->size,packet->Header,packet->ID,packet->SUB_ID,*((unsigned int*)(packet->Data + packet->size)),*((packet->Data + packet->size+4)));

#ifdef DEBUG_PRINTS
	print_header(packet);
	print_payload(packet);
	print_footer(packet);
#endif
	_platform_transmit((uint8_t *)packet,(uint16_t)(packet->size + HDR_SIZE + FTR_SIZE));
	bufferxfer_context.current_segment++;
	return 1;
}

static int handle_master_rx()
{

	int32_t ret = rbuf_get( (uint8_t *)&ipacket , HDR_SIZE + FTR_SIZE);
	COM_PRINTI("In handle_master_rx [%ld]\n",ret);
	if(ret == HDR_SIZE + FTR_SIZE) // checking the com_ACK packet size
	{
		COM_PRINTI("Recv commAck packet comACK %d AsubID %d ACK %d AId %d\n",ipacket.ID,ipacket.SUB_ID,GET_ACK(ipacket),GET_aID(ipacket));
		if(CHECK_COMACK_PACKET(ipacket) && CHECK_ACKFIELD(ipacket) && CHECK_CONTEXT(ipacket))
		{
			COM_PRINTI("Recv commAck packet check ok\n");
			return(STATE_CHANGE);
		}
		else // packet not commAck  master clash
		{
			COM_PRINTE("Recv commAck packet check fail\n");
			resetPrimitiveMaster(ID);
			RESET_STATE_MACHINE();
		}
	}
	return(STATE_WAIT);
}

static int handle_master_proc()
{
	COM_PRINTI("In handle_master_proc aID %d\n",GET_aID(ipacket));
	int32_t ret = STATE_CHANGE;
	enum _ptrigger recv = p_invalid;

	switch(GET_aID(ipacket))
	{
	case COMMAND:
		{
		recv = (GET_ACK(ipacket) == ACK)?cmd_s:cmd_f;
	}break;
	case RESPONSE:
	{
		recv = (GET_ACK(ipacket) == ACK)?rsp_s:rsp_f;
	}break;
	case INDICATION:
		{
		recv = (GET_ACK(ipacket) == ACK)?ind_s:ind_f;
	}break;
	case BUFFERTX:
	{
		if (GET_ACK(ipacket) == NACK)
		{
			recv = bufferxfer_f;
		}
		else if (GET_ACK(ipacket) == ACK)
		{
			COM_PRINTI("Mode BufferXfer [%d]:[%d] \n",bufferxfer_context.current_segment,bufferxfer_context.max_segments);
			if(bufferxfer_context.current_segment < bufferxfer_context.max_segments)
			{
				COM_PRINTI("Looping to m_tx for [%d] \n",bufferxfer_context.current_segment);
				ack_status = t_ack_s;
				t_changeState(m_tx);
				handle_change_state_event();
				ret = STATE_WAIT;
			}
			else
			{
				COM_PRINTI("Received all segments [%d]:[%d] sending <bufferxfer_s> \n",bufferxfer_context.current_segment,bufferxfer_context.max_segments);
				buffer_transfer_flag = 0;
				recv = bufferxfer_s;
				memset(&bufferxfer_context, 0, sizeof(bufferxfer_context));
			}
		}

	}break;

	default: break;

		}


	if(ret == STATE_CHANGE)
		{
		SetPrimitiveTrigger_Data(recv, NULL, 0, comm_NONE);
		}
	return(ret);
		}


static int handle_slave_init()
{
	COM_PRINTI("In handle_slave_init\n");
	Open_Slave_Port();
	return(STATE_WAIT);
}

static int handle_slave_rxh()
{
	int32_t ret = rbuf_get( (uint8_t *)&ipacket ,HDR_SIZE );
	COM_PRINTI("In handle_slave_rxh [%ld]\n",ret);
	if(ret == HDR_SIZE)
	{
		if(CHECK_HDR(ipacket))
		{
			SUB_ID = ipacket.SUB_ID;
			ID = ipacket.ID;
			return(STATE_CHANGE);
		}
		else
		{
#ifdef TRANSPORT_FAIL
			TIMEOUT_STATE_MACHINE();
#endif
		}
	}

	return(STATE_WAIT);
}

static int handle_slave_rxp()
{

	int32_t ret = rbuf_get(ipacket.Data, (uint16_t)(ipacket.size + FTR_SIZE));
	COM_PRINTI("In handle_slave_rxp [%ld]\n",ret);
	if(ret == (ipacket.size + FTR_SIZE)) // full packet received
	{
		if(CHECK_FTR(ipacket) && Check_CommCrc(&ipacket) && (ipacket.ID <= BUFFERTX))
		{
			return STATE_CHANGE;
		}
		else
		{
#ifdef DEBUG_PRINTS
			printf("Footer mismatch in handle_slave_rxp \r\n");
			print_payload(&ipacket);
			print_footer(&ipacket);
//			RingBuf_Debug();
#endif

#ifdef TRANSPORT_FAIL
			TIMEOUT_STATE_MACHINE();
#endif
		}
	}

	return(STATE_WAIT);
}

static int buffer_transfer_recv()
{
	if(ipacket.Data[0] == 0)
	{
		if(bufferxfer_context.max_segments != 0)
		{}
		COM_PRINTI("In packet 0 received, updating bufferxfer context\r\n");
		buffer_transfer_flag = 1;
		bufferxfer_context.current_segment = 0;
		bufferxfer_context.mtu = (uint16_t)((ipacket.Data[MTU_MSB] << 8) | (ipacket.Data[MTU_LSB]));
		bufferxfer_context.tot_len = (uint32_t)((ipacket.Data[TOTAL_LEN_MSB] << 16) | (ipacket.Data[TOTAL_LEN_LSB1] << 8) | ipacket.Data[TOTAL_LEN_LSB0]);
		bufferxfer_context.requester = ipacket.Data[DATA_ORIGIN];
		bufferxfer_context.timestamp = (uint32_t)((ipacket.Data[TIMESTAMP_MSB1] << 24) | (ipacket.Data[TIMESTAMP_MSB0] << 16) | (ipacket.Data[TIMESTAMP_LSB1] << 8) | ipacket.Data[TIMESTAMP_LSB0]);
		bufferxfer_context.crc = (uint32_t)((ipacket.Data[CRC_MSB1] << 24) | (ipacket.Data[CRC_MSB0] << 16) | (ipacket.Data[CRC_LSB1] << 8) | ipacket.Data[CRC_LSB0]);
		if(bufferxfer_context.tot_len % bufferxfer_context.mtu)
		{
			bufferxfer_context.max_segments = (uint8_t)((bufferxfer_context.tot_len / bufferxfer_context.mtu)+1);
		}
		else
		{
			bufferxfer_context.max_segments = (uint8_t)(bufferxfer_context.tot_len / bufferxfer_context.mtu);
		}
		COM_PRINTI("bufferxfer_context.current_segment %d,bufferxfer_context.max_segments %d,bufferxfer_context.mtu %d bufferxfer_context.tot_len:%ld Requester:%d timestamp:%ld crc %ld\r\n",
				bufferxfer_context.current_segment,
				bufferxfer_context.max_segments,
				bufferxfer_context.mtu,
				bufferxfer_context.tot_len,
				bufferxfer_context.requester,
				bufferxfer_context.timestamp,
				bufferxfer_context.crc);
	}
	else
	{
		bufferxfer_context.current_segment++;
	}
	if( bufferxfer_context.current_segment == ipacket.Data[0])
	{
		COM_PRINTI("Sending bufferxfer_rx event for seg number [%d]\r\n", ipacket.Data[0]);
#ifdef TRANSPORT_TESTING
		COM_PRINTI("Transport Testing sending ack \r\n");
		ack_status = t_ack_s;
		Refresh_Port();
		return(STATE_CHANGE);
#endif
	}
	else
	{
		COM_PRINTI("Current Segment mismatch, received seg num = [%d]\r\n", ipacket.Data[0]);
	}
	return 1;
}
static int handle_slave_proc()
{
	COM_PRINTI("In handle_slave_proc\n");

	enum _ptrigger recv = p_invalid;
	uint8_t *payload 	= NULL;
	uint16_t len 		=0;

	switch (ipacket.ID)
	{
	case COMMAND:
		recv = cmd_r;
		payload = NULL;
		len 	= 0;
		break;
	case INDICATION:  //slave
		recv = ind_r;
		if(ipacket.size)
		{
			payload = ipacket.Data;
			len 	= ipacket.size;
		}
		break;
	case RESPONSE:
		recv = resp_r;
		if(ipacket.size)
		{
			payload = ipacket.Data;
			len 	= ipacket.size;
		}
		break;

	case BUFFERTX:
	{
		COM_PRINTI("In BUFFERTx\r\n");
		recv  = bufferxfer_rx;
		if(ipacket.size)
		{
			payload = ipacket.Data;


			len 	= ipacket.size;
				}

		buffer_transfer_recv();
			}
				// HAndle error.
	break;


	default: COM_PRINTI("In handle_slave_proc unknow ID %d\n ",ipacket.ID);






	break;
	}

	SetPrimitiveTrigger_Data(recv, payload, len, ipacket.SUB_ID);
	return(STATE_WAIT);
}


static int handle_slave_tx()
{
	COM_PRINTI("In handle_slave_tx\n");
	
	
	/*
	if(SUB_ID == 42)
		return STATE_WAIT;
		
	*/
	
	uint8_t _ack = 0;
	if(ack_status == t_ack_s || ack_status == t_buffxfer_cont)
	{
		_ack = ACK;
	}
	else
	{
		_ack = NACK;
	}
	struct _Exchange* packet = GetTManagerContext();
	packet->Header = HEADER_HDR;
	packet->ID	   = comACK;
	packet->SUB_ID = SUB_ID;
	packet->size   = (uint16_t)((ID << 8) | (_ack));
	struct footer* pfooter = (struct footer*)packet->Data;
	pfooter->crc = get_crc(&packet->ID, (uint32_t)(packet->size + HDR_SIZE - 1));
	pfooter->footer = HEADER_FTR;
	_platform_transmit((uint8_t *)packet,(uint16_t)HDR_SIZE + FTR_SIZE);
	return STATE_WAIT;
}


static enum t_states t_nextState()
{
	switch(currentState)
	{
#if 1
	case master_init: return m_tx; //TODO remove this. Added for testing
	break;
#endif
	case m_tx: return m_rx;
	break;
	case m_rx: return m_proc;
	break;
	case m_proc:return t_idle;
	break;
	case s_rxh: return s_rxp;
	break;
	case s_tx: return t_idle; // TODO only for debugging.
	break;
	case s_rxp: return  s_proc;
	break;
	case s_proc: return  s_tx;
	break;
	case slave_init: return s_rxh;
	break;
	case t_invalidstate: return t_close;
	break;
	case t_idle: return t_close;
	break;
	default: return t_invalidstate;

	}
}

static int t_changeState(enum t_states state)
{
	prevState = currentState;
	currentState = state;
	return STATE_CHANGE;
}

static int32_t Check_CommCrc(struct _Exchange *packet)
{
	if(packet->size == 0)
	{
		return 1;
	}
	else
	{
		uint32_t calc_crc = (uint32_t)(packet->Data[packet->size] | packet->Data[packet->size+1]<<8 | packet->Data[packet->size+2]<<16 | packet->Data[packet->size+3]<<24);
		return (get_crc(&packet->ID, (uint32_t)(packet->size + HDR_SIZE -1)) == calc_crc);
	}
	return 0;
}

enum _ttrigger GetTransportTrigger()
{
	tt_trigger trigger;
	trigger.trigger = t_invalid;
	dequeueTrigger(&trigger);
	return trigger.trigger;
}

void SetTransportTrigger(enum _ttrigger _trigger)
{
	tt_trigger trigger;
	COM_PRINTI("In SetTransportTrigger trigget %d\n",_trigger);
	trigger.trigger = _trigger;
	enqueueTrigger(&trigger);
}

uint32_t TransportMain(enum _ttrigger _trigger)
{
	int32_t ret;
	Trigger = _trigger;

	do
	{
		COM_PRINTI("Transport Main trigger %d \n",_trigger);

		if(Trigger != tchange_state)
		{
			currentEvent = Trigger;
		}
		ret = ttrigger_function[Trigger]();
		if(ret == STATE_CHANGE)
		{
			COM_PRINTI("TM State change from %d to  %d\n",prevState,currentState);
			Trigger = tchange_state;
		}
	}while(ret == STATE_CHANGE);

	Trigger = t_invalid;
	COM_PRINTI("Transport Main exit\n");
	return 0;
}

int GetTransportManagerState(void)
{
	if(currentState == t_idle || currentState == t_close)
	{
		return 0;
	}
	else
	{
		return 1;
	}
}

int isComClose(void)
{
	if(currentState == t_close)
	{
		return 1;
	}
	return 0;
}

int IsTransportMaster()
{
	if(currentState == m_tx)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}

struct _Exchange* GetTManagerContext(void)
{
	return &opacket;
}

int32_t resetTransportManager(void)
{
	SUB_ID = comm_NONE;
	ID = 0;

	Trigger 	 = t_invalid;
	currentState = t_close;
	ack_status   = 0;
	buffer_transfer_flag = 0;

	memset(&opacket, 0, sizeof(opacket));
	memset(&ipacket, 0, sizeof(ipacket));
	memset(&bufferxfer_context, 0, sizeof(bufferxfer_context));
	return 1;
}

struct Buffer_Xfer* GetBufferXferContext()
{
	return &bufferxfer_context;
}
