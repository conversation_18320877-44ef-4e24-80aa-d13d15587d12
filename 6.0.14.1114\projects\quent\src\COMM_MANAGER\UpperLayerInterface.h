#define Execute_Indication_API 		BLE_ExecuteIndication
#define Execute_Command_API    		BLE_ExecuteCommand
#define Execute_Response_API   		BLE_ExecuteResponse
#define Execute_Bufferxfer_API 		BLE_ExecuteBufferxfer
#define GetUpperLayerBuffer()    ((void*)0)
#define UpdateUpperLayerBuffer   updateGattBuffer
#define  NotifycrMsgFail()
#define  NotifyiMsgFail()
#define  NotifycrTrFail()
#define  NotifyiTrFail()


enum exec_req
{
	UL_S,
	UL_F,
	UL_C,
	UL_BUSY,
	UL_LBATT,
};

int32_t updateGattBuffer(uint8_t *data, uint16_t length);

uint32_t CmdIsServicable(enum comm_subid subid);
uint32_t IndIsServicable(enum comm_subid subid);

void setRequesterTimeStmap(uint32_t timestamp);
uint32_t getRequesterTimeStmap(void);
vitals_request_origin_t getCommRequester(void);

void MakePacket(struct _Exchange *packet, uint8_t *data, uint16_t len);

enum exec_req BLE_ExecuteCommand(enum comm_subid sub_id);
enum exec_req BLE_ExecuteIndication(enum comm_subid sub_id, uint8_t* data, uint16_t len);
enum exec_req BLE_ExecuteResponse(enum comm_subid sub_id, uint8_t* data, uint16_t len);
enum exec_req BLE_ExecuteBufferxfer(enum comm_subid sub_id);
void _platform_upd_requester(struct _Exchange *resp, int32_t cr_exec_status);