/*
 * AwtQueue.c
 *
 *  Created on: 05-Oct-2023
 *      Author: admin
 */

// C program for array implementation of queue

#include<limits.h>
#include"Awt_types.h"
#include"PrimitiveQueue.h"
#include"Comm_Debug.h"

// A structure to represent a queue
struct Queue {
    int front, rear, size;
    int capacity;
    pe_trigger _qtrigger[10];
};

// function to create a queue
// of given capacity.
// It initializes size of queue as 0
static struct Queue ptrigger_queueCR;
static struct Queue ptrigger_queueInd;

static pe_trigger iTriggerFront       = {comm_NONE, p_invalid, 0, 0, 0};    // QueueFront
static pe_trigger crTriggerFront      = {comm_NONE, p_invalid, 0, 0, 0};     // QueueFront

void InitQueue(void)
{
	ptrigger_queueCR.capacity = 4;
	ptrigger_queueCR.front = ptrigger_queueCR.size = 0;
	// This is important, see the enqueue
	ptrigger_queueCR.rear = ptrigger_queueCR.capacity - 1;
	memset(&ptrigger_queueCR._qtrigger, p_invalid, sizeof(ptrigger_queueCR._qtrigger));

	ptrigger_queueInd.capacity = 4;
	ptrigger_queueInd.front = ptrigger_queueInd.size = 0;
	// This is important, see the enqueue
	ptrigger_queueInd.rear = ptrigger_queueInd.capacity - 1;
	memset(&ptrigger_queueInd._qtrigger, p_invalid, sizeof(ptrigger_queueInd._qtrigger));
}

// Queue is full when size becomes
// equal to the capacity

int enqueueCmdTxToFront(pe_trigger* item)
{
	if(crTriggerFront.trigger == p_invalid)
	{
		memcpy(&crTriggerFront,item,sizeof(pe_trigger));
		return 1;
	}
	return 0;
}

int enqueueIndTxToFront(pe_trigger* item)
{
	if(iTriggerFront.trigger == p_invalid)
	{
		memcpy(&iTriggerFront,item,sizeof(pe_trigger));
		return 1;
	}
	return 0;
}


int isCmdTxFull(void)
{
    return (ptrigger_queueCR.size == ptrigger_queueCR.capacity);
}

int isIndTxFull(void)
{
    return (ptrigger_queueInd.size == ptrigger_queueInd.capacity);
}

int isCmdTxEmpty(void)
{
    return (ptrigger_queueCR.size == 0 && crTriggerFront.trigger == p_invalid);
}

int isIndTxEmpty(void)
{
    return (ptrigger_queueInd.size == 0 && iTriggerFront.trigger == p_invalid);
}

// Function to add an item to the queue.
// It changes rear and size
int enqueueCmdTx(pe_trigger* item)
{
    if (isCmdTxFull())
    {
        return 0;
    }
    ptrigger_queueCR.rear = (ptrigger_queueCR.rear + 1) % ptrigger_queueCR.capacity;
    memcpy(&ptrigger_queueCR._qtrigger[ptrigger_queueCR.rear],item,sizeof(pe_trigger));
    ptrigger_queueCR.size = ptrigger_queueCR.size + 1;
    return 1;
}

int enqueueIndTx(pe_trigger* item)
{
    if (isIndTxFull())
    {
        return 0;
    }
    ptrigger_queueInd.rear = (ptrigger_queueInd.rear + 1) % ptrigger_queueInd.capacity;
    memcpy(&ptrigger_queueInd._qtrigger[ptrigger_queueInd.rear],item,sizeof(pe_trigger));
    ptrigger_queueInd.size = ptrigger_queueInd.size + 1;
    return 1;
}

// Function to remove an item from queue.
// It changes front and size
int dequeueCmdTx(pe_trigger* item)
{
	if(crTriggerFront.trigger != p_invalid)
	{
		memcpy(item,&crTriggerFront,sizeof(pe_trigger));
		crTriggerFront.trigger = p_invalid;
		return 1;
	}

	if(isCmdTxEmpty())
	{
		return 0;
	}

	memcpy(item,&ptrigger_queueCR._qtrigger[ptrigger_queueCR.front],sizeof(pe_trigger));
	ptrigger_queueCR.front = (ptrigger_queueCR.front + 1) % ptrigger_queueCR.capacity;
	ptrigger_queueCR.size = ptrigger_queueCR.size - 1;
	return 1;
}

int dequeueIndTx(pe_trigger* item)
{
	if(iTriggerFront.trigger != p_invalid)
	{
		memcpy(item,&iTriggerFront,sizeof(pe_trigger));
		iTriggerFront.trigger = p_invalid;

		return 1;
	}

	if(isIndTxEmpty())
	{
		return 0;;
	}

	memcpy(item,&ptrigger_queueInd._qtrigger[ptrigger_queueInd.front],sizeof(pe_trigger));
	ptrigger_queueInd.front = (ptrigger_queueInd.front + 1) % ptrigger_queueInd.capacity;
	ptrigger_queueInd.size = ptrigger_queueInd.size - 1;
	return 1;
}
