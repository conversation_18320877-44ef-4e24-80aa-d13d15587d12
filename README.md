# QUENT_BLE
This repository is used for development of BLE firmware on DA14531 SoC.
Keil uVision5 IDE is used for development of firmware.

# Functionality
To receive/request features implemented at the watch over the GATT Server in communication with STM32 SoC.
Features such as:
1. Vitals
2. Pedometer features
3. Battery Percentage
4. Alerts
5. Emergency Services : SOS
6. User info Read/Write
7. OTA
