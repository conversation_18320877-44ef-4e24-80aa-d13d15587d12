LICENSES

NAME                    LICENSE FILE PATH                                        LICENSE TYPE    COPYRIGHT                       COMMENTS
------------------------------------------------------------------------------------------------------------------------------------------------------------
SDK core and                                                                     Dialog          Dialog Semiconductor            Licensed under the relevant Dialog SLA
applications                                                                     proprietary
------------------------------------------------------------------------------------------------------------------------------------------------------------
BLE stack                                                                        Third Party     Third Party                     Licensed under the relevant Dialog SLA
                                                                                 proprietary
------------------------------------------------------------------------------------------------------------------------------------------------------------
micro-ecc               third_party\micro_ecc\micro_ecc_license.txt              BSD-2           Kenneth MacKay                  Contained in file headers
                                                                                                                                 where applicable   
------------------------------------------------------------------------------------------------------------------------------------------------------------
AES implementation      sdk\platform\core_modules\crypto\sw_aes_license.txt      Revised BSD     Cameron Rich                    Contained in file headers
                                                                                                                                 where applicable
------------------------------------------------------------------------------------------------------------------------------------------------------------
getopt                  utilities\prod_test\prod_test_cmds\getopt_license.txt    BSD             Microsoft Corporation,          Contained in file headers
                                                                                                 The Regents of the University   where applicable
                                                                                                 of California
------------------------------------------------------------------------------------------------------------------------------------------------------------
crc32                   third_party\crc32\crc32_license.txt                      unrestricted use                                Contained in file headers
                                                                                                                                 where applicable
------------------------------------------------------------------------------------------------------------------------------------------------------------
ARM                     sdk\platform\arm_license.txt                             Revised BSD     ARM LIMITED                     Contained in file headers
                                                                                                                                 where applicable
------------------------------------------------------------------------------------------------------------------------------------------------------------
iRNG                                                                             Third Party     Third Party                     Licensed under the relevant Dialog SLA
                                                                                 proprietary
------------------------------------------------------------------------------------------------------------------------------------------------------------
chacha20                third_party\rand\chacha20_license.txt                    CC0 1.0         (Github user name): Emill       Contained in file headers
                                                                                 Universal                                       where applicable
------------------------------------------------------------------------------------------------------------------------------------------------------------

This software includes both proprietary and open source components. The proprietary components may only be used after execution of the
relevant Dialog Semiconductor Licensing Agreement. If you have not executed such an agreement you are not authorised to use this software.
All open source components constitute a separate delivery and are licensed according to the applicable terms of their respective open source
licenses. All open source software is delivered "AS IS" with no warranties. While Dialog Semiconductor seeks to provide complete and
accurate licensing information with regard to each open source package, Dialog Semiconductor does not warrant or represent that the
licensing information is complete or error-free. Certain open source licenses may  require that source code is made available under certain conditions. 
<NAME_EMAIL> for any source code requests, as well as for any licensing inquiries with respect to the proprietary components.

Release Version: DA14585/586/531 v6.0.14.1114 SDK

Approved by Legal: Yes
