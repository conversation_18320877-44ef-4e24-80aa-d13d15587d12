Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    system_da14531.o(.text) refers to system_da14531.o(.data) for .data
    system_da14531.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    startup_da14531.o(RESET) refers to startup_da14531.o(STACK) for __initial_sp
    startup_da14531.o(RESET) refers to startup_da14531.o(.text) for Reset_Handler
    startup_da14531.o(RESET) refers to rwble.o(.text) for BLE_WAKEUP_LP_Handler
    startup_da14531.o(RESET) refers to uart.o(.text) for UART2_Handler
    startup_da14531.o(RESET) refers to adc_531.o(.text) for ADC_Handler
    startup_da14531.o(RESET) refers to gpio.o(.text) for GPIO0_Handler
    startup_da14531.o(.text) refers to system_da14531.o(.text) for SystemInit
    startup_da14531.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    startup_da14531.o(.text) refers to nmi_handler.o(.text) for NMI_HandlerC
    startup_da14531.o(.text) refers to hardfault_handler.o(.text) for HardFault_HandlerC
    nvds.o(.constdata) refers to nvds.o(.constdata) for nvds_data_storage
    nvds.o(.constdata) refers to arch_system.o(retention_mem_area0) for dev_bdaddr
    arch_main.o(.text) refers to arch_system.o(.text) for system_init
    arch_main.o(.text) refers to serialinterface.o(.text) for EnableRFSwitch
    arch_main.o(.text) refers to rwip.o(.text) for rwip_sleep
    arch_main.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_main.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_main.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_main.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_clear_dcdc_reserved
    arch_main.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_set_level
    arch_main.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_main.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_main.o(.text) refers to arch_main.o(retention_mem_area0) for retention_mem_area0
    arch_main.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    jump_table.o(.constdata) refers to ble_arp.o(.text) for rf_init_func
    jump_table.o(.constdata) refers to uart.o(.text) for UART_Handler_SDK_func
    jump_table.o(.constdata) refers to jump_table.o(.text) for platform_reset_func
    jump_table.o(.constdata) refers to rwble.o(.text) for lld_sleep_compensate_func
    jump_table.o(.constdata) refers to arch_system.o(.text) for lld_sleep_init_func
    jump_table.o(.constdata) refers to patch.o(.text) for JT_lld_test_mode_rx_func
    jump_table.o(.constdata) refers to prf.o(.text) for prf_init_func
    jump_table.o(.constdata) refers to jump_table.o(heap_mem_area_not_ret) for rwip_heap_non_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_env_area) for rwip_heap_env_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_db_area) for rwip_heap_db_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_msg_area) for rwip_heap_msg_ret
    jump_table.o(.constdata) refers to nvds.o(.constdata) for rom_nvds_cfg
    jump_table.o(.constdata) refers to custs1.o(.constdata) for rom_cust_prf_cfg
    jump_table.o(.constdata) refers to prf.o(.constdata) for rom_prf_cfg
    jump_table.o(.constdata) refers to app_entry_point.o(.constdata) for rom_app_task_cfg
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_set_extended_sleep
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_system.o(.text) refers to arch_system.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    arch_system.o(.text) refers to otp_cs.o(.text) for otp_cs_get_xtal_wait_trim
    arch_system.o(.text) refers to adc_531.o(.text) for adc_init
    arch_system.o(.text) refers to ble_arp.o(.text) for rf_recalibration
    arch_system.o(.text) refers to gpio.o(.text) for GPIO_init
    arch_system.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_system.o(.text) refers to otp_hdr.o(.text) for otp_hdr_get_bd_address
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.bss) for .bss
    arch_system.o(.text) refers to arch_system.o(.constdata) for .constdata
    arch_system.o(.text) refers to arch_rom.o(.text) for arch_rom_init
    arch_system.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_system.o(.text) refers to trng.o(.text) for init_rand_seed_from_trng
    arch_system.o(.text) refers to arch_sleep.o(.text) for arch_disable_sleep
    arch_system.o(.text) refers to app.o(.text) for app_init
    arch_system.o(.text) refers to comm_task.o(.text) for comm_init
    arch_system.o(.text) refers to user_peripheral.o(.text) for user_app_init
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.data) for .data
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_push
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_system.o(.text) for set_xtal32m_trim_value
    arch_hibernation.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to arch_system.o(retention_mem_area0) for last_temp
    arch_rom.o(.text) refers to patch.o(.text) for patch_global_vars_init
    arch_rom.o(.text) refers to trng.o(trng_state) for trng_state_val
    arch_rom.o(.text) refers to arch_rom.o(.constdata) for .constdata
    arch_rom.o(.text) refers to arch_rom.o(retention_mem_area0) for retention_mem_area0
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_func_addr_table_var
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_cfg_table_var
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(otp_cs_booter) for otp_cs_booter
    otp_cs.o(.text) refers to otp_cs.o(.bss) for .bss
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.bss) for .bss
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.text) for dcdc_cfg
    gpio.o(.text) refers to gpio.o(retention_mem_area0) for retention_mem_area0
    gpio.o(.text) refers to gpio.o(.text) for gpioshift16
    hw_otpc_531.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_get_level
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(retention_mem_area0) for retention_mem_area0
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.constdata) for .constdata
    hw_otpc_531.o(.text) refers to syscntl.o(.bss) for syscntl_dcdc_state
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_prog
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_read_verif
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_get
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_read_byte
    uart.o(.text) refers to uart.o(.text) for uart_read_buffer
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_DeInit
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_Init
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_enable_flow_control
    trng.o(.text) refers to trng.o(trng_state) for trng_state
    trng.o(.text) refers to arch_system.o(.bss) for otp_hdr_timestamp
    trng.o(.text) refers to trng.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_25_cal
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(.text) for adc_input_shift_enable
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for adc_start
    adc_531.o(.text) refers to adc_531.o(.text) for adc_init
    adc_531.o(.text) refers to adc_531.o(.text) for adc_get_sample
    adc_531.o(.text) refers to adc_531.o(.text) for adc_correction_apply
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_ge
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_offset
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_ge
    rwble.o(.text) refers to arch_system.o(.text) for lld_sleep_lpcycles_2_us_sel_func
    rwble.o(.text) refers to syscntl.o(.text) for syscntl_use_highest_amba_clocks
    rwble.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    rwble.o(.text) refers to rf_531.o(.text) for rf_adplldig_deactivate
    rwble.o(.text) refers to rwble.o(retention_mem_area0) for retention_mem_area0
    rwble.o(.text) refers to rwip.o(retention_mem_area0) for slp_period_retained
    rwble.o(.text) refers to arch_system.o(retention_mem_area0) for clk_freq_trim_reg_value
    rwip.o(.text) refers to arch_sleep.o(.text) for arch_ble_ext_wakeup_get
    rwip.o(.text) refers to arch_system.o(.text) for set_sleep_delay
    rwip.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    rwip.o(.text) refers to rwip.o(retention_mem_area0) for retention_mem_area0
    rwip.o(.text) refers to arch_system.o(retention_mem_area0) for xtal_wait_trim
    rwip.o(.text) refers to arch_system.o(.bss) for twirq_reset_value
    rwip.o(.text) refers to rwip.o(.bss) for .bss
    rwip.o(.text) refers to rwble.o(retention_mem_area0) for ble_finetim_corr
    ble_arp.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    ble_arp.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    ble_arp.o(.text) refers to rf_531.o(.text) for rf_power_up
    ble_arp.o(.text) refers to otp_cs.o(.text) for otp_cs_load_pd_rad
    ble_arp.o(.text) refers to ble_arp.o(.constdata) for .constdata
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(.data) for .data
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.text) for en_adpll_tx
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services_size
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    custs1.o(.constdata) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.text) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.constdata) refers to custs1_task.o(.text) for gattc_write_req_ind_handler
    custs1_task.o(.constdata) refers to custs1_task.o(.constdata) for custs1_default_state
    prf.o(.text) refers to prf.o(retention_mem_area0) for retention_mem_area0
    prf.o(.text) refers to custs1_task.o(.constdata) for custs1_default_handler
    prf.o(.constdata) refers to prf.o(.constdata) for prf_if
    prf.o(.constdata) refers to prf.o(retention_mem_area0) for prf_env
    app_default_handlers.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app_default_handlers.o(.text) refers to app_diss.o(.text) for app_dis_init
    app_default_handlers.o(.text) refers to arch_sleep.o(.text) for arch_set_sleep_mode
    app_default_handlers.o(.text) refers to app.o(.text) for app_prf_enable
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    app_default_handlers.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_offset
    app_default_handlers.o(.text) refers to hash.o(.text) for hash
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_send_pairing_rsp
    app_default_handlers.o(.text) refers to app_security.o(.text) for app_sec_gen_ltk
    app_default_handlers.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_tk_exch
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_add_entry
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.constdata) refers to user_peripheral.o(.text) for user_app_adv_start
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app_default_handlers.o(.text) for default_app_generate_unique_static_random_addr
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_advertise_stop_handler
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_param_update_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_dev_config_create_msg
    app.o(.constdata) refers to app_diss.o(.text) for app_diss_create_db
    app.o(.constdata) refers to app_task.o(retention_mem_area0) for app_state
    app.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_task.o(.text) refers to app.o(.text) for app_easy_gap_dev_configure
    app_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to strlen.o(.text) for strlen
    app_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_task.o(.text) refers to user_custs1_impl.o(text) for device_config
    app_task.o(.text) refers to app_task.o(.constdata) for .constdata
    app_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.constdata) refers to app_task.o(.text) for gapm_device_ready_ind_handler
    app_security.o(.text) refers to app_utils.o(.text) for app_fill_random_byte_array
    app_security.o(.text) refers to app_security.o(retention_mem_area0) for retention_mem_area0
    app_security_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_pairing_request
    app_security_task.o(.text) refers to user_peripheral.o(.text) for user_app_on_tk_exch
    app_security_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_security_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_security_task.o(.text) refers to app_security_task.o(.constdata) for .constdata
    app_security_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_security_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_security_task.o(.constdata) refers to app_security_task.o(.text) for gapc_bond_req_ind_handler
    app_diss_task.o(.text) refers to user_custs1_impl.o(.text) for Get_Software_Revision
    app_diss_task.o(.text) refers to printf1.o(i.__0sprintf$1) for __2sprintf
    app_diss_task.o(.text) refers to strlen.o(.text) for strlen
    app_diss_task.o(.text) refers to app_diss_task.o(.constdata) for .constdata
    app_diss_task.o(.constdata) refers to app_diss_task.o(.text) for diss_value_req_ind_handler
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_entry_point.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_entry_point.o(.constdata) refers to app_task.o(.text) for app_gap_process_handler
    app_entry_point.o(.constdata) refers to app_easy_timer.o(.text) for app_timer_api_process_handler
    app_entry_point.o(.constdata) refers to app_security_task.o(.text) for app_sec_process_handler
    app_entry_point.o(.constdata) refers to app_diss_task.o(.text) for app_diss_process_handler
    app_entry_point.o(.constdata) refers to app_customs_task.o(.text) for app_custs1_process_handler
    app_entry_point.o(.constdata) refers to app.o(.text) for app_easy_gap_dev_configure
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for user_app_callbacks
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for app_process_handlers
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_env
    app_entry_point.o(.constdata) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_prf_srv_perm
    app_msg_utils.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_msg_utils.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_modify
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_set
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_free_callback
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_security.o(.text) for app_sec_gen_csrk
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.constdata) for .constdata
    app_easy_security.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for pairing_rsp_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for tk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for csrk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for encrypt_cfm_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_request_get_active
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_ral_op
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_timer.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_easy_timer.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_customs.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for GetBondInfo
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to uart.o(.text) for uart_send
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    user_custs_config.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    user_custs_config.o(.constdata) refers to app_customs.o(.text) for app_custs1_create_db
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_svc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Ecg_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_char
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ECG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_cfg
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for PPG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_user_desc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.conststring) for .conststring
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Vital_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for VITAL_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Alert_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ALERT_STATUS_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for User_Info_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for USR_INFO_UUID_128
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_turn_on_in_boost
    user_periph_setup.o(.text) refers to patch.o(.text) for patch_func
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_initialize
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_register_tx_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to user_periph_setup.o(.text) for uart_send_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_custs1_impl.o(.text) refers to comm_manager.o(.text) for Com_Send_Data
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(text) for text
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(free_area) for free_area
    user_custs1_impl.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.constdata) for .constdata
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.text) for ResetCommLock_cb
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    user_peripheral.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    user_peripheral.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_peripheral.o(.text) refers to app.o(.text) for app_easy_gap_param_update_start
    user_peripheral.o(.text) refers to user_peripheral.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_peripheral.o(retention_mem_area0) for retention_mem_area0
    user_peripheral.o(.text) refers to user_peripheral.o(.data) for .data
    user_peripheral.o(.text) refers to gpio.o(.text) for GPIO_EnableIRQ
    user_peripheral.o(.text) refers to app_default_handlers.o(.text) for default_app_on_init
    user_peripheral.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_init
    user_peripheral.o(.text) refers to app_security.o(.text) for app_sec_gen_tk
    user_peripheral.o(.text) refers to serialinterface.o(.text) for uart_trigger
    user_peripheral.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    user_peripheral.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_peripheral.o(.text) refers to user_custs1_impl.o(.bss) for Comm_Lock
    user_peripheral.o(.text) refers to user_peripheral.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    scheduler.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.text) for SchedulerCallback
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.bss) for .bss
    comm_task.o(.text) refers to primitivequeue.o(.text) for InitQueue
    comm_task.o(.text) refers to transportqueue.o(.text) for InitTriggerQueue
    comm_task.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_task.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_task.o(.text) refers to comm_manager.o(.text) for RunMainLoop
    comm_task.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    comm_task.o(.text) refers to comm_task.o(.constdata) for .constdata
    comm_task.o(.text) refers to comm_task.o(retention_mem_area0) for retention_mem_area0
    comm_task.o(.text) refers to comm_task.o(.bss) for .bss
    comm_task.o(.constdata) refers to comm_task.o(.text) for comm_manager
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_default_state
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_handler
    comm_task.o(.constdata) refers to comm_task.o(retention_mem_area0) for comm_state
    serialinterface.o(.text) refers to uart.o(.text) for uart_send
    serialinterface.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    serialinterface.o(.text) refers to gpio.o(.text) for GPIO_GetPinStatus
    serialinterface.o(.text) refers to comm_manager.o(.text) for COM_BreakEvent
    serialinterface.o(.text) refers to serialinterface.o(.data) for .data
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_OPEN_PORT_INIT
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_CLOSE_PORT_INIT
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_error_cb
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_send_cb
    upperlayerinterface.o(.text) refers to user_peripheral.o(.text) for updateMacIDPayload
    upperlayerinterface.o(.text) refers to user_peripheral.o(.text) for sendOverBle
    upperlayerinterface.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    upperlayerinterface.o(.text) refers to upperlayerinterface.o(.bss) for .bss
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_manager.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_manager.o(.text) refers to serialinterface.o(.text) for Close_Port
    comm_manager.o(.text) refers to comm_manager.o(.constdata) for .constdata
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    primitivemanager.o(.text) refers to transportmanager.o(.text) for SetTransportTrigger
    primitivemanager.o(.text) refers to comm_task.o(.text) for stopCRTimer
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.constdata) for .constdata
    primitivemanager.o(.text) refers to primitivequeue.o(.text) for isCmdTxEmpty
    primitivemanager.o(.text) refers to comm_manager.o(.text) for WriteCmd
    primitivemanager.o(.text) refers to upperlayerinterface.o(.text) for BLE_ExecuteResponse
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManagerInd
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.constdata) refers to primitivemanager.o(.text) for cmd_tx_event
    primitivequeue.o(.text) refers to primitivequeue.o(.bss) for .bss
    primitivequeue.o(.text) refers to primitivequeue.o(.data) for .data
    transportmanager.o(.text) refers to serialinterface.o(.text) for sendBreakCmd
    transportmanager.o(.text) refers to uart.o(.text) for rbuf_get
    transportmanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveMaster
    transportmanager.o(.text) refers to comm_task.o(.text) for Comm_Sleep_Set
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.text) refers to transportmanager.o(.constdata) for .constdata
    transportmanager.o(.text) refers to transportmanager.o(.data) for .data
    transportmanager.o(.text) refers to upperlayerinterface.o(.text) for getMtuSize
    transportmanager.o(.text) refers to transportqueue.o(.text) for dequeueTrigger
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.constdata) refers to transportmanager.o(.text) for handle_close
    transportqueue.o(.text) refers to transportqueue.o(.bss) for .bss
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to arch_main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to arch_main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz


==============================================================================

Removing Unused input sections from the image.

    Removing system_da14531.o(.text), (16 bytes).
    Removing system_da14531.o(.data), (4 bytes).
    Removing startup_da14531.o(HEAP), (256 bytes).
    Removing arch_main.o(.constdata), (28 bytes).
    Removing jump_table.o(.text), (2 bytes).
    Removing arch_sleep.o(.text), (276 bytes).
    Removing arch_sleep.o(.text), (80 bytes).
    Removing arch_sleep.o(.text), (36 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_system.o(.text), (60 bytes).
    Removing arch_system.o(.text), (56 bytes).
    Removing arch_system.o(.text), (2 bytes).
    Removing arch_system.o(.constdata), (28 bytes).
    Removing arch_system.o(.data), (4 bytes).
    Removing arch_system.o(retention_mem_area0), (1 bytes).
    Removing arch_system.o(retention_mem_area0), (2 bytes).
    Removing arch_system.o(retention_mem_area0), (2 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_hibernation.o(.text), (300 bytes).
    Removing arch_hibernation.o(.text), (336 bytes).
    Removing arch_hibernation.o(.text), (448 bytes).
    Removing arch_hibernation.o(.text), (40 bytes).
    Removing arch_hibernation.o(retention_mem_area0), (8 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing syscntl.o(.text), (36 bytes).
    Removing syscntl.o(.text), (40 bytes).
    Removing syscntl.o(.text), (84 bytes).
    Removing syscntl.o(.text), (44 bytes).
    Removing gpio.o(.text), (24 bytes).
    Removing gpio.o(.text), (24 bytes).
    Removing gpio.o(.text), (32 bytes).
    Removing gpio.o(.text), (144 bytes).
    Removing gpio.o(.text), (36 bytes).
    Removing gpio.o(.text), (40 bytes).
    Removing gpio.o(.text), (12 bytes).
    Removing gpio.o(.text), (16 bytes).
    Removing gpio.o(.text), (16 bytes).
    Removing hw_otpc_531.o(.text), (68 bytes).
    Removing hw_otpc_531.o(.text), (22 bytes).
    Removing hw_otpc_531.o(.text), (52 bytes).
    Removing hw_otpc_531.o(.text), (54 bytes).
    Removing hw_otpc_531.o(.text), (40 bytes).
    Removing hw_otpc_531.o(.text), (52 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (64 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (12 bytes).
    Removing uart.o(.text), (14 bytes).
    Removing uart.o(.text), (28 bytes).
    Removing uart.o(.text), (124 bytes).
    Removing uart.o(.text), (16 bytes).
    Removing uart.o(.text), (44 bytes).
    Removing uart.o(.text), (108 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (108 bytes).
    Removing adc_531.o(.text), (32 bytes).
    Removing adc_531.o(.text), (24 bytes).
    Removing adc_531.o(.text), (16 bytes).
    Removing adc_531.o(.text), (40 bytes).
    Removing adc_531.o(.text), (56 bytes).
    Removing adc_531.o(.text), (44 bytes).
    Removing adc_531.o(.text), (16 bytes).
    Removing adc_531.o(.text), (28 bytes).
    Removing adc_531.o(.text), (220 bytes).
    Removing adc_531.o(.text), (40 bytes).
    Removing adc_531.o(.text), (92 bytes).
    Removing adc_531.o(.text), (116 bytes).
    Removing rwble.o(retention_mem_area0), (1 bytes).
    Removing rwip.o(retention_mem_area0), (4 bytes).
    Removing ble_arp.o(.constdata), (4 bytes).
    Removing rf_531.o(.text), (276 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (404 bytes).
    Removing rf_531.o(.text), (2 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (16 bytes).
    Removing rf_531.o(.text), (32 bytes).
    Removing rf_531.o(.text), (28 bytes).
    Removing rf_531.o(.text), (28 bytes).
    Removing rf_531.o(.text), (80 bytes).
    Removing rf_531.o(.text), (32 bytes).
    Removing rf_531.o(.text), (24 bytes).
    Removing app_default_handlers.o(.text), (8 bytes).
    Removing app_default_handlers.o(.text), (8 bytes).
    Removing app_default_handlers.o(.text), (48 bytes).
    Removing app_default_handlers.o(.text), (68 bytes).
    Removing app_default_handlers.o(.text), (28 bytes).
    Removing app_default_handlers.o(.constdata), (12 bytes).
    Removing app_default_handlers.o(.constdata), (4 bytes).
    Removing app.o(.text), (224 bytes).
    Removing app.o(.text), (96 bytes).
    Removing app.o(.text), (80 bytes).
    Removing app.o(.text), (28 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (32 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (40 bytes).
    Removing app.o(.text), (48 bytes).
    Removing app.o(.text), (28 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (38 bytes).
    Removing app.o(.text), (24 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (32 bytes).
    Removing app.o(.constdata), (12 bytes).
    Removing app.o(.constdata), (74 bytes).
    Removing app.o(.constdata), (140 bytes).
    Removing app_task.o(.constdata), (140 bytes).
    Removing app_security_task.o(.constdata), (140 bytes).
    Removing app_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(.text), (32 bytes).
    Removing app_easy_msg_utils.o(.text), (52 bytes).
    Removing app_easy_msg_utils.o(.text), (36 bytes).
    Removing app_easy_msg_utils.o(.text), (32 bytes).
    Removing app_easy_msg_utils.o(.text), (20 bytes).
    Removing app_easy_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(.text), (56 bytes).
    Removing app_easy_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(retention_mem_area0), (24 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (44 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (52 bytes).
    Removing app_easy_security.o(.text), (28 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (80 bytes).
    Removing app_easy_security.o(.text), (88 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (2 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (46 bytes).
    Removing app_easy_security.o(.constdata), (32 bytes).
    Removing app_easy_timer.o(.text), (44 bytes).
    Removing app_customs.o(.text), (2 bytes).
    Removing app_customs_common.o(.text), (4 bytes).
    Removing app_bond_db.o(.text), (4 bytes).
    Removing app_bond_db.o(.text), (228 bytes).
    Removing app_bond_db.o(.text), (36 bytes).
    Removing app_bond_db.o(.text), (56 bytes).
    Removing app_bond_db.o(.text), (72 bytes).
    Removing app_bond_db.o(.text), (60 bytes).
    Removing app_utils.o(.text), (20 bytes).
    Removing app_easy_whitelist.o(.text), (68 bytes).
    Removing user_custs1_def.o(.constdata), (2 bytes).
    Removing user_periph_setup.o(.text), (64 bytes).
    Removing user_periph_setup.o(.text), (56 bytes).
    Removing user_periph_setup.o(.text), (2 bytes).
    Removing user_periph_setup.o(.text), (88 bytes).
    Removing user_periph_setup.o(.text), (24 bytes).
    Removing user_periph_setup.o(.data), (24 bytes).
    Removing user_custs1_impl.o(.text), (12 bytes).
    Removing user_custs1_impl.o(.text), (68 bytes).
    Removing user_custs1_impl.o(.text), (12 bytes).
    Removing user_custs1_impl.o(.text), (68 bytes).
    Removing user_custs1_impl.o(.text), (76 bytes).
    Removing user_custs1_impl.o(.text), (20 bytes).
    Removing user_custs1_impl.o(.text), (20 bytes).
    Removing user_custs1_impl.o(.bss), (4 bytes).
    Removing user_custs1_impl.o(.bss), (49 bytes).
    Removing user_custs1_impl.o(.bss), (2 bytes).
    Removing user_custs1_impl.o(.bss), (4 bytes).
    Removing user_peripheral.o(.text), (84 bytes).
    Removing user_peripheral.o(.text), (28 bytes).
    Removing user_peripheral.o(.constdata), (12 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (32 bytes).
    Removing scheduler.o(.bss), (1 bytes).
    Removing scheduler.o(.bss), (1 bytes).
    Removing scheduler.o(.bss), (2 bytes).
    Removing scheduler.o(retention_mem_area0), (8 bytes).
    Removing ring_buf.o(.text), (16 bytes).
    Removing ring_buf.o(.text), (4 bytes).
    Removing ring_buf.o(.text), (42 bytes).
    Removing ring_buf.o(.text), (34 bytes).
    Removing ring_buf.o(.bss), (4 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (18 bytes).
    Removing comm_manager.o(.text), (2 bytes).
    Removing comm_manager.o(.text), (84 bytes).
    Removing comm_manager.o(.text), (56 bytes).
    Removing primitivemanager.o(.text), (28 bytes).
    Removing primitivemanager.o(.text), (80 bytes).
    Removing transportmanager.o(.text), (20 bytes).
    Removing dadd.o(.text), (356 bytes).
    Removing dmul.o(.text), (208 bytes).
    Removing ddiv.o(.text), (240 bytes).
    Removing dfixul.o(.text), (64 bytes).
    Removing cdrcmple.o(.text), (40 bytes).
    Removing depilogue.o(.text), (190 bytes).
    Removing otp_cs.o(i.__ARM_common_switch8), (26 bytes).
    Removing depilogue.o(i.__ARM_clz), (46 bytes).

214 unused section(s) (total 10483 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_bond_db\app_bond_db.c 0x00000000   Number         0  app_bond_db.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_common\app.c 0x00000000   Number         0  app.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_common\app_msg_utils.c 0x00000000   Number         0  app_msg_utils.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_common\app_task.c 0x00000000   Number         0  app_task.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_common\app_utils.c 0x00000000   Number         0  app_utils.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_custs\app_customs.c 0x00000000   Number         0  app_customs.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_custs\app_customs_common.c 0x00000000   Number         0  app_customs_common.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_custs\app_customs_task.c 0x00000000   Number         0  app_customs_task.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_default_hnd\app_default_handlers.c 0x00000000   Number         0  app_default_handlers.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_diss\app_diss.c 0x00000000   Number         0  app_diss.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_diss\app_diss_task.c 0x00000000   Number         0  app_diss_task.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_easy\app_easy_msg_utils.c 0x00000000   Number         0  app_easy_msg_utils.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_easy\app_easy_security.c 0x00000000   Number         0  app_easy_security.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_easy\app_easy_timer.c 0x00000000   Number         0  app_easy_timer.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_easy\app_easy_whitelist.c 0x00000000   Number         0  app_easy_whitelist.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_entry\app_entry_point.c 0x00000000   Number         0  app_entry_point.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_sec\app_security.c 0x00000000   Number         0  app_security.o ABSOLUTE
    ..\..\..\sdk\app_modules\src\app_sec\app_security_task.c 0x00000000   Number         0  app_security_task.o ABSOLUTE
    ..\..\..\sdk\ble_stack\host\att\attm\attm_db_128.c 0x00000000   Number         0  attm_db_128.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\custom\custom_common.c 0x00000000   Number         0  custom_common.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1.c 0x00000000   Number         0  custs1.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1_task.c 0x00000000   Number         0  custs1_task.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss.c 0x00000000   Number         0  diss.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss_task.c 0x00000000   Number         0  diss_task.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\prf.c    0x00000000   Number         0  prf.o ABSOLUTE
    ..\..\..\sdk\ble_stack\profiles\prf_utils.c 0x00000000   Number         0  prf_utils.o ABSOLUTE
    ..\..\..\sdk\ble_stack\rwble\rwble.c     0x00000000   Number         0  rwble.o ABSOLUTE
    ..\..\..\sdk\platform\arch\boot\ARM\startup_DA14531.s 0x00000000   Number         0  startup_da14531.o ABSOLUTE
    ..\..\..\sdk\platform\arch\boot\system_DA14531.c 0x00000000   Number         0  system_da14531.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\arch_hibernation.c 0x00000000   Number         0  arch_hibernation.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\arch_main.c 0x00000000   Number         0  arch_main.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\arch_rom.c 0x00000000   Number         0  arch_rom.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\arch_sleep.c 0x00000000   Number         0  arch_sleep.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\arch_system.c 0x00000000   Number         0  arch_system.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\hardfault_handler.c 0x00000000   Number         0  hardfault_handler.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\jump_table.c 0x00000000   Number         0  jump_table.o ABSOLUTE
    ..\..\..\sdk\platform\arch\main\nmi_handler.c 0x00000000   Number         0  nmi_handler.o ABSOLUTE
    ..\..\..\sdk\platform\core_modules\nvds\src\nvds.c 0x00000000   Number         0  nvds.o ABSOLUTE
    ..\..\..\sdk\platform\core_modules\rf\src\ble_arp.c 0x00000000   Number         0  ble_arp.o ABSOLUTE
    ..\..\..\sdk\platform\core_modules\rf\src\rf_531.c 0x00000000   Number         0  rf_531.o ABSOLUTE
    ..\..\..\sdk\platform\core_modules\rf\src\rf_585.c 0x00000000   Number         0  rf_585.o ABSOLUTE
    ..\..\..\sdk\platform\core_modules\rwip\src\rwip.c 0x00000000   Number         0  rwip.o ABSOLUTE
    ..\..\..\sdk\platform\driver\adc\adc_531.c 0x00000000   Number         0  adc_531.o ABSOLUTE
    ..\..\..\sdk\platform\driver\gpio\gpio.c 0x00000000   Number         0  gpio.o ABSOLUTE
    ..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_531.c 0x00000000   Number         0  hw_otpc_531.o ABSOLUTE
    ..\..\..\sdk\platform\driver\syscntl\syscntl.c 0x00000000   Number         0  syscntl.o ABSOLUTE
    ..\..\..\sdk\platform\driver\trng\trng.c 0x00000000   Number         0  trng.o ABSOLUTE
    ..\..\..\sdk\platform\driver\uart\uart.c 0x00000000   Number         0  uart.o ABSOLUTE
    ..\..\..\sdk\platform\driver\wifi\wlan_coex.c 0x00000000   Number         0  wlan_coex.o ABSOLUTE
    ..\..\..\sdk\platform\utilities\otp_cs\otp_cs.c 0x00000000   Number         0  otp_cs.o ABSOLUTE
    ..\..\..\sdk\platform\utilities\otp_hdr\otp_hdr.c 0x00000000   Number         0  otp_hdr.o ABSOLUTE
    ..\..\..\third_party\hash\hash.c         0x00000000   Number         0  hash.o ABSOLUTE
    ..\..\..\third_party\rand\chacha20.c     0x00000000   Number         0  chacha20.o ABSOLUTE
    ..\..\src\DA14531\patch.c                0x00000000   Number         0  patch.o ABSOLUTE
    ..\src\COMM_MANAGER\Lib\Comm_Manager.c   0x00000000   Number         0  comm_manager.o ABSOLUTE
    ..\src\COMM_MANAGER\Lib\PrimitiveManager.c 0x00000000   Number         0  primitivemanager.o ABSOLUTE
    ..\src\COMM_MANAGER\Lib\PrimitiveQueue.c 0x00000000   Number         0  primitivequeue.o ABSOLUTE
    ..\src\COMM_MANAGER\Lib\TransportManager.c 0x00000000   Number         0  transportmanager.o ABSOLUTE
    ..\src\COMM_MANAGER\Lib\TransportQueue.c 0x00000000   Number         0  transportqueue.o ABSOLUTE
    ..\src\COMM_MANAGER\SerialInterface.c    0x00000000   Number         0  serialinterface.o ABSOLUTE
    ..\src\COMM_MANAGER\UpperLayerInterface.c 0x00000000   Number         0  upperlayerinterface.o ABSOLUTE
    ..\src\COMM_MANAGER\comm_task.c          0x00000000   Number         0  comm_task.o ABSOLUTE
    ..\src\custom_profile\ring_buf.c         0x00000000   Number         0  ring_buf.o ABSOLUTE
    ..\src\custom_profile\user_custs1_def.c  0x00000000   Number         0  user_custs1_def.o ABSOLUTE
    ..\src\custom_profile\user_custs_config.c 0x00000000   Number         0  user_custs_config.o ABSOLUTE
    ..\src\platform\user_periph_setup.c      0x00000000   Number         0  user_periph_setup.o ABSOLUTE
    ..\src\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\src\user_custs1_impl.c                0x00000000   Number         0  user_custs1_impl.o ABSOLUTE
    ..\src\user_peripheral.c                 0x00000000   Number         0  user_peripheral.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x07fc0000   Section      160  startup_da14531.o(RESET)
    otp_cs_booter                            0x07fc00a0   Section        8  otp_cs.o(otp_cs_booter)
    .ARM.Collect$$$$00000000                 0x07fc0110   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x07fc0110   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x07fc0114   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x07fc0118   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x07fc0118   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x07fc0118   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x07fc0120   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x07fc0120   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x07fc0120   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x07fc0120   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x07fc0124   Section        0  system_da14531.o(.text)
    __NVIC_DisableIRQ                        0x07fc018d   Thumb Code    26  system_da14531.o(.text)
    .text                                    0x07fc01bc   Section       80  startup_da14531.o(.text)
    NMI_Handler                              0x07fc01c5   Thumb Code    24  startup_da14531.o(.text)
    HardFault_Handler                        0x07fc01dd   Thumb Code    24  startup_da14531.o(.text)
    .text                                    0x07fc020c   Section        0  hardfault_handler.o(.text)
    .text                                    0x07fc0224   Section        0  nmi_handler.o(.text)
    .text                                    0x07fc0254   Section        0  arch_main.o(.text)
    arch_resume_from_sleep                   0x07fc0303   Thumb Code   136  arch_main.o(.text)
    arch_goto_sleep                          0x07fc038b   Thumb Code   108  arch_main.o(.text)
    init_retention_mode                      0x07fc03f7   Thumb Code   114  arch_main.o(.text)
    arch_turn_peripherals_off                0x07fc0469   Thumb Code   172  arch_main.o(.text)
    .text                                    0x07fc0564   Section        0  jump_table.o(.text)
    .text                                    0x07fc05a8   Section        0  arch_sleep.o(.text)
    .text                                    0x07fc0668   Section        0  arch_system.o(.text)
    .text                                    0x07fc0b50   Section        0  arch_rom.o(.text)
    .text                                    0x07fc0cac   Section        0  hash.o(.text)
    .text                                    0x07fc0cf8   Section        0  otp_cs.o(.text)
    calc_adc_ge                              0x07fc0fa9   Thumb Code    16  otp_cs.o(.text)
    calc_adc_offset                          0x07fc0fb9   Thumb Code    12  otp_cs.o(.text)
    .text                                    0x07fc0ff4   Section        0  otp_hdr.o(.text)
    .text                                    0x07fc1004   Section        0  syscntl.o(.text)
    dcdc_cfg                                 0x07fc1043   Thumb Code    58  syscntl.o(.text)
    .text                                    0x07fc111c   Section        0  gpio.o(.text)
    gpioshift16                              0x07fc12f9   Thumb Code    32  gpio.o(.text)
    .text                                    0x07fc1338   Section        0  hw_otpc_531.o(.text)
    dcdc_cfg                                 0x07fc1339   Thumb Code    46  hw_otpc_531.o(.text)
    .text                                    0x07fc14bc   Section        0  uart.o(.text)
    __NVIC_SetPriority                       0x07fc14bd   Thumb Code    60  uart.o(.text)
    uart_rx_isr                              0x07fc1507   Thumb Code    48  uart.o(.text)
    uart_intr_hndl                           0x07fc1537   Thumb Code   180  uart.o(.text)
    __NVIC_EnableIRQ                         0x07fc17f1   Thumb Code    18  uart.o(.text)
    __NVIC_DisableIRQ                        0x07fc1803   Thumb Code    28  uart.o(.text)
    uart_tx_isr                              0x07fc181f   Thumb Code   108  uart.o(.text)
    .text                                    0x07fc18a4   Section        0  trng.o(.text)
    .text                                    0x07fc1920   Section        0  adc_531.o(.text)
    adc_configure                            0x07fc1963   Thumb Code   238  adc_531.o(.text)
    __NVIC_DisableIRQ                        0x07fc1af1   Thumb Code    26  adc_531.o(.text)
    .text                                    0x07fc1b28   Section        0  rwble.o(.text)
    .text                                    0x07fc1ce4   Section        0  rwip.o(.text)
    get_max_sleep_duration                   0x07fc1e89   Thumb Code     8  rwip.o(.text)
    .text                                    0x07fc1ed0   Section        0  ble_arp.o(.text)
    rf_reset                                 0x07fc1ed7   Thumb Code     2  ble_arp.o(.text)
    rf_rssi_convert                          0x07fc1ed9   Thumb Code    36  ble_arp.o(.text)
    RADIOCNTL_Handler                        0x07fc1efd   Thumb Code     2  ble_arp.o(.text)
    rf_force_agc_enable                      0x07fc1eff   Thumb Code     2  ble_arp.o(.text)
    rf_txpwr_dbm_get                         0x07fc1f01   Thumb Code    52  ble_arp.o(.text)
    rf_sleep                                 0x07fc1f35   Thumb Code    24  ble_arp.o(.text)
    rf_regs                                  0x07fc1f4d   Thumb Code    52  ble_arp.o(.text)
    update_radiopwrupdn_pref_setting         0x07fc20bb   Thumb Code    32  ble_arp.o(.text)
    ble_update_arp_adpll_div_val             0x07fc20db   Thumb Code    32  ble_arp.o(.text)
    ble_update_arp_adpll_calib_vals          0x07fc20fb   Thumb Code    34  ble_arp.o(.text)
    ble_update_arp                           0x07fc211d   Thumb Code    86  ble_arp.o(.text)
    .text                                    0x07fc21b8   Section        0  rf_531.o(.text)
    .text                                    0x07fc2684   Section        0  custs1_task.o(.text)
    gattc_write_req_ind_handler              0x07fc2685   Thumb Code   314  custs1_task.o(.text)
    .text                                    0x07fc27d4   Section        0  prf.o(.text)
    .text                                    0x07fc2874   Section        0  app_default_handlers.o(.text)
    .text                                    0x07fc2a68   Section        0  app.o(.text)
    app_task_in_user_app                     0x07fc2a69   Thumb Code    32  app.o(.text)
    app_easy_gap_undirected_advertise_start_create_msg 0x07fc2bb5   Thumb Code   180  app.o(.text)
    app_easy_gap_param_update_msg_create     0x07fc2c69   Thumb Code    58  app.o(.text)
    app_easy_gap_dev_config_create_msg       0x07fc2ca3   Thumb Code   176  app.o(.text)
    .text                                    0x07fc2e44   Section        0  app_task.o(.text)
    gapm_device_ready_ind_handler            0x07fc2e45   Thumb Code    50  app_task.o(.text)
    gapm_cmp_evt_handler                     0x07fc2e77   Thumb Code   130  app_task.o(.text)
    gapc_connection_req_ind_handler          0x07fc2ef9   Thumb Code   114  app_task.o(.text)
    gapc_cmp_evt_handler                     0x07fc2f6b   Thumb Code    36  app_task.o(.text)
    gapc_disconnect_ind_handler              0x07fc2f8f   Thumb Code    58  app_task.o(.text)
    gapc_get_dev_info_req_ind_handler        0x07fc2fc9   Thumb Code   186  app_task.o(.text)
    .text                                    0x07fc30bc   Section        0  app_security.o(.text)
    .text                                    0x07fc3130   Section        0  app_security_task.o(.text)
    gapc_bond_req_ind_handler                0x07fc3131   Thumb Code    84  app_security_task.o(.text)
    gapc_bond_ind_handler                    0x07fc3185   Thumb Code   174  app_security_task.o(.text)
    gapc_encrypt_req_ind_handler             0x07fc3233   Thumb Code    12  app_security_task.o(.text)
    gapc_encrypt_ind_handler                 0x07fc323f   Thumb Code    20  app_security_task.o(.text)
    gapm_addr_solved_ind_handler             0x07fc3253   Thumb Code    12  app_security_task.o(.text)
    gapm_ral_size_ind_handler                0x07fc325f   Thumb Code     4  app_security_task.o(.text)
    gapm_ral_addr_ind_handler                0x07fc3263   Thumb Code     4  app_security_task.o(.text)
    .text                                    0x07fc3294   Section        0  app_diss.o(.text)
    .text                                    0x07fc32d0   Section        0  app_diss_task.o(.text)
    diss_value_req_ind_handler               0x07fc32d1   Thumb Code   168  app_diss_task.o(.text)
    .text                                    0x07fc33fc   Section        0  app_msg_utils.o(.text)
    .text                                    0x07fc3424   Section        0  app_easy_security.o(.text)
    pairing_rsp_create_msg                   0x07fc3425   Thumb Code    54  app_easy_security.o(.text)
    tk_exch_create_msg                       0x07fc3471   Thumb Code    40  app_easy_security.o(.text)
    csrk_exch_create_msg                     0x07fc34d3   Thumb Code    70  app_easy_security.o(.text)
    ltk_exch_create_msg                      0x07fc3531   Thumb Code    44  app_easy_security.o(.text)
    encrypt_cfm_create_msg                   0x07fc35a9   Thumb Code    38  app_easy_security.o(.text)
    .text                                    0x07fc367c   Section        0  app_easy_timer.o(.text)
    timer_canceled_handler                   0x07fc367d   Thumb Code     2  app_easy_timer.o(.text)
    .text                                    0x07fc3680   Section        0  app_easy_timer.o(.text)
    timer_modified_handler                   0x07fc3681   Thumb Code     2  app_easy_timer.o(.text)
    create_timer                             0x07fc3683   Thumb Code    56  app_easy_timer.o(.text)
    cancel_timer_handler                     0x07fc3899   Thumb Code    90  app_easy_timer.o(.text)
    .text                                    0x07fc3914   Section        0  app_customs.o(.text)
    .text                                    0x07fc3974   Section        0  app_customs_task.o(.text)
    .text                                    0x07fc398c   Section        0  app_bond_db.o(.text)
    .text                                    0x07fc3ae4   Section        0  app_utils.o(.text)
    .text                                    0x07fc3af0   Section        0  user_periph_setup.o(.text)
    .text                                    0x07fc3b38   Section        0  user_custs1_impl.o(.text)
    .text                                    0x07fc3d3c   Section        0  user_peripheral.o(.text)
    mnf_data_update                          0x07fc40a5   Thumb Code    34  user_peripheral.o(.text)
    adv_data_update_timer_cb                 0x07fc40c7   Thumb Code    72  user_peripheral.o(.text)
    param_update_request_timer_cb            0x07fc410f   Thumb Code    38  user_peripheral.o(.text)
    app_add_ad_struct                        0x07fc43bd   Thumb Code   124  user_peripheral.o(.text)
    .text                                    0x07fc4490   Section        0  user_peripheral.o(.text)
    .text                                    0x07fc44a8   Section        0  ring_buf.o(.text)
    .text                                    0x07fc4514   Section        0  comm_task.o(.text)
    .text                                    0x07fc4694   Section        0  serialinterface.o(.text)
    __NVIC_SetPriority                       0x07fc4695   Thumb Code    60  serialinterface.o(.text)
    uart_receive_cb                          0x07fc4771   Thumb Code     2  serialinterface.o(.text)
    uart_error_cb                            0x07fc4773   Thumb Code    16  serialinterface.o(.text)
    __NVIC_EnableIRQ                         0x07fc4937   Thumb Code    18  serialinterface.o(.text)
    __NVIC_DisableIRQ                        0x07fc4949   Thumb Code    28  serialinterface.o(.text)
    __NVIC_ClearPendingIRQ                   0x07fc4965   Thumb Code    18  serialinterface.o(.text)
    .text                                    0x07fc499c   Section        0  serialinterface.o(.text)
    uart_send_cb                             0x07fc499d   Thumb Code     2  serialinterface.o(.text)
    .text                                    0x07fc49a0   Section        0  upperlayerinterface.o(.text)
    .text                                    0x07fc4a40   Section        0  comm_manager.o(.text)
    Handle_Message                           0x07fc4bc9   Thumb Code   104  comm_manager.o(.text)
    .text                                    0x07fc4c38   Section        0  primitivemanager.o(.text)
    cmd_tx_event                             0x07fc4c39   Thumb Code    24  primitivemanager.o(.text)
    cmd_f_event                              0x07fc4c51   Thumb Code    24  primitivemanager.o(.text)
    cmd_s_event                              0x07fc4c69   Thumb Code    24  primitivemanager.o(.text)
    resp_r_event                             0x07fc4c81   Thumb Code    28  primitivemanager.o(.text)
    cr_ack_s_event                           0x07fc4c9d   Thumb Code    32  primitivemanager.o(.text)
    cr_nack_s_event                          0x07fc4cbd   Thumb Code    32  primitivemanager.o(.text)
    cmd_r_event                              0x07fc4d3b   Thumb Code    58  primitivemanager.o(.text)
    rsp_s_event                              0x07fc4d75   Thumb Code    24  primitivemanager.o(.text)
    rsp_f_event                              0x07fc4d8d   Thumb Code    24  primitivemanager.o(.text)
    ind_tx_event                             0x07fc4da5   Thumb Code    26  primitivemanager.o(.text)
    ind_r_event                              0x07fc4dbf   Thumb Code    30  primitivemanager.o(.text)
    ind_s_event                              0x07fc4ddd   Thumb Code    26  primitivemanager.o(.text)
    ind_f_event                              0x07fc4df7   Thumb Code    26  primitivemanager.o(.text)
    ind_ack_s_event                          0x07fc4e11   Thumb Code    30  primitivemanager.o(.text)
    ind_nack_s_event                         0x07fc4e2f   Thumb Code    30  primitivemanager.o(.text)
    cr_exec_s_event                          0x07fc4e4d   Thumb Code    40  primitivemanager.o(.text)
    cr_exec_f_event                          0x07fc4e75   Thumb Code    40  primitivemanager.o(.text)
    ind_exec_s_event                         0x07fc4e9d   Thumb Code    34  primitivemanager.o(.text)
    ind_exec_f_event                         0x07fc4ebf   Thumb Code    34  primitivemanager.o(.text)
    ind_transp_f_event                       0x07fc4ee1   Thumb Code    10  primitivemanager.o(.text)
    bufferxfer_event                         0x07fc4eeb   Thumb Code    10  primitivemanager.o(.text)
    bufferxfer_rx_event                      0x07fc4ef5   Thumb Code    10  primitivemanager.o(.text)
    bufferxfer_s_event                       0x07fc4eff   Thumb Code    26  primitivemanager.o(.text)
    bufferxfer_f_event                       0x07fc4f19   Thumb Code    26  primitivemanager.o(.text)
    bufferxfer_exec_s_event                  0x07fc4f33   Thumb Code    34  primitivemanager.o(.text)
    bufferxfer_exec_f_event                  0x07fc4f55   Thumb Code    34  primitivemanager.o(.text)
    handle_ind_change_state_event            0x07fc4f77   Thumb Code    14  primitivemanager.o(.text)
    handle_ind_timeout_event                 0x07fc4f85   Thumb Code    10  primitivemanager.o(.text)
    handle_invalid_event                     0x07fc4f8f   Thumb Code     6  primitivemanager.o(.text)
    cr_transp_f_event                        0x07fc4f95   Thumb Code    10  primitivemanager.o(.text)
    handle_cr_change_state_event             0x07fc4f9f   Thumb Code    12  primitivemanager.o(.text)
    handle_cr_timeout_event                  0x07fc4fab   Thumb Code    10  primitivemanager.o(.text)
    handle_p_cr_timeout                      0x07fc4fb5   Thumb Code    78  primitivemanager.o(.text)
    handle_p_ind_timeout                     0x07fc5003   Thumb Code    58  primitivemanager.o(.text)
    handle_p_cr_idle                         0x07fc503d   Thumb Code   104  primitivemanager.o(.text)
    handle_p_ind_idle                        0x07fc50a5   Thumb Code   122  primitivemanager.o(.text)
    handle_p_csend                           0x07fc511f   Thumb Code    48  primitivemanager.o(.text)
    handle_p_csuccess                        0x07fc514f   Thumb Code     4  primitivemanager.o(.text)
    handle_p_rrecv                           0x07fc5189   Thumb Code    20  primitivemanager.o(.text)
    handle_p_rexec                           0x07fc519d   Thumb Code    58  primitivemanager.o(.text)
    handle_p_crecv                           0x07fc51d7   Thumb Code    56  primitivemanager.o(.text)
    handle_p_cexec                           0x07fc520f   Thumb Code    52  primitivemanager.o(.text)
    handle_p_rsend                           0x07fc5243   Thumb Code    64  primitivemanager.o(.text)
    handle_p_isend                           0x07fc5283   Thumb Code   114  primitivemanager.o(.text)
    handle_p_irecv                           0x07fc52f5   Thumb Code    54  primitivemanager.o(.text)
    handle_p_iexec                           0x07fc532b   Thumb Code    66  primitivemanager.o(.text)
    handle_p_bufferxfer                      0x07fc536d   Thumb Code    66  primitivemanager.o(.text)
    handle_p_bufferxfer_rx                   0x07fc53af   Thumb Code   184  primitivemanager.o(.text)
    handle_p_exec_bufferxfer                 0x07fc5467   Thumb Code    38  primitivemanager.o(.text)
    handle_p_cr_msgfail                      0x07fc548d   Thumb Code    10  primitivemanager.o(.text)
    handle_p_ind_msgfail                     0x07fc5497   Thumb Code    10  primitivemanager.o(.text)
    handle_p_cr_transpfail                   0x07fc54a1   Thumb Code    60  primitivemanager.o(.text)
    handle_p_ind_transpfail                  0x07fc54dd   Thumb Code    40  primitivemanager.o(.text)
    handle_p_ind_invalid                     0x07fc5505   Thumb Code     6  primitivemanager.o(.text)
    handle_p_cr_invalid                      0x07fc550b   Thumb Code     6  primitivemanager.o(.text)
    IsInd                                    0x07fc5511   Thumb Code    38  primitivemanager.o(.text)
    p_cr_changeState                         0x07fc57b9   Thumb Code    12  primitivemanager.o(.text)
    p_ind_changeState                        0x07fc57c5   Thumb Code    14  primitivemanager.o(.text)
    IsCR                                     0x07fc57d3   Thumb Code    12  primitivemanager.o(.text)
    .text                                    0x07fc57ec   Section        0  primitivequeue.o(.text)
    .text                                    0x07fc5ac0   Section        0  transportmanager.o(.text)
    handle_invalid                           0x07fc5ac1   Thumb Code     4  transportmanager.o(.text)
    handle_change_state_event                0x07fc5ac5   Thumb Code    86  transportmanager.o(.text)
    handle_tx_event                          0x07fc5b1b   Thumb Code    36  transportmanager.o(.text)
    handle_st_wakeup_event                   0x07fc5b3f   Thumb Code    32  transportmanager.o(.text)
    handle_tx_comp_event                     0x07fc5b5f   Thumb Code    34  transportmanager.o(.text)
    handle_master_rx                         0x07fc5b81   Thumb Code   104  transportmanager.o(.text)
    handle_slave_rxp                         0x07fc5be9   Thumb Code   114  transportmanager.o(.text)
    handle_slave_rxh                         0x07fc5c5b   Thumb Code    60  transportmanager.o(.text)
    handle_rx_event                          0x07fc5c97   Thumb Code   114  transportmanager.o(.text)
    handle_peer_close_event                  0x07fc5d09   Thumb Code    50  transportmanager.o(.text)
    handle_close_event                       0x07fc5d3b   Thumb Code    32  transportmanager.o(.text)
    handle_ack_event                         0x07fc5d5b   Thumb Code    28  transportmanager.o(.text)
    handle_nack_event                        0x07fc5d77   Thumb Code    28  transportmanager.o(.text)
    handle_break_event                       0x07fc5da3   Thumb Code    68  transportmanager.o(.text)
    handle_timeout_event                     0x07fc5de7   Thumb Code    42  transportmanager.o(.text)
    handle_invalid_event                     0x07fc5e11   Thumb Code     6  transportmanager.o(.text)
    handle_close                             0x07fc5e51   Thumb Code    34  transportmanager.o(.text)
    handle_half_close                        0x07fc5e73   Thumb Code    10  transportmanager.o(.text)
    handle_timeout                           0x07fc5e7d   Thumb Code    10  transportmanager.o(.text)
    handle_idle                              0x07fc5e87   Thumb Code    58  transportmanager.o(.text)
    handle_master_init                       0x07fc5ec1   Thumb Code    10  transportmanager.o(.text)
    buffer_transfer_send                     0x07fc5ecf   Thumb Code   352  transportmanager.o(.text)
    handle_master_tx                         0x07fc602f   Thumb Code   106  transportmanager.o(.text)
    handle_master_proc                       0x07fc6099   Thumb Code   152  transportmanager.o(.text)
    handle_slave_init                        0x07fc6131   Thumb Code    10  transportmanager.o(.text)
    buffer_transfer_recv                     0x07fc613b   Thumb Code   136  transportmanager.o(.text)
    handle_slave_proc                        0x07fc61c3   Thumb Code    98  transportmanager.o(.text)
    handle_slave_tx                          0x07fc6225   Thumb Code    84  transportmanager.o(.text)
    t_changeState                            0x07fc62e7   Thumb Code    12  transportmanager.o(.text)
    _slave_tx_complete                       0x07fc62f3   Thumb Code   108  transportmanager.o(.text)
    .text                                    0x07fc6364   Section        0  transportqueue.o(.text)
    .text                                    0x07fc647c   Section        0  patch.o(.text)
    .text                                    0x07fc68a4   Section        0  strlen.o(.text)
    .text                                    0x07fc68b4   Section       36  init.o(.text)
    i.__0sprintf$1                           0x07fc68d8   Section        0  printf1.o(i.__0sprintf$1)
    i.__ARM_common_ll_muluu                  0x07fc6900   Section        0  arch_system.o(i.__ARM_common_ll_muluu)
    i.__scatterload_copy                     0x07fc6930   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x07fc693e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x07fc6940   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x07fc6950   Section        0  printf1.o(i._printf_core)
    _printf_core                             0x07fc6951   Thumb Code   332  printf1.o(i._printf_core)
    i._sputc                                 0x07fc6aa0   Section        0  printf1.o(i._sputc)
    _sputc                                   0x07fc6aa1   Thumb Code    10  printf1.o(i._sputc)
    .constdata                               0x07fc6aaa   Section       14  nvds.o(.constdata)
    nvds_data_storage                        0x07fc6aaa   Data          14  nvds.o(.constdata)
    .constdata                               0x07fc6ab8   Section        8  nvds.o(.constdata)
    .constdata                               0x07fc6ac0   Section      380  jump_table.o(.constdata)
    .constdata                               0x07fc6c3c   Section      236  jump_table.o(.constdata)
    .constdata                               0x07fc6d28   Section        8  arch_system.o(.constdata)
    .constdata                               0x07fc6d30   Section       80  arch_rom.o(.constdata)
    .constdata                               0x07fc6d80   Section       28  hw_otpc_531.o(.constdata)
    tim1                                     0x07fc6d80   Data          28  hw_otpc_531.o(.constdata)
    .constdata                               0x07fc6d9c   Section      112  ble_arp.o(.constdata)
    .constdata                               0x07fc6e0c   Section       16  custs1.o(.constdata)
    .constdata                               0x07fc6e1c   Section       72  custs1_task.o(.constdata)
    .constdata                               0x07fc6e64   Section        8  custs1_task.o(.constdata)
    .constdata                               0x07fc6e6c   Section       24  prf.o(.constdata)
    .constdata                               0x07fc6e84   Section       12  prf.o(.constdata)
    .constdata                               0x07fc6e90   Section      124  app.o(.constdata)
    user_adv_conf                            0x07fc6e90   Data          16  app.o(.constdata)
    user_gapm_conf                           0x07fc6ea0   Data          42  app.o(.constdata)
    user_prf_funcs                           0x07fc6ecc   Data          12  app.o(.constdata)
    TASK_DESC_APP                            0x07fc6ef0   Data          16  app.o(.constdata)
    <Data1>                                  0x07fc6f00   Data          12  app.o(.constdata)
    .constdata                               0x07fc6f0c   Section      112  app_task.o(.constdata)
    app_gap_process_handlers                 0x07fc6f0c   Data         112  app_task.o(.constdata)
    .constdata                               0x07fc6f7c   Section       56  app_security_task.o(.constdata)
    app_sec_process_handlers                 0x07fc6f7c   Data          56  app_security_task.o(.constdata)
    .constdata                               0x07fc6fb4   Section        8  app_diss_task.o(.constdata)
    app_diss_process_handlers                0x07fc6fb4   Data           8  app_diss_task.o(.constdata)
    .constdata                               0x07fc6fbc   Section      140  app_entry_point.o(.constdata)
    user_app_callbacks                       0x07fc6fbc   Data         140  app_entry_point.o(.constdata)
    .constdata                               0x07fc7048   Section       24  app_entry_point.o(.constdata)
    .constdata                               0x07fc7060   Section       32  app_entry_point.o(.constdata)
    .constdata                               0x07fc7080   Section        7  app_easy_security.o(.constdata)
    user_security_conf                       0x07fc7080   Data           7  app_easy_security.o(.constdata)
    .constdata                               0x07fc7088   Section       56  user_custs_config.o(.constdata)
    .constdata                               0x07fc70c0   Section       16  user_custs1_def.o(.constdata)
    Ecg_Service                              0x07fc70c0   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc70d0   Section       16  user_custs1_def.o(.constdata)
    ECG_SAMPLES_UUID                         0x07fc70d0   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc70e0   Section       16  user_custs1_def.o(.constdata)
    PPG_SAMPLES_UUID                         0x07fc70e0   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc70f0   Section       16  user_custs1_def.o(.constdata)
    Vital_Service                            0x07fc70f0   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7100   Section       16  user_custs1_def.o(.constdata)
    VITAL_UUID                               0x07fc7100   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7110   Section       16  user_custs1_def.o(.constdata)
    Alert_Service                            0x07fc7110   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7120   Section       16  user_custs1_def.o(.constdata)
    ALERT_STATUS_UUID                        0x07fc7120   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7130   Section       16  user_custs1_def.o(.constdata)
    User_Info_Service                        0x07fc7130   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7140   Section       16  user_custs1_def.o(.constdata)
    USR_INFO_UUID_128                        0x07fc7140   Data          16  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7150   Section        2  user_custs1_def.o(.constdata)
    att_decl_svc                             0x07fc7150   Data           2  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7152   Section        2  user_custs1_def.o(.constdata)
    att_decl_char                            0x07fc7152   Data           2  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7154   Section        2  user_custs1_def.o(.constdata)
    att_desc_cfg                             0x07fc7154   Data           2  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7156   Section        2  user_custs1_def.o(.constdata)
    att_desc_user_desc                       0x07fc7156   Data           2  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7158   Section        5  user_custs1_def.o(.constdata)
    .constdata                               0x07fc715d   Section        1  user_custs1_def.o(.constdata)
    .constdata                               0x07fc7160   Section      400  user_custs1_def.o(.constdata)
    .constdata                               0x07fc72f0   Section        8  user_custs1_impl.o(.constdata)
    .constdata                               0x07fc72f8   Section        8  comm_task.o(.constdata)
    .constdata                               0x07fc7300   Section        8  comm_task.o(.constdata)
    .constdata                               0x07fc7308   Section       16  comm_task.o(.constdata)
    TASK_DESC_COMM                           0x07fc7308   Data          16  comm_task.o(.constdata)
    .constdata                               0x07fc7318   Section        9  comm_manager.o(.constdata)
    .constdata                               0x07fc7324   Section      220  primitivemanager.o(.constdata)
    ptrigger_function                        0x07fc7324   Data         128  primitivemanager.o(.constdata)
    pstate_function                          0x07fc73a4   Data          92  primitivemanager.o(.constdata)
    .constdata                               0x07fc7400   Section      104  transportmanager.o(.constdata)
    tstate_function                          0x07fc7400   Data          56  transportmanager.o(.constdata)
    ttrigger_function                        0x07fc7438   Data          48  transportmanager.o(.constdata)
    .conststring                             0x07fc7468   Section       12  user_custs1_def.o(.conststring)
    .data                                    0x07fc74a4   Section        1  rf_531.o(.data)
    .data                                    0x07fc74a6   Section        2  user_peripheral.o(.data)
    .data                                    0x07fc74a8   Section       24  serialinterface.o(.data)
    uart_cfg                                 0x07fc74a8   Data          24  serialinterface.o(.data)
    .data                                    0x07fc74c0   Section       57  primitivemanager.o(.data)
    currentEvent                             0x07fc74c0   Data           1  primitivemanager.o(.data)
    curState_I                               0x07fc74c1   Data           1  primitivemanager.o(.data)
    prevState_I                              0x07fc74c2   Data           1  primitivemanager.o(.data)
    iTrigger                                 0x07fc74c3   Data           9  primitivemanager.o(.data)
    crTrigger                                0x07fc74cc   Data           9  primitivemanager.o(.data)
    irtTrigger                               0x07fc74d5   Data           9  primitivemanager.o(.data)
    crtTrigger                               0x07fc74de   Data           9  primitivemanager.o(.data)
    crExecTrigger                            0x07fc74e7   Data           9  primitivemanager.o(.data)
    temp                                     0x07fc74f0   Data           9  primitivemanager.o(.data)
    .data                                    0x07fc74f9   Section       18  primitivequeue.o(.data)
    iTriggerFront                            0x07fc74f9   Data           9  primitivequeue.o(.data)
    crTriggerFront                           0x07fc7502   Data           9  primitivequeue.o(.data)
    .data                                    0x07fc750b   Section        2  transportmanager.o(.data)
    Trigger                                  0x07fc750b   Data           1  transportmanager.o(.data)
    currentEvent                             0x07fc750c   Data           1  transportmanager.o(.data)
    text                                     0x07fc750d   Section      700  user_custs1_impl.o(text)
    .bss                                     0x07fc77d0   Section       12  arch_system.o(.bss)
    .bss                                     0x07fc77dc   Section        4  otp_cs.o(.bss)
    txdiv_trim                               0x07fc77dc   Data           4  otp_cs.o(.bss)
    .bss                                     0x07fc77e0   Section        2  syscntl.o(.bss)
    .bss                                     0x07fc77e4   Section     1064  uart.o(.bss)
    uartn_env                                0x07fc77e4   Data        1064  uart.o(.bss)
    .bss                                     0x07fc7c0c   Section       20  trng.o(.bss)
    extra_samples                            0x07fc7c0c   Data           4  trng.o(.bss)
    trng_bits                                0x07fc7c10   Data          16  trng.o(.bss)
    .bss                                     0x07fc7c20   Section        2  adc_531.o(.bss)
    cal_val                                  0x07fc7c20   Data           2  adc_531.o(.bss)
    .bss                                     0x07fc7c24   Section        4  rwip.o(.bss)
    .bss                                     0x07fc7c28   Section       46  rf_531.o(.bss)
    fsm_ctrl_reg                             0x07fc7c28   Data           4  rf_531.o(.bss)
    .bss                                     0x07fc7c56   Section        4  user_custs1_impl.o(.bss)
    conn_status_flag                         0x07fc7c57   Data           1  user_custs1_impl.o(.bss)
    .bss                                     0x07fc7c5a   Section        6  user_peripheral.o(.bss)
    .bss                                     0x07fc7c60   Section       10  comm_task.o(.bss)
    com_queue                                0x07fc7c60   Data          10  comm_task.o(.bss)
    .bss                                     0x07fc7c6c   Section        8  upperlayerinterface.o(.bss)
    buffer                                   0x07fc7c6c   Data           4  upperlayerinterface.o(.bss)
    buffer_length                            0x07fc7c70   Data           4  upperlayerinterface.o(.bss)
    .bss                                     0x07fc7c74   Section       36  primitivemanager.o(.bss)
    curState_CR                              0x07fc7c74   Data           1  primitivemanager.o(.bss)
    prevState_CR                             0x07fc7c75   Data           1  primitivemanager.o(.bss)
    _mtu                                     0x07fc7c76   Data           2  primitivemanager.o(.bss)
    max_segments                             0x07fc7c78   Data           2  primitivemanager.o(.bss)
    usageCount                               0x07fc7c7c   Data           4  primitivemanager.o(.bss)
    cr_master                                0x07fc7c80   Data           4  primitivemanager.o(.bss)
    i_master                                 0x07fc7c84   Data           4  primitivemanager.o(.bss)
    pendTimeout                              0x07fc7c88   Data           4  primitivemanager.o(.bss)
    cr_exec_status                           0x07fc7c8c   Data           4  primitivemanager.o(.bss)
    ind_exec_status                          0x07fc7c90   Data           4  primitivemanager.o(.bss)
    tot_len                                  0x07fc7c94   Data           4  primitivemanager.o(.bss)
    .bss                                     0x07fc7c98   Section      212  primitivequeue.o(.bss)
    ptrigger_queueCR                         0x07fc7c98   Data         106  primitivequeue.o(.bss)
    ptrigger_queueInd                        0x07fc7d02   Data         106  primitivequeue.o(.bss)
    .bss                                     0x07fc7d6c   Section      688  transportmanager.o(.bss)
    pending_timeout                          0x07fc7d6c   Data           1  transportmanager.o(.bss)
    ack_status                               0x07fc7d6f   Data           1  transportmanager.o(.bss)
    ID                                       0x07fc7d70   Data           1  transportmanager.o(.bss)
    SUB_ID                                   0x07fc7d72   Data           2  transportmanager.o(.bss)
    buffer_transfer_flag                     0x07fc7d74   Data           4  transportmanager.o(.bss)
    bufferxfer_context                       0x07fc7d78   Data          21  transportmanager.o(.bss)
    Buffer                                   0x07fc7d8d   Data         655  transportmanager.o(.bss)
    .bss                                     0x07fc801c   Section       26  transportqueue.o(.bss)
    ttrigger_queue                           0x07fc801c   Data          26  transportqueue.o(.bss)
    STACK                                    0x07fc8038   Section     1536  startup_da14531.o(STACK)
    heap_mem_area_not_ret                    0x07fc8638   Section     1036  jump_table.o(heap_mem_area_not_ret)
    retention_mem_area0                      0x07fc8bc0   Section        8  arch_main.o(retention_mem_area0)
    ret_mode_for_non_ret_heap                0x07fc8bc0   Data           1  arch_main.o(retention_mem_area0)
    ret_mode                                 0x07fc8bc1   Data           1  arch_main.o(retention_mem_area0)
    ret_mode_for_ret_data                    0x07fc8bc2   Data           1  arch_main.o(retention_mem_area0)
    reinit_non_ret_heap                      0x07fc8bc3   Data           1  arch_main.o(retention_mem_area0)
    code_size                                0x07fc8bc4   Data           4  arch_main.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8bc8   Section        4  arch_sleep.o(retention_mem_area0)
    sleep_md                                 0x07fc8bc8   Data           1  arch_sleep.o(retention_mem_area0)
    sleep_pend                               0x07fc8bc9   Data           1  arch_sleep.o(retention_mem_area0)
    sleep_cnt                                0x07fc8bca   Data           1  arch_sleep.o(retention_mem_area0)
    sleep_ext_force                          0x07fc8bcb   Data           1  arch_sleep.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8bd0   Section       48  arch_system.o(retention_mem_area0)
    rfcal_count                              0x07fc8bdc   Data           4  arch_system.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c00   Section        2  arch_rom.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c04   Section       92  otp_cs.o(retention_mem_area0)
    otp_cs                                   0x07fc8c04   Data          92  otp_cs.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c60   Section       20  gpio.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c74   Section        1  hw_otpc_531.o(retention_mem_area0)
    dcdc_reserved                            0x07fc8c74   Data           1  hw_otpc_531.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c78   Section        4  adc_531.o(retention_mem_area0)
    intr_cb                                  0x07fc8c78   Data           4  adc_531.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c7c   Section        8  rwble.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c84   Section        4  rwip.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c88   Section        4  rwip.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c8c   Section        8  rf_531.o(retention_mem_area0)
    rf_tx_pwr_lvl                            0x07fc8c8c   Data           1  rf_531.o(retention_mem_area0)
    near_field_mode                          0x07fc8c8d   Data           1  rf_531.o(retention_mem_area0)
    saved_tx_power                           0x07fc8c90   Data           4  rf_531.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8c94   Section       48  prf.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8cc4   Section       78  app.o(retention_mem_area0)
    i                                        0x07fc8cc4   Data           1  app.o(retention_mem_area0)
    k                                        0x07fc8cc5   Data           1  app.o(retention_mem_area0)
    j                                        0x07fc8cc6   Data           1  app.o(retention_mem_area0)
    adv_timer_id                             0x07fc8cc7   Data           1  app.o(retention_mem_area0)
    param_update_cmd                         0x07fc8cc8   Data           4  app.o(retention_mem_area0)
    set_dev_config_cmd                       0x07fc8ccc   Data           4  app.o(retention_mem_area0)
    adv_cmd                                  0x07fc8cd0   Data           4  app.o(retention_mem_area0)
    start_connection_cmd                     0x07fc8cd4   Data           4  app.o(retention_mem_area0)
    adv_timeout_callback                     0x07fc8cd8   Data           4  app.o(retention_mem_area0)
    app_random_addr                          0x07fc8cdc   Data           6  app.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8d12   Section       20  app.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8d26   Section        1  app_task.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8d28   Section      122  app_security.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8da4   Section       24  app_easy_security.o(retention_mem_area0)
    gapc_bond_cfm_pairing_rsp                0x07fc8da4   Data           4  app_easy_security.o(retention_mem_area0)
    gapc_bond_cfm_tk_exch                    0x07fc8da8   Data           4  app_easy_security.o(retention_mem_area0)
    gapc_bond_cfm_csrk_exch                  0x07fc8dac   Data           4  app_easy_security.o(retention_mem_area0)
    gapc_bond_cfm_ltk_exch                   0x07fc8db0   Data           4  app_easy_security.o(retention_mem_area0)
    gapc_encrypt_cfm                         0x07fc8db4   Data           4  app_easy_security.o(retention_mem_area0)
    gapc_security_req                        0x07fc8db8   Data           4  app_easy_security.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8dbc   Section       80  app_easy_timer.o(retention_mem_area0)
    timer_callbacks                          0x07fc8dbc   Data          40  app_easy_timer.o(retention_mem_area0)
    modified_timer_callbacks                 0x07fc8de4   Data          40  app_easy_timer.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8e0c   Section      135  app_bond_db.o(retention_mem_area0)
    bdb                                      0x07fc8e0c   Data         135  app_bond_db.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8e93   Section       74  user_peripheral.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8edd   Section        1  comm_task.o(retention_mem_area0)
    retention_mem_area0                      0x07fc8ede   Section        2  comm_task.o(retention_mem_area0)
    crTimer                                  0x07fc8ede   Data           1  comm_task.o(retention_mem_area0)
    indTimer                                 0x07fc8edf   Data           1  comm_task.o(retention_mem_area0)
    heap_db_area                             0x07fc8ee0   Section     1036  jump_table.o(heap_db_area)
    heap_env_area                            0x07fc92ec   Section      696  jump_table.o(heap_env_area)
    heap_msg_area                            0x07fc95a4   Section     1404  jump_table.o(heap_msg_area)
    free_area                                0x07fcb4ac   Section      160  user_custs1_impl.o(free_area)
    payloadPlaceHolder                       0x07fcb4ac   Data         160  user_custs1_impl.o(free_area)
    trng_state                               0x07fcb89c   Section        4  trng.o(trng_state)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  arch_main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _store_core_registers                     - Undefined Weak Reference
    _store_primask                            - Undefined Weak Reference
    reset_indication                          - Undefined Weak Reference
    __Vectors_Size                           0x000000a0   Number         0  startup_da14531.o ABSOLUTE
    heap_mem_area_not_ret$$Length            0x0000040c   Number         0  anon$$obj.o ABSOLUTE
    Image$$ER_IROM3$$Length                  0x000076bc   Number         0  anon$$obj.o ABSOLUTE
    uECC_vli_add                             0x07f02001   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_sub                             0x07f02019   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_mult                            0x07f02035   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    set_system_clocks                        0x07f02159   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    set_peripheral_clocks                    0x07f0216b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rf_workaround_init                       0x07f02183   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    get_stack_usage                          0x07f02185   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_eif_get_func                        0x07f0218d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_set_em_base                         0x07f0219d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    platform_initialization                  0x07f021a3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ble_init                                 0x07f02255   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ble_regs_push                            0x07f022cb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ble_regs_pop                             0x07f02323   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    platform_sleep                           0x07f02379   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rf_reinit                                0x07f02641   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_param                         0x07f02649   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pdu_recv                            0x07f02653   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_compensate                     0x07f0265d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_init                           0x07f02667   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_us_2_lpcycles                  0x07f02671   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_lpcycles_2_us                  0x07f0267b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_flow_off                            0x07f02685   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_finish_transfers                    0x07f0268d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_read                                0x07f02695   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_write                               0x07f0269d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    UART_Handler                             0x07f026a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_init                                0x07f026ad   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_flow_on                             0x07f026b5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_init                                 0x07f026bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_init                             0x07f026c5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_start                       0x07f026cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_hdr                         0x07f026d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_payl                        0x07f026dd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_tx_done                          0x07f026e5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_rx_done                          0x07f026ed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_init                                0x07f026f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_start                          0x07f026fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_hdr                            0x07f02705   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_payl                           0x07f0270d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_next_out_of_sync               0x07f02715   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_out_of_sync                         0x07f0271d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_tx_done                             0x07f02725   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_rx_done                             0x07f0272d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_task_init                             0x07f02735   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_init                            0x07f0273d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_encryption_done                      0x07f02745   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_get                                 0x07f0274d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_del                                 0x07f02755   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_put                                 0x07f0275d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_eif_get                             0x07f02765   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    platform_reset                           0x07f0276d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_stop                            0x07f02777   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_mode_tx                         0x07f02781   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_mode_rx                         0x07f0278b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_init                                 0x07f02795   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_add_profile                          0x07f0279f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_create                               0x07f027a9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_cleanup                              0x07f027b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_get_id_from_task                     0x07f027bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_get_task_from_id                     0x07f027c7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_init                                0x07f027d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    SetSystemVars                            0x07f027d9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    dbg_init                                 0x07f027e3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    dbg_platform_reset_complete              0x07f027ed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_rd_local_supp_feats_cmd_handler      0x07f027f7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_pack                            0x07f02807   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_unpack                          0x07f0281d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2c_send_lecb_message                    0x07f02835   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2c_process_sdu                          0x07f0283f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_recv_ind_handler                0x07f02849   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_lecb_connect_cfm_handler            0x07f02859   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_l2cc_pdu_recv_handler               0x07f02869   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_l2cc_pdu_recv_handler               0x07f02873   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    crypto_init                              0x07f0287d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_le_adv_report_ind                    0x07f02887   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    PK_PointMult                             0x07f02891   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_p256_start                           0x07f0289b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_create_p256_key                      0x07f028a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_p256_req_handler                     0x07f028b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_length_effective                  0x07f028c3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_length_conn_init                  0x07f028cf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_prog                         0x07f028db   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_check                        0x07f028e7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pdu_send                             0x07f028f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    dia_rand                                 0x07f028ff   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    dia_srand                                0x07f0290b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_data_notif                           0x07f02917   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ba431_get_rand                           0x07f02931   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_public_key_exchange_start           0x07f0293d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_dhkey_calc_ind                      0x07f02949   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_ecdh_key_create                     0x07f02955   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ble_init_arp                             0x07f02961   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_buf_init                              0x07f02971   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_buf_rx_free                           0x07f02a35   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_buf_rx_buffer_get                     0x07f02a4f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_buf_tx_buffer_get                     0x07f02a59   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_init                             0x07f02a81   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_pop_front                        0x07f02a91   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_flush                            0x07f02ab3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_push_back                        0x07f02ac9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_pool_init                        0x07f02aed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_push_front                       0x07f02b55   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_extract                          0x07f02b71   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_find                             0x07f02bc1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_merge                            0x07f02bd7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_insert_before                    0x07f02bf3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_insert_after                     0x07f02c2b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_list_size                             0x07f02c67   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_bdaddr_compare                        0x07f02c7b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    co_array_reverse                         0x07f02c97   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_init                                 0x07f02cb9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_nb_of_pkt_comp_evt_send       0x07f02ce5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_acl_tx_data_flush                    0x07f02d05   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_stop                                 0x07f02d6d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_reset                                0x07f02db9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_length_effective_func             0x07f02ddb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_length_conn_init_func             0x07f02e75   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_enh_con_cmp_evt_send              0x07f02ed3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_le_con_cmp_evt_send                  0x07f02ff3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_start                                0x07f0308f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_acl_tx_data_squash                   0x07f03173   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_acl_tx_desc_flushed                  0x07f03215   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_acl_tx_data_process                  0x07f03285   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_discon_event_complete_send           0x07f032d3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_update_complete_send             0x07f032f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_ltk_req_send                         0x07f0332f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_feats_rd_event_send                  0x07f03367   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_version_rd_event_send                0x07f033a3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_cmd_complete_send             0x07f033d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_cmd_status_send               0x07f033f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_cmd_discard                   0x07f0340f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_flush_occurred_send           0x07f03417   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_enc_key_ref_comp_evt_send     0x07f03431   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_common_enc_change_evt_send           0x07f0344f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_update_ind                       0x07f034b7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_lsto_con_update                      0x07f0355b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_map_update_ind                       0x07f03595   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_chnl_map_req_send                    0x07f03653   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_add_bad_chnl                         0x07f0366f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pdu_send_func                        0x07f03709   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_version_ind_pdu_send                 0x07f0377b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_ch_map_update_pdu_send               0x07f037d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pause_enc_req_pdu_send               0x07f03825   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pause_enc_rsp_pdu_send               0x07f03871   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_enc_req_pdu_send                     0x07f038d7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_enc_rsp_pdu_send                     0x07f039a3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_start_enc_rsp_pdu_send               0x07f03a41   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_reject_ind_pdu_send                  0x07f03a99   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_update_pdu_send                  0x07f03b37   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_param_req_pdu_send               0x07f03b8d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_param_rsp_pdu_send               0x07f03c1b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_feats_req_pdu_send                   0x07f03ca9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_start_enc_req_pdu_send               0x07f03d09   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_terminate_ind_pdu_send               0x07f03dbd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_unknown_rsp_send_pdu                 0x07f03e31   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_length_req_pdu_send                  0x07f03e65   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_length_rsp_pdu_send                  0x07f03f31   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_length_ind                           0x07f03fa1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_ping_req_pdu_send                    0x07f0402d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_ping_rsp_pdu_send                    0x07f0405d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_feats_req_ind                        0x07f0408d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_feats_rsp_ind                        0x07f040f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_vers_ind_ind                         0x07f04167   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_terminate_ind                        0x07f041fb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pause_enc_req_ind                    0x07f04237   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_pause_enc_rsp_ind                    0x07f04267   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_enc_req_ind                          0x07f042dd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_enc_rsp_ind                          0x07f0439d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_start_enc_req_ind                    0x07f04449   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_start_enc_rsp_ind                    0x07f044b7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_cntl_rcv                             0x07f0453f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llcp_con_param_req_pdu_unpk              0x07f045cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llcp_con_param_rsp_pdu_unpk              0x07f04645   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_con_update_req_ind                   0x07f046bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_ch_map_req_ind                       0x07f04717   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_data_rcv                             0x07f04799   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_get_free_conhdl                 0x07f06bd1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_dicon_procedure                 0x07f06c01   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_gen_skdx                        0x07f06c63   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_update_channel_map              0x07f06c77   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_set_llcp_discard_enable         0x07f06c89   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_util_set_auth_payl_to_margin         0x07f06ca1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_add_bad_chnl                         0x07f06cc9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llc_data_notif_func                      0x07f06d05   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_init                                 0x07f06e11   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_reset                                0x07f06f5d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_adv_start                            0x07f06fab   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_adv_stop                             0x07f0710f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_scan_start                           0x07f07135   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_scan_stop                            0x07f0727d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_start                            0x07f072b5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_move_to_master                       0x07f07647   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_update_req                       0x07f076df   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_update_after_param_req           0x07f0775d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_param_rsp                        0x07f07949   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_param_req                        0x07f07a45   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_stop                             0x07f07b1b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_get_mode                             0x07f07b71   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_move_to_slave                        0x07f07b95   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_ch_map_ind                           0x07f07d3b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_con_update_ind                       0x07f07d6b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_crypt_isr                            0x07f07d79   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_mode_tx_func                    0x07f07d83   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_mode_rx_func                    0x07f07e23   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_test_stop_func                       0x07f07eb5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_rx_check                        0x07f07f75   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_rx_flush                        0x07f07fb9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_check_func                   0x07f07fdf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_loop                         0x07f08085   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_push                         0x07f080b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_prog_func                    0x07f08115   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_data_tx_flush                        0x07f08255   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_drift_compute                    0x07f08363   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_elt_delete                       0x07f08449   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_deffered_elt_handler             0x07f08a6f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_init                             0x07f08b43   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_init_evt                         0x07f08bb9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_elt_insert                       0x07f08bd7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_conhdl2elt                       0x07f08c01   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_schedule_next                    0x07f08c1d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_schedule                         0x07f08d19   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_prevent_stop                     0x07f08d55   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_canceled                         0x07f08d57   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_scan_create                      0x07f08d7b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_move_to_master                   0x07f08e6f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_update_create                    0x07f08fef   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_ch_map_update_req                0x07f090f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_move_to_slave                    0x07f09109   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_slave_update                     0x07f09347   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_adv_create                       0x07f09405   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_end                              0x07f094b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_rx                               0x07f095c1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_timer_isr                        0x07f095f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_end_isr                          0x07f095fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_evt_rx_isr                           0x07f0968b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_us_2_lpcycles_func             0x07f09839   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_lpcycles_2_us_func             0x07f0985f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_enter                          0x07f09969   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_wakeup                         0x07f099a9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_sleep_wakeup_end                     0x07f099c3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_wlcoex_connection_complete           0x07f099e9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_wlcoex_remove_connection             0x07f09a01   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_wlcoex_set                           0x07f09a15   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_get_bd_address                  0x07f09a29   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_set_bd_address                  0x07f09a49   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_freq2chnl                       0x07f09a85   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_get_local_offset                0x07f09aa7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_get_peer_offset                 0x07f09ac1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    lld_util_connection_param_set            0x07f09add   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_wl_clr                               0x07f09b2d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_init                                 0x07f09b55   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_common_cmd_complete_send             0x07f09d5b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_ble_ready                            0x07f09d73   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_wl_from_rl_restore                   0x07f09d79   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_con_req_ind                          0x07f09df9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_resolv_addr                          0x07f0a0ef   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_wl_update                    0x07f0a131   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_alter_conn                           0x07f0a16b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_adv_report_set                       0x07f0a207   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_direct_adv_report_set                0x07f0a295   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_encryption_start                     0x07f0a2d7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_resolv_addr_inplace                  0x07f0a37f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_le_adv_report_ind_func               0x07f0a42b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_con_req_tx_cfm                       0x07f0a93d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_common_cmd_status_send               0x07f0aa59   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_test_mode_start_tx                   0x07f0aa73   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_test_mode_start_rx                   0x07f0ab8d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_adv_param                        0x07f0abcd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_gen_rand_addr                        0x07f0ad33   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_wl_from_rl                           0x07f0adef   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_adv_en                           0x07f0af1f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_adv_data                         0x07f0b225   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_scan_rsp_data                    0x07f0b2dd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_scan_param                       0x07f0b3cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_scan_en                          0x07f0b453   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_wl_dev_add                           0x07f0b5a7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_wl_dev_rem                           0x07f0b681   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_create_con                           0x07f0b6d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_encryption_done_func                 0x07f0b9db   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_get_chnl_assess_nb_pkt               0x07f0bcb3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_get_chnl_assess_nb_bad_pkt           0x07f0bcbb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_get_min_rssi                         0x07f0bcc3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_le_scan_report_ind                   0x07f0bccd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_set_tx_oct_time                      0x07f0bd35   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_p256_start_func                      0x07f0bd5f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_create_p256_key_func                 0x07f0bdd5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_p256_req_handler_func                0x07f0be85   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_rd_local_supp_feats_cmd_handler_func 0x07f0c6b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_bd_addr_in_wl                   0x07f0cf35   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_check_address_validity          0x07f0cfab   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_check_map_validity              0x07f0cfbb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_apply_bd_addr                   0x07f0d001   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_set_public_addr                 0x07f0d019   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_check_evt_mask                  0x07f0d027   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_get_channel_map                 0x07f0d049   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_get_supp_features               0x07f0d057   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_adv_data_update                 0x07f0d063   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_bl_check                        0x07f0d087   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_bl_add                          0x07f0d0c9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_bl_rem                          0x07f0d11f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_check                        0x07f0d16f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_add                          0x07f0d1a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_rem                          0x07f0d223   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_peer_find                    0x07f0d249   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_peer_resolv                  0x07f0d275   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    llm_util_rl_rpa_find                     0x07f0d2c7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    PK_PointMult_func                        0x07f0d2f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_time_get_slot_rounded                 0x07f0d401   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_init                                  0x07f0d4cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_elt_create                            0x07f0d521   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_time_get_halfslot_rounded             0x07f0d53b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_elt_insert                            0x07f0d56b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_elt_remove                            0x07f0d7af   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_elt_delete                            0x07f0d837   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_interval_create                       0x07f0d851   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_interval_insert                       0x07f0d867   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_interval_delete                       0x07f0d875   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_finetimer_isr                         0x07f0d88f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_sw_isr                                0x07f0d961   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_offset_req                            0x07f0d97f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_sleep_check                           0x07f0db3d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ea_interval_duration_req                 0x07f0db93   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    flash_identify                           0x07f0dcb3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    flash_init                               0x07f0dd01   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    flash_erase                              0x07f0dd3d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    flash_write                              0x07f0dda1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    flash_read                               0x07f0de05   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_init_func                           0x07f0de93   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_flow_on_func                        0x07f0def1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_flow_off_func                       0x07f0def9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_finish_transfers_func               0x07f0df49   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_read_func                           0x07f0df61   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_write_func                          0x07f0df77   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    UART_Handler_func                        0x07f0df99   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_set_flow_off_retries_limit          0x07f0dfeb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    init_delay                               0x07f0e045   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    delay_us                                 0x07f0e047   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_init_func                            0x07f0e319   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_enter_sleep                          0x07f0e341   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_exit_sleep                           0x07f0e36b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_send_msg                             0x07f0e373   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_start_func                  0x07f0e3fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_hdr_func                    0x07f0e41d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_read_payl_func                   0x07f0e43d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_tx_done_func                     0x07f0e47b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_rx_done_func                     0x07f0e48b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_init_func                        0x07f0e5bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_write                            0x07f0e5d7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_start                            0x07f0e5f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_eif_stop                             0x07f0e603   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gtl_env_curr_msg_type_set                0x07f0e61f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_tl_host_cmd_discarded                0x07f0e8d3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_tl_send                              0x07f0e8f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_tl_init                              0x07f0e939   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_cmd_get_max_param_size               0x07f0e95d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_cmd_received                         0x07f0e9a7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_acl_tx_data_alloc                    0x07f0eae3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_acl_tx_data_received                 0x07f0eb75   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_acl_rx_data_alloc                    0x07f0ebdd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_acl_rx_data_received                 0x07f0ebe9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_evt_received                         0x07f0ec1f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_tl_env_tx_queue_cnt_get              0x07f0edcb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_util_pack                            0x07f0eee3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_util_unpack                          0x07f0efe3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_look_for_cmd_desc                    0x07f0f4c9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_look_for_evt_desc                    0x07f0f515   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_look_for_le_evt_desc                 0x07f0f537   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_evt_mask_set                         0x07f0f569   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_init                                 0x07f0f5b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_reset                                0x07f0f5cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_send_2_host                          0x07f0f5e5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_host_cmd_discarded                   0x07f0f6cf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_send_2_controller                    0x07f0f6d7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_start_func                     0x07f0f7b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_hdr_func                       0x07f0f7cf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_payl_func                      0x07f0f7eb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_read_next_out_of_sync_func          0x07f0f805   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_out_of_sync_func                    0x07f0f819   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_out_of_sync_check                   0x07f0f83f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_tx_done_func                        0x07f0f897   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_rx_done_func                        0x07f0f8af   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_init_func                           0x07f0f9f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_write                               0x07f0fa11   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_start                               0x07f0fa39   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_stop                                0x07f0fa41   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_env_rx_type_set                     0x07f0fa55   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    h4tl_env_hdr_set                         0x07f0fa61   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_send_att_req                        0x07f0fa9d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_allocate_att_req                    0x07f0fad9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_send_hdl_cfm                        0x07f0fafb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_send_execute                        0x07f0fb11   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_send_read_ind                       0x07f0fb2b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attc_l2cc_pdu_recv_handler_func          0x07f1046b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_convert_to128                       0x07f104c5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_uuid_comp                           0x07f104f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_uuid16_comp                         0x07f1054d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_is_bt16_uuid                        0x07f10559   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_is_bt32_uuid                        0x07f1057f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_add_service                       0x07f1097f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_destroy                           0x07f10a07   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_service                       0x07f10a21   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_attribute                     0x07f10a5f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_next_att                      0x07f10a93   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_uuid16_comp                       0x07f10af7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_att_set_value                     0x07f10b33   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_max_len                       0x07f10bdf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_uuid                          0x07f10c45   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_value                         0x07f10d1b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_att_set_permission                0x07f10e6b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_att_update_perm                   0x07f10edd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_svc_get_permission                0x07f10f4b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_att_get_permission                0x07f10f69   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_svc_set_permission                0x07f1106b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_init                              0x07f1108f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_nb_svc                        0x07f110a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_get_svc_info                      0x07f110b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_svc_create_db                       0x07f110ed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attmdb_reserve_handle_range              0x07f111fb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_clear_read_cache                    0x07f1138d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_send_error                          0x07f11519   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_write_signed_cfm                    0x07f11535   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_send_event                          0x07f1157b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_clear_prep_data                     0x07f11603   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_clear_rsp_data                      0x07f11627   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_clear_pending_write_ind_data        0x07f1167d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_write_rsp_send                      0x07f116a1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    atts_l2cc_pdu_recv_handler_func          0x07f122b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_cleanup                            0x07f1252b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_init                               0x07f125b5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_update_state                       0x07f125e7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_create                             0x07f1260b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_con_enable                         0x07f1268b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_get_mtu                            0x07f12691   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_set_mtu                            0x07f1269d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_get_requester                      0x07f126e3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_send_complete_evt                  0x07f126ff   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_send_error_evt                     0x07f1275b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_get_operation                      0x07f12781   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_get_op_seq_num                     0x07f12797   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_get_operation_ptr                  0x07f127ad   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_set_operation_ptr                  0x07f127b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_reschedule_operation               0x07f127c5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_reallocate_svc                     0x07f12809   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_svc_get_start_hdl                  0x07f13815   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_init                               0x07f1381b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_init_attr                          0x07f13839   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_create                             0x07f1388d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_cleanup                            0x07f13895   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_get_max_mtu                        0x07f1389d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_set_max_mtu                        0x07f138a3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_get_max_mps                        0x07f138bf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattm_set_max_mps                        0x07f138c5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_cleanup                             0x07f13b2d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_init                                0x07f13b71   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_create                              0x07f13ba3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_update_state                        0x07f13bdb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    hci_acl_data_rx_handler                  0x07f13d97   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cm_init                                0x07f14029   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cm_create                              0x07f1403d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cm_cleanup                             0x07f14045   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cm_set_link_layer_buff_size            0x07f1404d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_send_use_enc_block_cmd              0x07f1405d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_send_start_enc_cmd                  0x07f14095   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_send_ltk_req_rsp                    0x07f1410f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_send_pairing_req_ind                0x07f1416b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_send_pairing_ind                    0x07f1424f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_pairing_feat                  0x07f1436b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_launch_rep_att_timer                0x07f14385   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_repeated_attempts             0x07f143c1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_max_key_size                  0x07f14423   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_key_distrib                   0x07f14469   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_xor                                 0x07f144b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_l                          0x07f144c9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_ci                         0x07f14517   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_rand                       0x07f1457b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_e1                         0x07f145a1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_cfm                        0x07f1465b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_stk                        0x07f146d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_calc_subkeys                        0x07f1472b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_clear_timeout_timer                 0x07f147a1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_end                         0x07f147cb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_tkdp_rcp_continue                   0x07f14829   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_tkdp_rcp_start                      0x07f148a1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pdu_send                            0x07f148f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_tkdp_send_start                     0x07f14993   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_tkdp_send_continue                  0x07f14a1f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_get_key_sec_prop                    0x07f14a9b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_is_sec_mode_reached                 0x07f14b67   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_handle_enc_change_evt               0x07f14ba9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pdu_recv_func                       0x07f14c63   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_subkey                     0x07f14ccf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    leftshift_onebit                         0x07f14d03   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    padding                                  0x07f14d1b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_subkey_P2                  0x07f14d3f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    AES_CMAC_block                           0x07f14de3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_f4                         0x07f14ea3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_g2                         0x07f14fb7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_f5                         0x07f15061   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_f5_T                       0x07f15073   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_f5_P2                      0x07f150e1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_generate_f6                         0x07f152b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_send_encrypt_req                    0x07f1544d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_send_gen_rand_nb_req                0x07f1547b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_check_addr_type                     0x07f15491   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_update_state                        0x07f154fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_requester                       0x07f1552d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_send_complete_evt                   0x07f15549   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_init                                0x07f15673   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_con_create                          0x07f156a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_con_create_enh                      0x07f1575f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_con_cleanup                         0x07f15859   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_send_disconect_ind                  0x07f15869   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_conidx                          0x07f1588b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_conhdl                          0x07f158c5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_role                            0x07f158dd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_bdaddr                          0x07f158f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_csrk                            0x07f15919   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_sign_counter                    0x07f15937   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_send_error_evt                      0x07f15955   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_operation                       0x07f15977   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_operation_ptr                   0x07f1598d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_set_operation_ptr                   0x07f15999   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_reschedule_operation                0x07f159a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_reschedule_conn_update              0x07f159d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_get_enc_keysize                     0x07f159fb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_is_sec_set                          0x07f15a13   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_set_enc_keysize                     0x07f15a9f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_link_encrypted                      0x07f15ab3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_auth_set                            0x07f15acd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_svc_chg_ccc_get                     0x07f15aed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_svc_chg_ccc_set                     0x07f15afd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_check_lecb_sec_perm                 0x07f15b13   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_search_lecb_channel                 0x07f15b7b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_lecnx_check_tx                      0x07f15bb5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_lecnx_check_rx                      0x07f15bfd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_lecnx_get_field                     0x07f15c41   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_process_op                          0x07f15cb5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_param_update_sanity                 0x07f15e2f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_param_cb_con_sanity                 0x07f15e57   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_recv_ind_handler_func           0x07f16323   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_lecb_connect_cfm_handler_func       0x07f172eb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_init                                0x07f176c7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_init_attr                           0x07f17723   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_operation                       0x07f1774f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_requester                       0x07f17761   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_reschedule_operation                0x07f17779   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_send_complete_evt                   0x07f1779b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_send_error_evt                      0x07f177d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_con_create                          0x07f177f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_con_enable                          0x07f17875   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_con_cleanup                         0x07f17881   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_id_from_task                    0x07f178b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_task_from_id                    0x07f178f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_is_disc_connection                  0x07f1792d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_adv_sanity                          0x07f18779   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_adv_op_sanity                       0x07f1886d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_set_adv_mode                        0x07f189f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_set_adv_data                        0x07f18a0d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_execute_adv_op                      0x07f18a9d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_scan_op_sanity                      0x07f18bc3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_set_scan_mode                       0x07f18ccb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_execute_scan_op                     0x07f18ce9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_connect_op_sanity                   0x07f18da3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_basic_hci_cmd_send                  0x07f18f23   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_execute_connect_op                  0x07f18f37   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_role                            0x07f190d9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_ad_type_flag                    0x07f190e1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_add_to_filter                       0x07f19107   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_is_filtered                         0x07f19187   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_update_air_op_state                 0x07f191eb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_irk                             0x07f192b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_get_bdaddr                          0x07f192b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_pack_func                       0x07f192d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_detect_dest                         0x07f197d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_handle_invalid_pdu                  0x07f1982d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2cc_pdu_unpack_func                     0x07f19943   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2c_process_sdu_func                     0x07f19c5f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    l2c_send_lecb_message_func               0x07f19d5f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_check_param_func                    0x07f19e59   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_hci_handler                         0x07f1aacd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_hci_handler                         0x07f1b745   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_start                       0x07f1b7b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_tk_exch                     0x07f1b837   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_ltk_exch                    0x07f1b8f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_csrk_exch                   0x07f1b949   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_rsp                         0x07f1b99f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_pairing_req_handler                 0x07f1ba83   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_security_req_send                   0x07f1babb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_encrypt_start                       0x07f1bae5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_encrypt_start_handler               0x07f1bb0b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_encrypt_cfm                         0x07f1bb3d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_sign_command                        0x07f1bb69   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_sign_cont                           0x07f1bc41   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_calc_confirm_cont                   0x07f1bdeb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_confirm_gen_rand                    0x07f1c32d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_public_key_exchange_start_func      0x07f1c3f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_dhkey_calc_start                    0x07f1c417   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_sec_authentication_start            0x07f1c447   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpc_dhkey_calc_ind_func                 0x07f1c475   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_gen_rand_addr                       0x07f1c4b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_resolv_addr                         0x07f1c4d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_use_enc_block                       0x07f1c4f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_gen_rand_nb                         0x07f1c4fb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    smpm_ecdh_key_create_func                0x07f1c503   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_init                                  0x07f1c521   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_flush                                 0x07f1c553   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_sleep_check                           0x07f1c593   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_stats_get                             0x07f1c5a5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_init                            0x07f1c5c1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_callback_set                    0x07f1c5cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_set                             0x07f1c5e1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_clear                           0x07f1c60d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_get                             0x07f1c639   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_get_all                         0x07f1c65f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_flush                           0x07f1c665   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_event_schedule                        0x07f1c66d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_mem_init                              0x07f1c6bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_mem_is_empty                          0x07f1c709   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_check_malloc                          0x07f1c749   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_malloc                                0x07f1c7d9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_free                                  0x07f1c8cf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_is_free                               0x07f1c9b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_get_mem_usage                         0x07f1c9c3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_get_max_mem_usage                     0x07f1c9cf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_alloc                             0x07f1c9f5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_send                              0x07f1ca2b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_send_basic                        0x07f1ca57   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_forward                           0x07f1ca65   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_forward_new_id                    0x07f1ca6f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_free                              0x07f1ca7f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_dest_id_get                       0x07f1ca87   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_src_id_get                        0x07f1ca8d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_in_queue                          0x07f1ca93   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_queue_extract                         0x07f1caa5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_queue_insert                          0x07f1caf5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_task_init_func                        0x07f1cddf   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_task_create                           0x07f1cdf3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_task_delete                           0x07f1ce2b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_state_set                             0x07f1ce57   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_state_get                             0x07f1ce81   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_discard                           0x07f1ce9f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_msg_save                              0x07f1cea3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_task_msg_flush                        0x07f1cea7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_init_func                       0x07f1d067   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_set                             0x07f1d073   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_clear                           0x07f1d107   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_active                          0x07f1d15d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    ke_timer_sleep_check                     0x07f1d183   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_hl_init                            0x07f1d289   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_hl_reset                           0x07f1d2ab   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_hl_send_message                    0x07f1d2cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_check_wakeup_boundary               0x07f1d2d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_init                                0x07f1d2f7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_reset                               0x07f1d3bb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_version                             0x07f1d3f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_schedule                            0x07f1d3fb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_prevent_sleep_set                   0x07f1d4a7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_wakeup                              0x07f1d4c9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_prevent_sleep_clear                 0x07f1d4df   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_wakeup_end                          0x07f1d501   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_wakeup_delay_set                    0x07f1d51d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_sleep_enable                        0x07f1d52b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwip_ext_wakeup_enable                   0x07f1d531   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_init                               0x07f1d555   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_reset                              0x07f1d5bb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_version                            0x07f1d5ef   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rwble_send_message                       0x07f1d61b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    YieldToScheduler                         0x07f1d725   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    xorshift64star                           0x07f1d72d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_set_rng                             0x07f1d793   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_get_rng                             0x07f1d799   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_private_key_size              0x07f1d79f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_public_key_size               0x07f1d7af   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_clear                           0x07f1d7b7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_isZero                          0x07f1d7cd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_testBit                         0x07f1d7ef   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_numBits                         0x07f1d801   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_set                             0x07f1d83b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_equal                           0x07f1d879   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_cmp                             0x07f1d89d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_rshift1                         0x07f1d8d3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_square                          0x07f1d8f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modAdd                          0x07f1d8fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modSub                          0x07f1d92b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_mmod                            0x07f1d94b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modMult                         0x07f1da55   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modMult_fast                    0x07f1da77   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modSquare                       0x07f1da97   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modSquare_fast                  0x07f1daa5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_modInv                          0x07f1dae5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_secp256r1                           0x07f1de21   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_nativeToBytes                   0x07f1e41f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_bytesToNative                   0x07f1e441   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_generate_random_int                 0x07f1e47f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_make_key                            0x07f1e4e1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_shared_secret                       0x07f1e55f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_compress                            0x07f1e61b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_decompress                          0x07f1e649   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_valid_point                         0x07f1e6b9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_valid_public_key                    0x07f1e71b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_compute_public_key                  0x07f1e74f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_sign                                0x07f1e9d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_sign_deterministic                  0x07f1eabb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_verify                              0x07f1ec29   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_words                     0x07f1eed5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_bytes                     0x07f1eedd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_bits                      0x07f1eee5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_n_words                   0x07f1eeed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_n_bytes                   0x07f1eefd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_num_n_bits                    0x07f1ef0d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_p                             0x07f1ef15   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_n                             0x07f1ef19   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_G                             0x07f1ef1d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_curve_b                             0x07f1ef21   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_mod_sqrt                        0x07f1ef25   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_vli_mmod_fast                       0x07f1ef2b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uECC_point_mult                          0x07f1ef31   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_uidiv                            0x07f1f005   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_uidivmod                         0x07f1f005   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_idiv                             0x07f1f031   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_idivmod                          0x07f1f031   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_lmul                             0x07f1f059   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _ll_mul                                  0x07f1f059   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    rand                                     0x07f1f0d5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    srand                                    0x07f1f0e7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memcpy                           0x07f1f0f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memcpy4                          0x07f1f0f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memcpy8                          0x07f1f0f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memset                           0x07f1f11d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memset4                          0x07f1f11d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memset8                          0x07f1f11d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memclr                           0x07f1f12b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memclr4                          0x07f1f12b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_memclr8                          0x07f1f12b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _memset$wrapper                          0x07f1f12f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    memcmp                                   0x07f1f141   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_uread4                           0x07f1f15b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __rt_uread4                              0x07f1f15b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _uread4                                  0x07f1f15b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_uwrite4                          0x07f1f16f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __rt_uwrite4                             0x07f1f16f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _uwrite4                                 0x07f1f16f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_llsl                             0x07f1f181   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _ll_shift_l                              0x07f1f181   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __ARM_common_switch8                     0x07f1f1a1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    uart_api                                 0x07f1f1bc   Data           0  da14531_symbols.txt ABSOLUTE
    co_sca2ppm                               0x07f1f1cc   Data           0  da14531_symbols.txt ABSOLUTE
    co_null_bdaddr                           0x07f1f1dc   Data           0  da14531_symbols.txt ABSOLUTE
    co_default_bdaddr                        0x07f1f1e2   Data           0  da14531_symbols.txt ABSOLUTE
    llc_state_handler                        0x07f1f468   Data           0  da14531_symbols.txt ABSOLUTE
    llc_default_handler                      0x07f1f538   Data           0  da14531_symbols.txt ABSOLUTE
    llm_debug_private_key                    0x07f1f556   Data           0  da14531_symbols.txt ABSOLUTE
    llm_local_le_states                      0x07f1f588   Data           0  da14531_symbols.txt ABSOLUTE
    llm_state_handler                        0x07f1f770   Data           0  da14531_symbols.txt ABSOLUTE
    llm_default_handler                      0x07f1f7a0   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_AA_CT1                               0x07f1f7a8   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_AA_CT2                               0x07f1f7ab   Data           0  da14531_symbols.txt ABSOLUTE
    ecc_p256_G                               0x07f1f7ad   Data           0  da14531_symbols.txt ABSOLUTE
    gtl_default_state                        0x07f1f800   Data           0  da14531_symbols.txt ABSOLUTE
    gtl_default_handler                      0x07f1f808   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_lk_ctrl                 0x07f1f814   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_ctrl_bb                 0x07f1f838   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_info_par                0x07f1f8b0   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_stat_par                0x07f1f8e0   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_le                      0x07f1f8ec   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_tab_vs                      0x07f1fb38   Data           0  da14531_symbols.txt ABSOLUTE
    rom_hci_cmd_desc_root_tab                0x07f1fc7c   Data           0  da14531_symbols.txt ABSOLUTE
    hci_evt_desc_tab                         0x07f1fcac   Data           0  da14531_symbols.txt ABSOLUTE
    hci_evt_le_desc_tab                      0x07f1fcf4   Data           0  da14531_symbols.txt ABSOLUTE
    attc_handlers                            0x07f1fd64   Data           0  da14531_symbols.txt ABSOLUTE
    atts_handlers                            0x07f1fdd4   Data           0  da14531_symbols.txt ABSOLUTE
    gattc_default_state                      0x07f1fe54   Data           0  da14531_symbols.txt ABSOLUTE
    gattc_default_handler                    0x07f1ff34   Data           0  da14531_symbols.txt ABSOLUTE
    gattm_default_state                      0x07f1ff80   Data           0  da14531_symbols.txt ABSOLUTE
    gattm_default_handler                    0x07f1ffd8   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_default_state                       0x07f1fff0   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_default_handler                     0x07f20008   Data           0  da14531_symbols.txt ABSOLUTE
    const_Rb                                 0x07f20029   Data           0  da14531_symbols.txt ABSOLUTE
    const_Zero                               0x07f20039   Data           0  da14531_symbols.txt ABSOLUTE
    gapc_default_state                       0x07f2005c   Data           0  da14531_symbols.txt ABSOLUTE
    gapc_default_handler                     0x07f201ac   Data           0  da14531_symbols.txt ABSOLUTE
    gapm_default_state                       0x07f20248   Data           0  da14531_symbols.txt ABSOLUTE
    gapm_default_handler                     0x07f20330   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_connor_pkt_format                   0x07f20338   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_signaling_pkt_format                0x07f20340   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_security_pkt_format                 0x07f2039c   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_attribute_pkt_format                0x07f203d8   Data           0  da14531_symbols.txt ABSOLUTE
    smpc_construct_pdu                       0x07f20454   Data           0  da14531_symbols.txt ABSOLUTE
    arch_printf_flush                        0x07f20be5   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    arch_vprintf                             0x07f20c9d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    arch_printf                              0x07f20cfd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    arch_puts                                0x07f20d11   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    arch_printf_process                      0x07f20d21   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_get_func                            0x07f20dcd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_init_func                           0x07f20ea9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_del_func                            0x07f20ead   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    nvds_put_func                            0x07f20eb1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    csprng_seed                              0x07f20f49   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    csprng_get_next_uint32                   0x07f20f79   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    trng_acquire                             0x07f21021   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_add_profile_func                     0x07f210d1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_cleanup_func                         0x07f211b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_env_get                              0x07f211f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_src_task_get                         0x07f2121d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_dst_task_get                         0x07f2122d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_get_id_from_task_func                0x07f21241   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_get_task_from_id_func                0x07f21279   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_reset_func                           0x07f212b1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_itf_get                              0x07f212fd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_pack_char_pres_fmt                   0x07f21321   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_pack_date_time                       0x07f2133f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_unpack_date_time                     0x07f2135f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    diss_compute_cfg_flag                    0x07f21381   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    diss_handle_to_value                     0x07f21453   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    diss_value_to_handle                     0x07f21483   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    diss_check_val_len                       0x07f214b7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    diss_prf_itf_get                         0x07f214ed   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    bass_get_att_handle                      0x07f2184b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    bass_get_att_idx                         0x07f21901   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    bass_exe_operation                       0x07f2196b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    bass_prf_itf_get                         0x07f21a6d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    suotar_prf_itf_get                       0x07f22059   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    check_client_char_cfg                    0x07f22355   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    get_value_handle                         0x07f2237f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    get_cfg_handle                           0x07f223cb   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_get_att_handle                    0x07f2242d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_get_att_idx                       0x07f22449   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_prf_itf_get                       0x07f22621   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_init_ccc_values                   0x07f226d3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_set_ccc_value                     0x07f2270b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_cmp_evt_handler                    0x07f22823   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_val_set_req_handler               0x07f22837   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_val_ntf_req_handler               0x07f22857   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_val_ind_req_handler               0x07f228b3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_att_info_rsp_handler              0x07f2290f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_read_req_ind_handler               0x07f2294b   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_att_info_req_ind_handler           0x07f22b57   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    custs1_value_req_rsp_handler             0x07f22b99   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    attm_svc_create_db_128                   0x07f22c19   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_adv_report_ind_handler_ROM          0x07f23085   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_security_ind_handler_ROM            0x07f2309f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_set_dev_info_req_ind_handler_ROM    0x07f23185   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapm_profile_added_ind_handler_ROM       0x07f231c7   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_param_update_req_ind_handler_ROM    0x07f231f9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_le_pkt_size_ind_handler_ROM         0x07f23239   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gattc_svc_changed_cfg_ind_handler_ROM    0x07f23253   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    gapc_peer_features_ind_handler_ROM       0x07f2326f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_entry_point_handler                  0x07f232a9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_std_process_event                    0x07f232f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_get_address_type_ROM                 0x07f23335   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_fill_random_byte_array_ROM           0x07f23361   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_ldivmod                          0x07f233f3   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_llsr                             0x07f2343f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    _ll_ushift_r                             0x07f2343f   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    __aeabi_uldivmod                         0x07f23461   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_db_init_start                        0x07f234c1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_db_init                              0x07f234dd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_easy_gap_confirm                     0x07f234e9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    append_device_name                       0x07f23515   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_easy_gap_update_adv_data             0x07f23539   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_easy_gap_disconnect                  0x07f23581   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_easy_gap_advertise_stop              0x07f235bd   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    active_conidx_to_conhdl                  0x07f235d9   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    active_conhdl_to_conidx                  0x07f23605   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_timer_set                            0x07f23641   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_easy_gap_set_data_packet_length      0x07f2365d   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    get_user_prf_srv_perm                    0x07f23699   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_set_prf_srv_perm                     0x07f236c1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    prf_init_srv_perm                        0x07f236f1   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    app_gattc_svc_changed_cmd_send           0x07f23715   Thumb Code     0  da14531_symbols.txt ABSOLUTE
    blank_otp_bdaddr                         0x07f239e4   Data           0  da14531_symbols.txt ABSOLUTE
    app_default_handler                      0x07f23f58   Data           0  da14531_symbols.txt ABSOLUTE
    gap_cfg_user_var_struct                  0x07f23f60   Data           0  da14531_symbols.txt ABSOLUTE
    __Vectors                                0x07fc0000   Data           4  startup_da14531.o(RESET)
    __Vectors_End                            0x07fc00a0   Data           0  startup_da14531.o(RESET)
    booter_val                               0x07fc00a0   Data           8  otp_cs.o(otp_cs_booter)
    Image$$ER_IROM3$$Base                    0x07fc0110   Number         0  anon$$obj.o ABSOLUTE
    __main                                   0x07fc0111   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x07fc0111   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x07fc0115   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x07fc0119   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x07fc0119   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x07fc0119   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x07fc0119   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x07fc0121   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x07fc0121   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    SystemInit                               0x07fc0125   Thumb Code   104  system_da14531.o(.text)
    Reset_Handler                            0x07fc01bd   Thumb Code     8  startup_da14531.o(.text)
    SVC_Handler                              0x07fc01f5   Thumb Code     2  startup_da14531.o(.text)
    PendSV_Handler                           0x07fc01f7   Thumb Code     2  startup_da14531.o(.text)
    SysTick_Handler                          0x07fc01f9   Thumb Code     2  startup_da14531.o(.text)
    BLE_RF_DIAG_Handler                      0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    DMA_Handler                              0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    I2C_Handler                              0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    KEYBRD_Handler                           0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    RESERVED21_Handler                       0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    RESERVED22_Handler                       0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    RESERVED23_Handler                       0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    RFCAL_Handler                            0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    RTC_Handler                              0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    SPI_Handler                              0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    SWTIM1_Handler                           0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    SWTIM_Handler                            0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    WKUP_QUADEC_Handler                      0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    XTAL32M_RDY_Handler                      0x07fc01fb   Thumb Code     0  startup_da14531.o(.text)
    HardFault_HandlerC                       0x07fc020d   Thumb Code    14  hardfault_handler.o(.text)
    NMI_HandlerC                             0x07fc0225   Thumb Code    42  nmi_handler.o(.text)
    main                                     0x07fc0255   Thumb Code   174  arch_main.o(.text)
    crypto_init_func                         0x07fc0565   Thumb Code     2  jump_table.o(.text)
    ba431_get_rand_func                      0x07fc0567   Thumb Code    24  jump_table.o(.text)
    dia_rand_func                            0x07fc057f   Thumb Code     8  jump_table.o(.text)
    dia_srand_func                           0x07fc0587   Thumb Code     8  jump_table.o(.text)
    dbg_init_func_empty                      0x07fc058f   Thumb Code     2  jump_table.o(.text)
    dbg_platform_reset_complete_func_empty   0x07fc0591   Thumb Code     2  jump_table.o(.text)
    smpm_ecdh_key_create_func_empty          0x07fc0593   Thumb Code     2  jump_table.o(.text)
    platform_reset_func                      0x07fc0595   Thumb Code    20  jump_table.o(.text)
    arch_disable_sleep                       0x07fc05a9   Thumb Code    26  arch_sleep.o(.text)
    arch_set_extended_sleep                  0x07fc05c3   Thumb Code    46  arch_sleep.o(.text)
    arch_set_sleep_mode                      0x07fc05f1   Thumb Code    24  arch_sleep.o(.text)
    arch_get_sleep_mode                      0x07fc0609   Thumb Code    26  arch_sleep.o(.text)
    arch_ble_ext_wakeup_get                  0x07fc0623   Thumb Code     6  arch_sleep.o(.text)
    arch_ble_force_wakeup                    0x07fc0629   Thumb Code    46  arch_sleep.o(.text)
    read_rcx_freq                            0x07fc0669   Thumb Code    80  arch_system.o(.text)
    calibrate_rcx20                          0x07fc06b9   Thumb Code    28  arch_system.o(.text)
    init_pwr_and_clk_ble                     0x07fc06d5   Thumb Code   160  arch_system.o(.text)
    lld_sleep_lpcycles_2_us_rcx_func         0x07fc0775   Thumb Code    32  arch_system.o(.text)
    lld_sleep_lpcycles_2_us_sel_func         0x07fc0795   Thumb Code     2  arch_system.o(.text)
    lld_sleep_us_2_lpcycles_rcx_func         0x07fc0797   Thumb Code    20  arch_system.o(.text)
    lld_sleep_us_2_lpcycles_sel_func         0x07fc07ab   Thumb Code     2  arch_system.o(.text)
    set_sleep_delay                          0x07fc07ad   Thumb Code    66  arch_system.o(.text)
    conditionally_run_radio_cals             0x07fc07ef   Thumb Code   134  arch_system.o(.text)
    app_use_lower_clocks_check               0x07fc0875   Thumb Code     4  arch_system.o(.text)
    lld_sleep_init_func                      0x07fc0879   Thumb Code    26  arch_system.o(.text)
    set_xtal32m_trim_value                   0x07fc0893   Thumb Code    82  arch_system.o(.text)
    system_init                              0x07fc08e5   Thumb Code   598  arch_system.o(.text)
    arch_rom_init                            0x07fc0b51   Thumb Code   212  arch_rom.o(.text)
    hash                                     0x07fc0cad   Thumb Code    50  hash.o(.text)
    otp_cs_store                             0x07fc0cf9   Thumb Code   468  otp_cs.o(.text)
    otp_cs_load_pd_rad                       0x07fc0ecd   Thumb Code    94  otp_cs.o(.text)
    otp_cs_load_pd_adpll                     0x07fc0f2b   Thumb Code    70  otp_cs.o(.text)
    otp_cs_get_adc_single_ge                 0x07fc0f71   Thumb Code     8  otp_cs.o(.text)
    otp_cs_get_adc_single_offset             0x07fc0f79   Thumb Code     8  otp_cs.o(.text)
    otp_cs_get_adc_diff_ge                   0x07fc0f81   Thumb Code     8  otp_cs.o(.text)
    otp_cs_get_adc_diff_offset               0x07fc0f89   Thumb Code     8  otp_cs.o(.text)
    otp_cs_get_adc_trim_val                  0x07fc0f91   Thumb Code     6  otp_cs.o(.text)
    otp_cs_get_adc_25_cal                    0x07fc0f97   Thumb Code     6  otp_cs.o(.text)
    otp_cs_get_xtal32m_trim_value            0x07fc0f9d   Thumb Code     6  otp_cs.o(.text)
    otp_cs_get_xtal_wait_trim                0x07fc0fa3   Thumb Code     6  otp_cs.o(.text)
    otp_hdr_get_bd_address                   0x07fc0ff5   Thumb Code    12  otp_hdr.o(.text)
    syscntl_use_lowest_amba_clocks           0x07fc1005   Thumb Code    22  syscntl.o(.text)
    syscntl_use_highest_amba_clocks          0x07fc101b   Thumb Code    22  syscntl.o(.text)
    syscntl_cfg_xtal32m_amp_reg              0x07fc1031   Thumb Code    18  syscntl.o(.text)
    syscntl_dcdc_get_level                   0x07fc107d   Thumb Code    20  syscntl.o(.text)
    syscntl_dcdc_set_level                   0x07fc1091   Thumb Code    36  syscntl.o(.text)
    syscntl_dcdc_turn_on_in_boost            0x07fc10b5   Thumb Code    86  syscntl.o(.text)
    GPIO_init                                0x07fc111d   Thumb Code     2  gpio.o(.text)
    GPIO_SetPinFunction                      0x07fc111f   Thumb Code    18  gpio.o(.text)
    GPIO_SetInactive                         0x07fc1131   Thumb Code    14  gpio.o(.text)
    GPIO_SetActive                           0x07fc113f   Thumb Code    14  gpio.o(.text)
    GPIO_ConfigurePin                        0x07fc114d   Thumb Code    38  gpio.o(.text)
    GPIO_GetPinStatus                        0x07fc1173   Thumb Code    20  gpio.o(.text)
    GPIO_EnableIRQ                           0x07fc1187   Thumb Code   230  gpio.o(.text)
    GPIO_ResetIRQ                            0x07fc126d   Thumb Code    32  gpio.o(.text)
    GPIO_RegisterCallback                    0x07fc128d   Thumb Code    20  gpio.o(.text)
    GPIOn_Handler                            0x07fc12a1   Thumb Code    68  gpio.o(.text)
    GPIO0_Handler                            0x07fc12e5   Thumb Code     4  gpio.o(.text)
    GPIO1_Handler                            0x07fc12e9   Thumb Code     4  gpio.o(.text)
    GPIO2_Handler                            0x07fc12ed   Thumb Code     4  gpio.o(.text)
    GPIO3_Handler                            0x07fc12f1   Thumb Code     4  gpio.o(.text)
    GPIO4_Handler                            0x07fc12f5   Thumb Code     4  gpio.o(.text)
    hw_otpc_enter_mode                       0x07fc1367   Thumb Code    76  hw_otpc_531.o(.text)
    hw_otpc_is_dcdc_reserved                 0x07fc13b3   Thumb Code     6  hw_otpc_531.o(.text)
    hw_otpc_clear_dcdc_reserved              0x07fc13b9   Thumb Code     8  hw_otpc_531.o(.text)
    hw_otpc_set_speed                        0x07fc13c1   Thumb Code    16  hw_otpc_531.o(.text)
    hw_otpc_init                             0x07fc13d1   Thumb Code    96  hw_otpc_531.o(.text)
    hw_otpc_disable                          0x07fc1431   Thumb Code   110  hw_otpc_531.o(.text)
    rbuf_get                                 0x07fc14f9   Thumb Code    14  uart.o(.text)
    enableBreak                              0x07fc15eb   Thumb Code    14  uart.o(.text)
    UART_Handler_SDK_func                    0x07fc15f9   Thumb Code     4  uart.o(.text)
    UART2_Handler                            0x07fc15fd   Thumb Code     4  uart.o(.text)
    uart_enable                              0x07fc1601   Thumb Code    48  uart.o(.text)
    uart_disable                             0x07fc1631   Thumb Code    52  uart.o(.text)
    uart_baudrate_setf                       0x07fc1665   Thumb Code    50  uart.o(.text)
    uart_initialize                          0x07fc1697   Thumb Code   184  uart.o(.text)
    uart_write_byte                          0x07fc174f   Thumb Code    14  uart.o(.text)
    uart_write_buffer                        0x07fc175d   Thumb Code    30  uart.o(.text)
    uart_send                                0x07fc177b   Thumb Code   118  uart.o(.text)
    init_rand_seed_from_trng                 0x07fc18a5   Thumb Code   108  trng.o(.text)
    adc_temp_sensor_disable                  0x07fc1921   Thumb Code    14  adc_531.o(.text)
    adc_temp_sensor_enable                   0x07fc192f   Thumb Code    22  adc_531.o(.text)
    adc_set_diff_input                       0x07fc1945   Thumb Code    14  adc_531.o(.text)
    adc_set_se_input                         0x07fc1953   Thumb Code    16  adc_531.o(.text)
    adc_enable                               0x07fc1a51   Thumb Code    12  adc_531.o(.text)
    adc_init                                 0x07fc1a5d   Thumb Code    54  adc_531.o(.text)
    adc_start                                0x07fc1a93   Thumb Code    12  adc_531.o(.text)
    ADC_Handler                              0x07fc1a9f   Thumb Code    18  adc_531.o(.text)
    adc_get_temp                             0x07fc1ab1   Thumb Code    64  adc_531.o(.text)
    lld_sleep_compensate_func                0x07fc1b29   Thumb Code    68  rwble.o(.text)
    power_up                                 0x07fc1b6d   Thumb Code    64  rwble.o(.text)
    BLE_WAKEUP_LP_Handler                    0x07fc1bad   Thumb Code    58  rwble.o(.text)
    rwble_isr                                0x07fc1be7   Thumb Code   218  rwble.o(.text)
    patched_ble_regs_push                    0x07fc1ce5   Thumb Code    16  rwip.o(.text)
    patched_ble_regs_pop                     0x07fc1cf5   Thumb Code    16  rwip.o(.text)
    rwip_sleep                               0x07fc1d05   Thumb Code   388  rwip.o(.text)
    rf_reg_rd                                0x07fc1ed1   Thumb Code     4  ble_arp.o(.text)
    rf_reg_wr                                0x07fc1ed5   Thumb Code     2  ble_arp.o(.text)
    rf_init_func                             0x07fc1f81   Thumb Code   158  ble_arp.o(.text)
    rf_reinit_func                           0x07fc201f   Thumb Code    88  ble_arp.o(.text)
    ble_init_arp_func                        0x07fc2077   Thumb Code    36  ble_arp.o(.text)
    rf_recalibration                         0x07fc209b   Thumb Code    32  ble_arp.o(.text)
    set_recommended_settings                 0x07fc21b9   Thumb Code   410  rf_531.o(.text)
    dis_adpll                                0x07fc2353   Thumb Code    46  rf_531.o(.text)
    kdtc_cal_end                             0x07fc2381   Thumb Code    74  rf_531.o(.text)
    dis_kdtc_cal                             0x07fc23cb   Thumb Code    12  rf_531.o(.text)
    en_adpll_tx                              0x07fc23d7   Thumb Code    58  rf_531.o(.text)
    dis_hclk                                 0x07fc2411   Thumb Code    22  rf_531.o(.text)
    en_kdtc_cal_mod0                         0x07fc2427   Thumb Code    22  rf_531.o(.text)
    kdtc_cal_init                            0x07fc243d   Thumb Code    98  rf_531.o(.text)
    kdtc_calibration                         0x07fc249f   Thumb Code   402  rf_531.o(.text)
    rf_calibration                           0x07fc2631   Thumb Code     2  rf_531.o(.text)
    rf_power_up                              0x07fc2633   Thumb Code     2  rf_531.o(.text)
    rf_adplldig_activate                     0x07fc2635   Thumb Code    38  rf_531.o(.text)
    rf_adplldig_deactivate                   0x07fc265b   Thumb Code    12  rf_531.o(.text)
    rf_pa_pwr_set                            0x07fc2667   Thumb Code     6  rf_531.o(.text)
    rf_pa_pwr_get                            0x07fc266d   Thumb Code     6  rf_531.o(.text)
    rf_nfm_is_enabled                        0x07fc2673   Thumb Code     6  rf_531.o(.text)
    prf_init_func                            0x07fc27d5   Thumb Code    96  prf.o(.text)
    prf_create_func                          0x07fc2835   Thumb Code    54  prf.o(.text)
    default_app_on_init                      0x07fc2875   Thumb Code    18  app_default_handlers.o(.text)
    default_app_on_connection                0x07fc2887   Thumb Code    28  app_default_handlers.o(.text)
    default_app_on_set_dev_config_complete   0x07fc28a3   Thumb Code    16  app_default_handlers.o(.text)
    default_app_on_db_init_complete          0x07fc28b3   Thumb Code     8  app_default_handlers.o(.text)
    default_app_on_get_dev_name              0x07fc28bb   Thumb Code    18  app_default_handlers.o(.text)
    default_app_on_get_dev_appearance        0x07fc28cd   Thumb Code     8  app_default_handlers.o(.text)
    default_app_on_get_dev_slv_pref_params   0x07fc28d5   Thumb Code    18  app_default_handlers.o(.text)
    default_app_on_set_dev_info              0x07fc28e7   Thumb Code    46  app_default_handlers.o(.text)
    default_app_update_params_request        0x07fc2915   Thumb Code    12  app_default_handlers.o(.text)
    default_app_generate_unique_static_random_addr 0x07fc2921   Thumb Code   132  app_default_handlers.o(.text)
    default_app_on_pairing_request           0x07fc29a5   Thumb Code     8  app_default_handlers.o(.text)
    default_app_on_csrk_exch                 0x07fc29ad   Thumb Code     8  app_default_handlers.o(.text)
    default_app_on_ltk_exch                  0x07fc29b5   Thumb Code    24  app_default_handlers.o(.text)
    default_app_on_encrypt_req_ind           0x07fc29cd   Thumb Code    46  app_default_handlers.o(.text)
    default_app_on_addr_solved_ind           0x07fc29fb   Thumb Code    48  app_default_handlers.o(.text)
    default_app_on_addr_resolve_failed       0x07fc2a2b   Thumb Code     8  app_default_handlers.o(.text)
    default_app_on_ral_cmp_evt               0x07fc2a33   Thumb Code    28  app_default_handlers.o(.text)
    app_db_init_next                         0x07fc2a89   Thumb Code   164  app.o(.text)
    app_prf_enable                           0x07fc2b2d   Thumb Code   136  app.o(.text)
    app_init                                 0x07fc2d53   Thumb Code    92  app.o(.text)
    app_easy_gap_undirected_advertise_get_active 0x07fc2daf   Thumb Code     2  app.o(.text)
    app_easy_gap_undirected_advertise_start  0x07fc2db1   Thumb Code    26  app.o(.text)
    app_easy_gap_param_update_start          0x07fc2dcb   Thumb Code    24  app.o(.text)
    app_easy_gap_dev_configure               0x07fc2de3   Thumb Code    18  app.o(.text)
    app_gap_process_handler                  0x07fc3083   Thumb Code    24  app_task.o(.text)
    app_sec_gen_tk                           0x07fc30bd   Thumb Code    18  app_security.o(.text)
    app_sec_gen_ltk                          0x07fc30cf   Thumb Code    54  app_security.o(.text)
    app_sec_gen_csrk                         0x07fc3105   Thumb Code    32  app_security.o(.text)
    app_sec_process_handler                  0x07fc3267   Thumb Code    24  app_security_task.o(.text)
    app_dis_init                             0x07fc3295   Thumb Code     2  app_diss.o(.text)
    app_diss_create_db                       0x07fc3297   Thumb Code    52  app_diss.o(.text)
    app_diss_process_handler                 0x07fc3379   Thumb Code    24  app_diss_task.o(.text)
    app_check_BLE_active                     0x07fc33fd   Thumb Code    36  app_msg_utils.o(.text)
    app_easy_security_send_pairing_rsp       0x07fc345b   Thumb Code    22  app_easy_security.o(.text)
    app_easy_security_tk_exch                0x07fc3499   Thumb Code    58  app_easy_security.o(.text)
    app_easy_security_csrk_exch              0x07fc3519   Thumb Code    24  app_easy_security.o(.text)
    app_easy_security_ltk_exch               0x07fc355d   Thumb Code    24  app_easy_security.o(.text)
    app_easy_security_set_ltk_exch_from_sec_env 0x07fc3575   Thumb Code    52  app_easy_security.o(.text)
    app_easy_security_encrypt_cfm            0x07fc35cf   Thumb Code    24  app_easy_security.o(.text)
    app_easy_security_set_encrypt_req_valid  0x07fc35e7   Thumb Code    36  app_easy_security.o(.text)
    app_easy_security_set_encrypt_req_invalid 0x07fc360b   Thumb Code    12  app_easy_security.o(.text)
    app_easy_security_accept_encryption      0x07fc3617   Thumb Code    38  app_easy_security.o(.text)
    app_easy_security_reject_encryption      0x07fc363d   Thumb Code    22  app_easy_security.o(.text)
    app_easy_security_bdb_init               0x07fc3653   Thumb Code     8  app_easy_security.o(.text)
    app_easy_security_bdb_add_entry          0x07fc365b   Thumb Code     8  app_easy_security.o(.text)
    app_easy_security_bdb_search_entry       0x07fc3663   Thumb Code     8  app_easy_security.o(.text)
    app_timer_api_process_handler            0x07fc36bb   Thumb Code   216  app_easy_timer.o(.text)
    app_easy_timer                           0x07fc3793   Thumb Code    80  app_easy_timer.o(.text)
    app_easy_timer_cancel                    0x07fc37e3   Thumb Code    64  app_easy_timer.o(.text)
    app_easy_timer_modify                    0x07fc3823   Thumb Code   118  app_easy_timer.o(.text)
    custs_get_func_callbacks                 0x07fc3915   Thumb Code    34  app_customs.o(.text)
    app_custs1_create_db                     0x07fc3937   Thumb Code    54  app_customs.o(.text)
    app_custs1_process_handler               0x07fc3975   Thumb Code    24  app_customs_task.o(.text)
    default_app_bdb_init                     0x07fc398d   Thumb Code    18  app_bond_db.o(.text)
    default_app_bdb_add_entry                0x07fc399f   Thumb Code   196  app_bond_db.o(.text)
    default_app_bdb_search_entry             0x07fc3a63   Thumb Code   112  app_bond_db.o(.text)
    app_fill_random_byte_array               0x07fc3ae5   Thumb Code    12  app_utils.o(.text)
    set_pad_functions                        0x07fc3af1   Thumb Code    38  user_periph_setup.o(.text)
    periph_init                              0x07fc3b17   Thumb Code    30  user_periph_setup.o(.text)
    Update_Ind_srcid                         0x07fc3b39   Thumb Code     6  user_custs1_impl.o(.text)
    Get_Software_Revision                    0x07fc3b3f   Thumb Code     4  user_custs1_impl.o(.text)
    notify_stm                               0x07fc3b43   Thumb Code    66  user_custs1_impl.o(.text)
    ECG_SAMPLES_Write_Handler                0x07fc3b85   Thumb Code    18  user_custs1_impl.o(.text)
    PPG_SAMPLES_Write_Handler                0x07fc3b97   Thumb Code     2  user_custs1_impl.o(.text)
    USER_INFO_SENDING_Function               0x07fc3b99   Thumb Code    46  user_custs1_impl.o(.text)
    Send_To_Gatt_Client                      0x07fc3bc7   Thumb Code   104  user_custs1_impl.o(.text)
    update_alert_data                        0x07fc3c2f   Thumb Code    40  user_custs1_impl.o(.text)
    UpdateVitalsReq                          0x07fc3c57   Thumb Code   154  user_custs1_impl.o(.text)
    GetBondInfo                              0x07fc3cf1   Thumb Code     6  user_custs1_impl.o(.text)
    handle_Ind_Cfm                           0x07fc3cf7   Thumb Code     2  user_custs1_impl.o(.text)
    update_conn_flag                         0x07fc3cf9   Thumb Code    34  user_custs1_impl.o(.text)
    clear_conn_status                        0x07fc3d1b   Thumb Code     8  user_custs1_impl.o(.text)
    UpdateBleMacId                           0x07fc3d3d   Thumb Code    38  user_peripheral.o(.text)
    awt_handle_error                         0x07fc3d63   Thumb Code   176  user_peripheral.o(.text)
    sendOverBle                              0x07fc3e13   Thumb Code   418  user_peripheral.o(.text)
    sendBuffOverBle                          0x07fc3fb5   Thumb Code   240  user_peripheral.o(.text)
    user_app_init                            0x07fc4135   Thumb Code    92  user_peripheral.o(.text)
    user_app_on_pairing_succeeded            0x07fc4191   Thumb Code    24  user_peripheral.o(.text)
    user_app_adv_start                       0x07fc41a9   Thumb Code    38  user_peripheral.o(.text)
    user_svc_changed_indication              0x07fc41cf   Thumb Code    14  user_peripheral.o(.text)
    user_app_connection                      0x07fc41dd   Thumb Code    84  user_peripheral.o(.text)
    user_app_adv_undirect_complete           0x07fc4231   Thumb Code     8  user_peripheral.o(.text)
    user_app_disconnect                      0x07fc4239   Thumb Code    44  user_peripheral.o(.text)
    user_app_on_tk_exch                      0x07fc4265   Thumb Code   132  user_peripheral.o(.text)
    user_catch_rest_hndl                     0x07fc42e9   Thumb Code   212  user_peripheral.o(.text)
    updateMacIDPayload                       0x07fc4491   Thumb Code    20  user_peripheral.o(.text)
    RingBuf_Init                             0x07fc44a9   Thumb Code    16  ring_buf.o(.text)
    RingBuf_put                              0x07fc44b9   Thumb Code    42  ring_buf.o(.text)
    RingBuf_get                              0x07fc44e3   Thumb Code    50  ring_buf.o(.text)
    comm_init                                0x07fc4515   Thumb Code    36  comm_task.o(.text)
    comm_manager                             0x07fc4539   Thumb Code    32  comm_task.o(.text)
    triggerCommMngr                          0x07fc4559   Thumb Code    34  comm_task.o(.text)
    crTimeoutCallback                        0x07fc457b   Thumb Code    42  comm_task.o(.text)
    iTimeoutCallback                         0x07fc45a5   Thumb Code    42  comm_task.o(.text)
    startITimer                              0x07fc45cf   Thumb Code    40  comm_task.o(.text)
    stopITimer                               0x07fc45f7   Thumb Code    22  comm_task.o(.text)
    startCRTimer                             0x07fc460d   Thumb Code    38  comm_task.o(.text)
    stopCRTimer                              0x07fc4633   Thumb Code    22  comm_task.o(.text)
    triggerFromPrimitive                     0x07fc4649   Thumb Code    44  comm_task.o(.text)
    IsComMessageEmpty                        0x07fc4675   Thumb Code     4  comm_task.o(.text)
    Comm_Can_Sleep                           0x07fc4679   Thumb Code     2  comm_task.o(.text)
    Comm_Sleep_Set                           0x07fc467b   Thumb Code     2  comm_task.o(.text)
    sendBreakCmd                             0x07fc46d1   Thumb Code     2  serialinterface.o(.text)
    WirelessUartTx                           0x07fc46d3   Thumb Code    16  serialinterface.o(.text)
    COM_STWakeup                             0x07fc46e3   Thumb Code    24  serialinterface.o(.text)
    uart_trigger                             0x07fc46fb   Thumb Code    92  serialinterface.o(.text)
    wakeupWireless                           0x07fc4757   Thumb Code    26  serialinterface.o(.text)
    Open_Master_tPort                        0x07fc4783   Thumb Code    70  serialinterface.o(.text)
    Open_Master_rPort                        0x07fc47c9   Thumb Code    46  serialinterface.o(.text)
    Open_Slave_Port                          0x07fc47f7   Thumb Code   108  serialinterface.o(.text)
    hClose_Port                              0x07fc4863   Thumb Code    12  serialinterface.o(.text)
    Close_Port                               0x07fc486f   Thumb Code    40  serialinterface.o(.text)
    Reset_Port                               0x07fc4897   Thumb Code    32  serialinterface.o(.text)
    get_crc                                  0x07fc48b7   Thumb Code    52  serialinterface.o(.text)
    EnableRFSwitch                           0x07fc48eb   Thumb Code    12  serialinterface.o(.text)
    _platform_transmit                       0x07fc48f7   Thumb Code    14  serialinterface.o(.text)
    _platform_break_handle                   0x07fc4905   Thumb Code    50  serialinterface.o(.text)
    getMtuSize                               0x07fc49a1   Thumb Code     4  upperlayerinterface.o(.text)
    getRequesterTimeStmap                    0x07fc49a5   Thumb Code     4  upperlayerinterface.o(.text)
    getCommRequester                         0x07fc49a9   Thumb Code     4  upperlayerinterface.o(.text)
    CmdIsServicable                          0x07fc49ad   Thumb Code     4  upperlayerinterface.o(.text)
    IndIsServicable                          0x07fc49b1   Thumb Code     4  upperlayerinterface.o(.text)
    MakePacket                               0x07fc49b5   Thumb Code    56  upperlayerinterface.o(.text)
    BLE_ExecuteCommand                       0x07fc49ed   Thumb Code    12  upperlayerinterface.o(.text)
    BLE_ExecuteIndication                    0x07fc49f9   Thumb Code    10  upperlayerinterface.o(.text)
    BLE_ExecuteResponse                      0x07fc4a03   Thumb Code    10  upperlayerinterface.o(.text)
    BLE_ExecuteBufferxfer                    0x07fc4a0d   Thumb Code    34  upperlayerinterface.o(.text)
    updateGattBuffer                         0x07fc4a2f   Thumb Code     8  upperlayerinterface.o(.text)
    _platform_upd_requester                  0x07fc4a37   Thumb Code     2  upperlayerinterface.o(.text)
    COM_RXEvent                              0x07fc4a41   Thumb Code    18  comm_manager.o(.text)
    COM_TXCEvent                             0x07fc4a53   Thumb Code    18  comm_manager.o(.text)
    COM_BreakEvent                           0x07fc4a65   Thumb Code    18  comm_manager.o(.text)
    Response_Status                          0x07fc4a77   Thumb Code    46  comm_manager.o(.text)
    Indication_Status                        0x07fc4aa5   Thumb Code    46  comm_manager.o(.text)
    Bufferxfer_Status                        0x07fc4ad3   Thumb Code    46  comm_manager.o(.text)
    Com_Get_Data                             0x07fc4b01   Thumb Code    28  comm_manager.o(.text)
    Com_Send_Data                            0x07fc4b1d   Thumb Code    48  comm_manager.o(.text)
    resetCommManager                         0x07fc4b4d   Thumb Code    18  comm_manager.o(.text)
    RunMainLoop                              0x07fc4b5f   Thumb Code    78  comm_manager.o(.text)
    WriteCmd                                 0x07fc4bad   Thumb Code    28  comm_manager.o(.text)
    resetPrimitiveManagerCR                  0x07fc4cdd   Thumb Code    94  primitivemanager.o(.text)
    CopyTrigger                              0x07fc5153   Thumb Code    54  primitivemanager.o(.text)
    PrimitiveMain                            0x07fc5537   Thumb Code    82  primitivemanager.o(.text)
    UpdatePrimitiveTriggerRetry              0x07fc5589   Thumb Code    40  primitivemanager.o(.text)
    SetPrimitiveTrigger_Data                 0x07fc55b1   Thumb Code    90  primitivemanager.o(.text)
    SetPrimitiveTrigger_UL                   0x07fc560b   Thumb Code    78  primitivemanager.o(.text)
    SetPrimitiveTrigger_RT                   0x07fc5659   Thumb Code    88  primitivemanager.o(.text)
    GetPrimitiveTrigger                      0x07fc56b1   Thumb Code   132  primitivemanager.o(.text)
    resetPrimitiveManagerInd                 0x07fc5735   Thumb Code    80  primitivemanager.o(.text)
    resetPrimitiveManager                    0x07fc5785   Thumb Code    14  primitivemanager.o(.text)
    resetPrimitiveMaster                     0x07fc5793   Thumb Code    38  primitivemanager.o(.text)
    InitQueue                                0x07fc57ed   Thumb Code   102  primitivequeue.o(.text)
    enqueueCmdTxToFront                      0x07fc5853   Thumb Code    28  primitivequeue.o(.text)
    enqueueIndTxToFront                      0x07fc586f   Thumb Code    32  primitivequeue.o(.text)
    isCmdTxFull                              0x07fc588f   Thumb Code    30  primitivequeue.o(.text)
    isIndTxFull                              0x07fc58ad   Thumb Code    32  primitivequeue.o(.text)
    isCmdTxEmpty                             0x07fc58cd   Thumb Code    30  primitivequeue.o(.text)
    isIndTxEmpty                             0x07fc58eb   Thumb Code    32  primitivequeue.o(.text)
    enqueueCmdTx                             0x07fc590b   Thumb Code    88  primitivequeue.o(.text)
    enqueueIndTx                             0x07fc5963   Thumb Code    90  primitivequeue.o(.text)
    dequeueCmdTx                             0x07fc59bd   Thumb Code   124  primitivequeue.o(.text)
    dequeueIndTx                             0x07fc5a39   Thumb Code   128  primitivequeue.o(.text)
    IsTransportMaster                        0x07fc5d93   Thumb Code    16  transportmanager.o(.text)
    resetTransportManager                    0x07fc5e17   Thumb Code    58  transportmanager.o(.text)
    GetTManagerContext                       0x07fc5ecb   Thumb Code     4  transportmanager.o(.text)
    GetTransportTrigger                      0x07fc6279   Thumb Code    18  transportmanager.o(.text)
    SetTransportTrigger                      0x07fc628b   Thumb Code    14  transportmanager.o(.text)
    TransportMain                            0x07fc6299   Thumb Code    52  transportmanager.o(.text)
    GetTransportManagerState                 0x07fc62cd   Thumb Code    20  transportmanager.o(.text)
    GetBufferXferContext                     0x07fc62e1   Thumb Code     6  transportmanager.o(.text)
    InitTriggerQueue                         0x07fc6365   Thumb Code    52  transportqueue.o(.text)
    isTriggerFull                            0x07fc6399   Thumb Code    30  transportqueue.o(.text)
    isTriggerEmpty                           0x07fc63b7   Thumb Code    22  transportqueue.o(.text)
    enqueueTrigger                           0x07fc63cd   Thumb Code    82  transportqueue.o(.text)
    dequeueTrigger                           0x07fc641f   Thumb Code    90  transportqueue.o(.text)
    JT_lld_test_mode_rx_func                 0x07fc647d   Thumb Code   142  patch.o(.text)
    JT_l2cc_pdu_unpack_func                  0x07fc650b   Thumb Code   786  patch.o(.text)
    JT_l2cc_pdu_recv_ind_handler_func        0x07fc681d   Thumb Code    72  patch.o(.text)
    patch_func                               0x07fc6865   Thumb Code     8  patch.o(.text)
    patch_global_vars_init                   0x07fc686d   Thumb Code     2  patch.o(.text)
    strlen                                   0x07fc68a5   Thumb Code    14  strlen.o(.text)
    __scatterload                            0x07fc68b5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x07fc68b5   Thumb Code     0  init.o(.text)
    __0sprintf$1                             0x07fc68d9   Thumb Code    36  printf1.o(i.__0sprintf$1)
    __1sprintf$1                             0x07fc68d9   Thumb Code     0  printf1.o(i.__0sprintf$1)
    __2sprintf                               0x07fc68d9   Thumb Code     0  printf1.o(i.__0sprintf$1)
    __ARM_common_ll_muluu                    0x07fc6901   Thumb Code    48  arch_system.o(i.__ARM_common_ll_muluu)
    __scatterload_copy                       0x07fc6931   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x07fc693f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x07fc6941   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    rom_nvds_cfg                             0x07fc6ab8   Data           8  nvds.o(.constdata)
    rom_func_addr_table_var                  0x07fc6ac0   Data         380  jump_table.o(.constdata)
    rom_cfg_table_var                        0x07fc6c3c   Data         236  jump_table.o(.constdata)
    my_custom_msg_handlers                   0x07fc6d30   Data           8  arch_rom.o(.constdata)
    arp_init_table                           0x07fc6d9c   Data         112  ble_arp.o(.constdata)
    rom_cust_prf_cfg                         0x07fc6e0c   Data          16  custs1.o(.constdata)
    custs1_default_state                     0x07fc6e1c   Data          72  custs1_task.o(.constdata)
    custs1_default_handler                   0x07fc6e64   Data           8  custs1_task.o(.constdata)
    prf_if                                   0x07fc6e6c   Data          24  prf.o(.constdata)
    rom_prf_cfg                              0x07fc6e84   Data          12  prf.o(.constdata)
    prf_funcs                                0x07fc6ed8   Data          24  app.o(.constdata)
    app_process_handlers                     0x07fc7048   Data          24  app_entry_point.o(.constdata)
    rom_app_task_cfg                         0x07fc7060   Data          32  app_entry_point.o(.constdata)
    cust_prf_funcs                           0x07fc7088   Data          56  user_custs_config.o(.constdata)
    custs1_services                          0x07fc7158   Data           5  user_custs1_def.o(.constdata)
    custs1_services_size                     0x07fc715d   Data           1  user_custs1_def.o(.constdata)
    custs1_att_db                            0x07fc7160   Data         400  user_custs1_def.o(.constdata)
    comm_default_state                       0x07fc72f8   Data           8  comm_task.o(.constdata)
    comm_handler                             0x07fc7300   Data           8  comm_task.o(.constdata)
    Region$$Table$$Base                      0x07fc7474   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x07fc74a4   Number         0  anon$$obj.o(Region$$Table)
    pa_en_dcf_tx_setting                     0x07fc74a4   Data           1  rf_531.o(.data)
    ADVERTISEMENT_DURATION                   0x07fc74a6   Data           2  user_peripheral.o(.data)
    device_config                            0x07fc750d   Data         700  user_custs1_impl.o(text)
    otp_hdr_timestamp                        0x07fc77d0   Data           4  arch_system.o(.bss)
    twirq_set_value                          0x07fc77d4   Data           4  arch_system.o(.bss)
    twirq_reset_value                        0x07fc77d8   Data           4  arch_system.o(.bss)
    syscntl_dcdc_state                       0x07fc77e0   Data           2  syscntl.o(.bss)
    twext_value                              0x07fc7c24   Data           4  rwip.o(.bss)
    calibration_res                          0x07fc7c2c   Data          42  rf_531.o(.bss)
    Comm_Lock                                0x07fc7c56   Data           1  user_custs1_impl.o(.bss)
    Indication_Src_ID                        0x07fc7c58   Data           2  user_custs1_impl.o(.bss)
    BLE_MAC_ID                               0x07fc7c5a   Data           6  user_peripheral.o(.bss)
    currentState                             0x07fc7d6d   Data           1  transportmanager.o(.bss)
    prevState                                0x07fc7d6e   Data           1  transportmanager.o(.bss)
    __initial_sp                             0x07fc8638   Data           0  startup_da14531.o(STACK)
    heap_mem_area_not_ret$$Base              0x07fc8638   Number         0  jump_table.o(heap_mem_area_not_ret)
    rwip_heap_non_ret                        0x07fc8638   Data        1036  jump_table.o(heap_mem_area_not_ret)
    Image$$RET_DATA_UNINIT$$Base             0x07fc8bc0   Number         0  anon$$obj.o ABSOLUTE
    sys_startup_flag                         0x07fc8bd0   Data           1  arch_system.o(retention_mem_area0)
    last_temp                                0x07fc8bd1   Data           1  arch_system.o(retention_mem_area0)
    rcx_cal_in_progress                      0x07fc8bd2   Data           1  arch_system.o(retention_mem_area0)
    clk_freq_trim_reg_value                  0x07fc8bd4   Data           2  arch_system.o(retention_mem_area0)
    arch_wkupct_deb_time                     0x07fc8bd6   Data           2  arch_system.o(retention_mem_area0)
    xtal_wait_trim                           0x07fc8bd8   Data           2  arch_system.o(retention_mem_area0)
    last_temp_time                           0x07fc8be0   Data           4  arch_system.o(retention_mem_area0)
    lp_clk_sel                               0x07fc8be4   Data           4  arch_system.o(retention_mem_area0)
    rcx_freq                                 0x07fc8be8   Data           4  arch_system.o(retention_mem_area0)
    rcx_period                               0x07fc8bec   Data           4  arch_system.o(retention_mem_area0)
    dev_bdaddr                               0x07fc8bf0   Data           6  arch_system.o(retention_mem_area0)
    rcx_slot_duration                        0x07fc8bf8   Data           8  arch_system.o(retention_mem_area0)
    heap_log                                 0x07fc8c00   Data           2  arch_rom.o(retention_mem_area0)
    GPIOHandlerFunction                      0x07fc8c60   Data          20  gpio.o(retention_mem_area0)
    arch_rwble_last_event                    0x07fc8c7c   Data           1  rwble.o(retention_mem_area0)
    ble_finetim_corr                         0x07fc8c80   Data           4  rwble.o(retention_mem_area0)
    slp_period_retained                      0x07fc8c84   Data           4  rwip.o(retention_mem_area0)
    retained_BLE_EM_BASE_REG                 0x07fc8c88   Data           4  rwip.o(retention_mem_area0)
    prf_env                                  0x07fc8c94   Data          48  prf.o(retention_mem_area0)
    app_env                                  0x07fc8ce2   Data          12  app.o(retention_mem_area0)
    device_info                              0x07fc8cee   Data          36  app.o(retention_mem_area0)
    app_prf_srv_perm                         0x07fc8d12   Data          20  app.o(retention_mem_area0)
    app_state                                0x07fc8d26   Data           1  app_task.o(retention_mem_area0)
    app_sec_env                              0x07fc8d28   Data         122  app_security.o(retention_mem_area0)
    app_connection_idx                       0x07fc8e93   Data           1  user_peripheral.o(retention_mem_area0)
    app_adv_data_update_timer_used           0x07fc8e94   Data           1  user_peripheral.o(retention_mem_area0)
    app_param_update_request_timer_used      0x07fc8e95   Data           1  user_peripheral.o(retention_mem_area0)
    mnf_data_index                           0x07fc8e96   Data           1  user_peripheral.o(retention_mem_area0)
    stored_adv_data_len                      0x07fc8e97   Data           1  user_peripheral.o(retention_mem_area0)
    stored_scan_rsp_data_len                 0x07fc8e98   Data           1  user_peripheral.o(retention_mem_area0)
    mnf_data                                 0x07fc8e99   Data           6  user_peripheral.o(retention_mem_area0)
    stored_adv_data                          0x07fc8e9f   Data          31  user_peripheral.o(retention_mem_area0)
    stored_scan_rsp_data                     0x07fc8ebe   Data          31  user_peripheral.o(retention_mem_area0)
    comm_state                               0x07fc8edd   Data           1  comm_task.o(retention_mem_area0)
    heap_db_area$$Base                       0x07fc8ee0   Number         0  jump_table.o(heap_db_area)
    rwip_heap_db_ret                         0x07fc8ee0   Data        1036  jump_table.o(heap_db_area)
    rwip_heap_env_ret                        0x07fc92ec   Data         696  jump_table.o(heap_env_area)
    rwip_heap_msg_ret                        0x07fc95a4   Data        1404  jump_table.o(heap_msg_area)
    dummy                                    0x07fc9c00   Data           0  da14531_symbols.txt ABSOLUTE
    trng_state_val                           0x07fcb89c   Data           4  trng.o(trng_state)
    ble_wakeup_executed                      0x07fcb900   Data           0  da14531_symbols.txt ABSOLUTE
    rf_in_sleep                              0x07fcb901   Data           0  da14531_symbols.txt ABSOLUTE
    custom_preinit                           0x07fcb904   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postinit                          0x07fcb908   Data           0  da14531_symbols.txt ABSOLUTE
    custom_appinit                           0x07fcb90c   Data           0  da14531_symbols.txt ABSOLUTE
    custom_preloop                           0x07fcb910   Data           0  da14531_symbols.txt ABSOLUTE
    custom_preschedule                       0x07fcb914   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postschedule                      0x07fcb918   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postschedule_async                0x07fcb91c   Data           0  da14531_symbols.txt ABSOLUTE
    custom_presleepcheck                     0x07fcb920   Data           0  da14531_symbols.txt ABSOLUTE
    custom_appsleepset                       0x07fcb924   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postsleepcheck                    0x07fcb928   Data           0  da14531_symbols.txt ABSOLUTE
    custom_presleepenter                     0x07fcb92c   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postsleepexit                     0x07fcb930   Data           0  da14531_symbols.txt ABSOLUTE
    custom_prewakeup                         0x07fcb934   Data           0  da14531_symbols.txt ABSOLUTE
    custom_postwakeup                        0x07fcb938   Data           0  da14531_symbols.txt ABSOLUTE
    custom_preidlecheck                      0x07fcb93c   Data           0  da14531_symbols.txt ABSOLUTE
    custom_pti_set                           0x07fcb940   Data           0  da14531_symbols.txt ABSOLUTE
    REG_BLE_EM_TX_BUFFER_SIZE                0x07fcb944   Data           0  da14531_symbols.txt ABSOLUTE
    REG_BLE_EM_RX_BUFFER_SIZE                0x07fcb948   Data           0  da14531_symbols.txt ABSOLUTE
    _ble_base                                0x07fcb94c   Data           0  da14531_symbols.txt ABSOLUTE
    gap_cfg_user                             0x07fcb950   Data           0  da14531_symbols.txt ABSOLUTE
    rom_func_addr_table                      0x07fcb954   Data           0  da14531_symbols.txt ABSOLUTE
    rom_cfg_table                            0x07fcb958   Data           0  da14531_symbols.txt ABSOLUTE
    BLE_TX_DESC_DATA_USER                    0x07fcb95c   Data           0  da14531_symbols.txt ABSOLUTE
    BLE_TX_DESC_CNTL_USER                    0x07fcb960   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_LE_ADV_DUMMY_IDX                     0x07fcb964   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_LE_SCAN_CON_REQ_ADV_DIR_IDX          0x07fcb968   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_LE_SCAN_RSP_IDX                      0x07fcb96c   Data           0  da14531_symbols.txt ABSOLUTE
    LLM_LE_ADV_IDX                           0x07fcb970   Data           0  da14531_symbols.txt ABSOLUTE
    length_exchange_needed                   0x07fcb974   Data           0  da14531_symbols.txt ABSOLUTE
    enh_con_cmp_cnt                          0x07fcb978   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt                               0x07fcb980   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad                           0x07fcb984   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad_adv                       0x07fcb988   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad_scn                       0x07fcb98c   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad_oth                       0x07fcb990   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad_wo_sync_err               0x07fcb994   Data           0  da14531_symbols.txt ABSOLUTE
    rx_pkt_cnt_bad_con                       0x07fcb998   Data           0  da14531_symbols.txt ABSOLUTE
    connect_req_cnt                          0x07fcb99c   Data           0  da14531_symbols.txt ABSOLUTE
    last_status                              0x07fcb9a0   Data           0  da14531_symbols.txt ABSOLUTE
    llc_state                                0x07fcb9a4   Data           0  da14531_symbols.txt ABSOLUTE
    lld_sleep_env                            0x07fcb9a8   Data           0  da14531_symbols.txt ABSOLUTE
    lld_wlcoex_enable                        0x07fcb9ac   Data           0  da14531_symbols.txt ABSOLUTE
    ble_duplicate_filter_max                 0x07fcb9b0   Data           0  da14531_symbols.txt ABSOLUTE
    ble_duplicate_filter_found               0x07fcb9b1   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_adv_all_cnt                   0x07fcb9b4   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_adv_dir_cnt                   0x07fcb9b8   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_adv_cnt                       0x07fcb9bc   Data           0  da14531_symbols.txt ABSOLUTE
    create_conn_cnt                          0x07fcb9c0   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_cnt                           0x07fcb9c4   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_restart_cnt                   0x07fcb9c8   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_peer_addr                     0x07fcb9cc   Data           0  da14531_symbols.txt ABSOLUTE
    alter_conn_local_addr                    0x07fcb9d2   Data           0  da14531_symbols.txt ABSOLUTE
    set_adv_data_discard_old                 0x07fcb9d8   Data           0  da14531_symbols.txt ABSOLUTE
    llm_resolving_list_max                   0x07fcb9d9   Data           0  da14531_symbols.txt ABSOLUTE
    llm_local_le_feats                       0x07fcb9da   Data           0  da14531_symbols.txt ABSOLUTE
    llm_bt_env                               0x07fcb9e2   Data           0  da14531_symbols.txt ABSOLUTE
    init_tx_cnt_cntl_cnt1                    0x07fcb9ec   Data           0  da14531_symbols.txt ABSOLUTE
    init_tx_cnt_cntl_cnt                     0x07fcb9f0   Data           0  da14531_symbols.txt ABSOLUTE
    tx_cnt_cntl_cnt                          0x07fcb9f4   Data           0  da14531_symbols.txt ABSOLUTE
    llm_state                                0x07fcb9f8   Data           0  da14531_symbols.txt ABSOLUTE
    delay_us_cnt                             0x07fcb9fa   Data           0  da14531_symbols.txt ABSOLUTE
    gtl_state                                0x07fcb9fc   Data           0  da14531_symbols.txt ABSOLUTE
    use_h4tl                                 0x07fcb9fd   Data           0  da14531_symbols.txt ABSOLUTE
    hci_cmd_desc_root_tab                    0x07fcba00   Data           0  da14531_symbols.txt ABSOLUTE
    gattc_state                              0x07fcba30   Data           0  da14531_symbols.txt ABSOLUTE
    gattm_state                              0x07fcba33   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_state                               0x07fcba34   Data           0  da14531_symbols.txt ABSOLUTE
    l2cm_env                                 0x07fcba38   Data           0  da14531_symbols.txt ABSOLUTE
    gapc_state                               0x07fcba3e   Data           0  da14531_symbols.txt ABSOLUTE
    gapm_state                               0x07fcba41   Data           0  da14531_symbols.txt ABSOLUTE
    whitelist_fix                            0x07fcba42   Data           0  da14531_symbols.txt ABSOLUTE
    ecdh_key_creation_in_progress            0x07fcba44   Data           0  da14531_symbols.txt ABSOLUTE
    ke_free_bad                              0x07fcba48   Data           0  da14531_symbols.txt ABSOLUTE
    DISABLE_KE_TASK_ALTERNATIVE_SAVED_QUEUE  0x07fcba4c   Data           0  da14531_symbols.txt ABSOLUTE
    rwip_env                                 0x07fcba54   Data           0  da14531_symbols.txt ABSOLUTE
    _rand_state_ROM_DATA                     0x07fcba5c   Data           0  da14531_symbols.txt ABSOLUTE
    custom_msg_handlers                      0x07fcba60   Data           0  da14531_symbols.txt ABSOLUTE
    ble_reg_save                             0x07fcba64   Data           0  da14531_symbols.txt ABSOLUTE
    sleep_env                                0x07fcbab4   Data           0  da14531_symbols.txt ABSOLUTE
    uart_env                                 0x07fcbab8   Data           0  da14531_symbols.txt ABSOLUTE
    ke_mem_heaps_used                        0x07fcbadc   Data           0  da14531_symbols.txt ABSOLUTE
    co_buf_env                               0x07fcbae0   Data           0  da14531_symbols.txt ABSOLUTE
    llc_env                                  0x07fcbb78   Data           0  da14531_symbols.txt ABSOLUTE
    lld_evt_env                              0x07fcbb84   Data           0  da14531_symbols.txt ABSOLUTE
    llm_le_env                               0x07fcbbb0   Data           0  da14531_symbols.txt ABSOLUTE
    llm_local_cmds                           0x07fcbcb0   Data           0  da14531_symbols.txt ABSOLUTE
    gtl_env                                  0x07fcbd30   Data           0  da14531_symbols.txt ABSOLUTE
    hci_env                                  0x07fcbd78   Data           0  da14531_symbols.txt ABSOLUTE
    h4tl_env                                 0x07fcbd88   Data           0  da14531_symbols.txt ABSOLUTE
    gattc_env                                0x07fcbda0   Data           0  da14531_symbols.txt ABSOLUTE
    gattm_env                                0x07fcbdac   Data           0  da14531_symbols.txt ABSOLUTE
    l2cc_env                                 0x07fcbdd0   Data           0  da14531_symbols.txt ABSOLUTE
    ecdh_key                                 0x07fcbddc   Data           0  da14531_symbols.txt ABSOLUTE
    gapc_env                                 0x07fcbe3c   Data           0  da14531_symbols.txt ABSOLUTE
    gapm_env                                 0x07fcbe48   Data           0  da14531_symbols.txt ABSOLUTE
    ke_env                                   0x07fcbe74   Data           0  da14531_symbols.txt ABSOLUTE
    rwip_rf                                  0x07fcbf58   Data           0  da14531_symbols.txt ABSOLUTE



==============================================================================

Memory Map of the image

  Image Entry point : 0x07fc0111

  Load Region LR_IROM1 (Base: 0x07fc0000, Size: 0x000000a8, Max: 0x000000c0, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x07fc0000, Size: 0x000000a8, Max: 0x000000c0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc0000   0x000000a0   Data   RO          280    RESET               startup_da14531.o
    0x07fc00a0   0x00000008   Data   RW          917    otp_cs_booter       otp_cs.o



  Load Region LR_IROM2 (Base: 0x07fc00c0, Size: 0x00000000, Max: 0x00000050, ABSOLUTE)

    Execution Region ER_IROM2 (Base: 0x07fc00c0, Size: 0x00000050, Max: 0x00000050, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc00c0   0x00000050   Zero   RW            1    ER_IROM2.bss        anon$$obj.o



  Load Region LR_IROM3 (Base: 0x07fc0110, Size: 0x000076bc, Max: 0x00008ab0, ABSOLUTE)

    Execution Region ER_IROM3 (Base: 0x07fc0110, Size: 0x000076bc, Max: 0x00008ab0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc0110   0x00000000   Code   RO         2918  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x07fc0110   0x00000004   Code   RO         3182    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x07fc0114   0x00000004   Code   RO         3185    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x07fc0118   0x00000000   Code   RO         3187    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x07fc0118   0x00000000   Code   RO         3189    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x07fc0118   0x00000008   Code   RO         3190    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x07fc0120   0x00000000   Code   RO         3192    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x07fc0120   0x00000000   Code   RO         3194    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x07fc0120   0x00000004   Code   RO         3183    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x07fc0124   0x00000098   Code   RO            3    .text               system_da14531.o
    0x07fc01bc   0x00000050   Code   RO          281    .text               startup_da14531.o
    0x07fc020c   0x00000018   Code   RO          286    .text               hardfault_handler.o
    0x07fc0224   0x00000030   Code   RO          338    .text               nmi_handler.o
    0x07fc0254   0x00000310   Code   RO          376    .text               arch_main.o
    0x07fc0564   0x00000044   Code   RO          578    .text               jump_table.o
    0x07fc05a8   0x000000c0   Code   RO          652    .text               arch_sleep.o
    0x07fc0668   0x000004e8   Code   RO          706    .text               arch_system.o
    0x07fc0b50   0x0000015c   Code   RO          873    .text               arch_rom.o
    0x07fc0cac   0x0000004c   Code   RO          897    .text               hash.o
    0x07fc0cf8   0x000002fc   Code   RO          912    .text               otp_cs.o
    0x07fc0ff4   0x00000010   Code   RO          949    .text               otp_hdr.o
    0x07fc1004   0x00000118   Code   RO          961    .text               syscntl.o
    0x07fc111c   0x0000021c   Code   RO         1001    .text               gpio.o
    0x07fc1338   0x00000184   Code   RO         1078    .text               hw_otpc_531.o
    0x07fc14bc   0x000003e8   Code   RO         1132    .text               uart.o
    0x07fc18a4   0x0000007c   Code   RO         1247    .text               trng.o
    0x07fc1920   0x00000208   Code   RO         1270    .text               adc_531.o
    0x07fc1b28   0x000001bc   Code   RO         1364    .text               rwble.o
    0x07fc1ce4   0x000001ec   Code   RO         1412    .text               rwip.o
    0x07fc1ed0   0x000002e8   Code   RO         1441    .text               ble_arp.o
    0x07fc21b8   0x000004cc   Code   RO         1473    .text               rf_531.o
    0x07fc2684   0x00000150   Code   RO         1624    .text               custs1_task.o
    0x07fc27d4   0x000000a0   Code   RO         1648    .text               prf.o
    0x07fc2874   0x000001f4   Code   RO         1676    .text               app_default_handlers.o
    0x07fc2a68   0x000003dc   Code   RO         1740    .text               app.o
    0x07fc2e44   0x00000278   Code   RO         1880    .text               app_task.o
    0x07fc30bc   0x00000074   Code   RO         1914    .text               app_security.o
    0x07fc3130   0x00000164   Code   RO         1944    .text               app_security_task.o
    0x07fc3294   0x0000003c   Code   RO         1962    .text               app_diss.o
    0x07fc32d0   0x0000012c   Code   RO         1974    .text               app_diss_task.o
    0x07fc33fc   0x00000028   Code   RO         2014    .text               app_msg_utils.o
    0x07fc3424   0x00000258   Code   RO         2086    .text               app_easy_security.o
    0x07fc367c   0x00000002   Code   RO         2205    .text               app_easy_timer.o
    0x07fc367e   0x00000002   PAD
    0x07fc3680   0x00000294   Code   RO         2206    .text               app_easy_timer.o
    0x07fc3914   0x00000060   Code   RO         2230    .text               app_customs.o
    0x07fc3974   0x00000018   Code   RO         2252    .text               app_customs_task.o
    0x07fc398c   0x00000158   Code   RO         2276    .text               app_bond_db.o
    0x07fc3ae4   0x0000000c   Code   RO         2343    .text               app_utils.o
    0x07fc3af0   0x00000048   Code   RO         2414    .text               user_periph_setup.o
    0x07fc3b38   0x00000204   Code   RO         2468    .text               user_custs1_impl.o
    0x07fc3d3c   0x00000754   Code   RO         2549    .text               user_peripheral.o
    0x07fc4490   0x00000018   Code   RO         2550    .text               user_peripheral.o
    0x07fc44a8   0x0000006c   Code   RO         2630    .text               ring_buf.o
    0x07fc4514   0x00000180   Code   RO         2672    .text               comm_task.o
    0x07fc4694   0x00000308   Code   RO         2717    .text               serialinterface.o
    0x07fc499c   0x00000002   Code   RO         2721    .text               serialinterface.o
    0x07fc499e   0x00000002   PAD
    0x07fc49a0   0x000000a0   Code   RO         2770    .text               upperlayerinterface.o
    0x07fc4a40   0x000001f8   Code   RO         2785    .text               comm_manager.o
    0x07fc4c38   0x00000bb4   Code   RO         2824    .text               primitivemanager.o
    0x07fc57ec   0x000002d4   Code   RO         2852    .text               primitivequeue.o
    0x07fc5ac0   0x000008a4   Code   RO         2874    .text               transportmanager.o
    0x07fc6364   0x00000118   Code   RO         2898    .text               transportqueue.o
    0x07fc647c   0x00000428   Code   RO         2916    .text               da14531.lib(patch.o)
    0x07fc68a4   0x0000000e   Code   RO         2919    .text               mc_p.l(strlen.o)
    0x07fc68b2   0x00000002   PAD
    0x07fc68b4   0x00000024   Code   RO         3210    .text               mc_p.l(init.o)
    0x07fc68d8   0x00000028   Code   RO         2968    i.__0sprintf$1      mc_p.l(printf1.o)
    0x07fc6900   0x00000030   Code   RO          747    i.__ARM_common_ll_muluu  arch_system.o
    0x07fc6930   0x0000000e   Code   RO         3222    i.__scatterload_copy  mc_p.l(handlers.o)
    0x07fc693e   0x00000002   Code   RO         3223    i.__scatterload_null  mc_p.l(handlers.o)
    0x07fc6940   0x0000000e   Code   RO         3224    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x07fc694e   0x00000002   PAD
    0x07fc6950   0x00000150   Code   RO         2973    i._printf_core      mc_p.l(printf1.o)
    0x07fc6aa0   0x0000000a   Code   RO         2975    i._sputc            mc_p.l(printf1.o)
    0x07fc6aaa   0x0000000e   Data   RO          353    .constdata          nvds.o
    0x07fc6ab8   0x00000008   Data   RO          354    .constdata          nvds.o
    0x07fc6ac0   0x0000017c   Data   RO          579    .constdata          jump_table.o
    0x07fc6c3c   0x000000ec   Data   RO          580    .constdata          jump_table.o
    0x07fc6d28   0x00000008   Data   RO          712    .constdata          arch_system.o
    0x07fc6d30   0x00000050   Data   RO          874    .constdata          arch_rom.o
    0x07fc6d80   0x0000001c   Data   RO         1085    .constdata          hw_otpc_531.o
    0x07fc6d9c   0x00000070   Data   RO         1443    .constdata          ble_arp.o
    0x07fc6e0c   0x00000010   Data   RO         1608    .constdata          custs1.o
    0x07fc6e1c   0x00000048   Data   RO         1625    .constdata          custs1_task.o
    0x07fc6e64   0x00000008   Data   RO         1626    .constdata          custs1_task.o
    0x07fc6e6c   0x00000018   Data   RO         1649    .constdata          prf.o
    0x07fc6e84   0x0000000c   Data   RO         1650    .constdata          prf.o
    0x07fc6e90   0x0000007c   Data   RO         1757    .constdata          app.o
    0x07fc6f0c   0x00000070   Data   RO         1882    .constdata          app_task.o
    0x07fc6f7c   0x00000038   Data   RO         1946    .constdata          app_security_task.o
    0x07fc6fb4   0x00000008   Data   RO         1975    .constdata          app_diss_task.o
    0x07fc6fbc   0x0000008c   Data   RO         1991    .constdata          app_entry_point.o
    0x07fc7048   0x00000018   Data   RO         1992    .constdata          app_entry_point.o
    0x07fc7060   0x00000020   Data   RO         1993    .constdata          app_entry_point.o
    0x07fc7080   0x00000007   Data   RO         2103    .constdata          app_easy_security.o
    0x07fc7087   0x00000001   PAD
    0x07fc7088   0x00000038   Data   RO         2375    .constdata          user_custs_config.o
    0x07fc70c0   0x00000010   Data   RO         2388    .constdata          user_custs1_def.o
    0x07fc70d0   0x00000010   Data   RO         2389    .constdata          user_custs1_def.o
    0x07fc70e0   0x00000010   Data   RO         2390    .constdata          user_custs1_def.o
    0x07fc70f0   0x00000010   Data   RO         2391    .constdata          user_custs1_def.o
    0x07fc7100   0x00000010   Data   RO         2392    .constdata          user_custs1_def.o
    0x07fc7110   0x00000010   Data   RO         2393    .constdata          user_custs1_def.o
    0x07fc7120   0x00000010   Data   RO         2394    .constdata          user_custs1_def.o
    0x07fc7130   0x00000010   Data   RO         2395    .constdata          user_custs1_def.o
    0x07fc7140   0x00000010   Data   RO         2396    .constdata          user_custs1_def.o
    0x07fc7150   0x00000002   Data   RO         2397    .constdata          user_custs1_def.o
    0x07fc7152   0x00000002   Data   RO         2398    .constdata          user_custs1_def.o
    0x07fc7154   0x00000002   Data   RO         2399    .constdata          user_custs1_def.o
    0x07fc7156   0x00000002   Data   RO         2400    .constdata          user_custs1_def.o
    0x07fc7158   0x00000005   Data   RO         2401    .constdata          user_custs1_def.o
    0x07fc715d   0x00000001   Data   RO         2402    .constdata          user_custs1_def.o
    0x07fc715e   0x00000002   PAD
    0x07fc7160   0x00000190   Data   RO         2404    .constdata          user_custs1_def.o
    0x07fc72f0   0x00000008   Data   RO         2481    .constdata          user_custs1_impl.o
    0x07fc72f8   0x00000008   Data   RO         2677    .constdata          comm_task.o
    0x07fc7300   0x00000008   Data   RO         2678    .constdata          comm_task.o
    0x07fc7308   0x00000010   Data   RO         2679    .constdata          comm_task.o
    0x07fc7318   0x00000009   Data   RO         2788    .constdata          comm_manager.o
    0x07fc7321   0x00000003   PAD
    0x07fc7324   0x000000dc   Data   RO         2828    .constdata          primitivemanager.o
    0x07fc7400   0x00000068   Data   RO         2877    .constdata          transportmanager.o
    0x07fc7468   0x0000000c   Data   RO         2405    .conststring        user_custs1_def.o
    0x07fc7474   0x00000030   Data   RO         3220    Region$$Table       anon$$obj.o
    0x07fc74a4   0x00000001   Data   RW         1488    .data               rf_531.o
    0x07fc74a5   0x00000001   PAD
    0x07fc74a6   0x00000002   Data   RW         2555    .data               user_peripheral.o
    0x07fc74a8   0x00000018   Data   RW         2722    .data               serialinterface.o
    0x07fc74c0   0x00000039   Data   RW         2829    .data               primitivemanager.o
    0x07fc74f9   0x00000012   Data   RW         2854    .data               primitivequeue.o
    0x07fc750b   0x00000002   Data   RW         2878    .data               transportmanager.o
    0x07fc750d   0x000002bc   Data   RW         2483    text                user_custs1_impl.o


    Execution Region ER_PRODTEST (Base: 0x07fc77d0, Size: 0x00000000, Max: 0xffffffff, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region ER_ZI (Base: 0x07fc77d0, Size: 0x00000e68, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc77d0   0x0000000c   Zero   RW          710    .bss                arch_system.o
    0x07fc77dc   0x00000004   Zero   RW          916    .bss                otp_cs.o
    0x07fc77e0   0x00000002   Zero   RW          966    .bss                syscntl.o
    0x07fc77e2   0x00000002   PAD
    0x07fc77e4   0x00000428   Zero   RW         1149    .bss                uart.o
    0x07fc7c0c   0x00000014   Zero   RW         1248    .bss                trng.o
    0x07fc7c20   0x00000002   Zero   RW         1283    .bss                adc_531.o
    0x07fc7c22   0x00000002   PAD
    0x07fc7c24   0x00000004   Zero   RW         1413    .bss                rwip.o
    0x07fc7c28   0x0000002e   Zero   RW         1487    .bss                rf_531.o
    0x07fc7c56   0x00000004   Zero   RW         2476    .bss                user_custs1_impl.o
    0x07fc7c5a   0x00000006   Zero   RW         2553    .bss                user_peripheral.o
    0x07fc7c60   0x0000000a   Zero   RW         2676    .bss                comm_task.o
    0x07fc7c6a   0x00000002   PAD
    0x07fc7c6c   0x00000008   Zero   RW         2771    .bss                upperlayerinterface.o
    0x07fc7c74   0x00000024   Zero   RW         2827    .bss                primitivemanager.o
    0x07fc7c98   0x000000d4   Zero   RW         2853    .bss                primitivequeue.o
    0x07fc7d6c   0x000002b0   Zero   RW         2876    .bss                transportmanager.o
    0x07fc801c   0x0000001a   Zero   RW         2899    .bss                transportqueue.o
    0x07fc8036   0x00000002   PAD
    0x07fc8038   0x00000600   Zero   RW          278    STACK               startup_da14531.o


    Execution Region ER_NZI (Base: 0x07fc8638, Size: 0x0000040c, Max: 0xffffffff, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc8638   0x0000040c   Zero   RW          583    heap_mem_area_not_ret  jump_table.o


    Execution Region ER_STATEFUL_HIBERNATION (Base: 0x07fc8a44, Size: 0x00000000, Max: 0xffffffff, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****



  Load Region LR_RETAINED_RAM0 (Base: 0x07fc8bc0, Size: 0x00000000, Max: 0x00001440, ABSOLUTE)

    Execution Region RET_DATA_UNINIT (Base: 0x07fc8bc0, Size: 0x00000000, Max: 0x00000000, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region RET_DATA (Base: 0x07fc8bc0, Size: 0x00000320, Max: 0x00000800, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc8bc0   0x00000008   Zero   RW          378    retention_mem_area0  arch_main.o
    0x07fc8bc8   0x00000004   Zero   RW          659    retention_mem_area0  arch_sleep.o
    0x07fc8bcc   0x00000004   PAD
    0x07fc8bd0   0x00000030   Zero   RW          714    retention_mem_area0  arch_system.o
    0x07fc8c00   0x00000002   Zero   RW          875    retention_mem_area0  arch_rom.o
    0x07fc8c02   0x00000002   PAD
    0x07fc8c04   0x0000005c   Zero   RW          918    retention_mem_area0  otp_cs.o
    0x07fc8c60   0x00000014   Zero   RW         1011    retention_mem_area0  gpio.o
    0x07fc8c74   0x00000001   Zero   RW         1086    retention_mem_area0  hw_otpc_531.o
    0x07fc8c75   0x00000003   PAD
    0x07fc8c78   0x00000004   Zero   RW         1284    retention_mem_area0  adc_531.o
    0x07fc8c7c   0x00000008   Zero   RW         1365    retention_mem_area0  rwble.o
    0x07fc8c84   0x00000004   Zero   RW         1415    retention_mem_area0  rwip.o
    0x07fc8c88   0x00000004   Zero   RW         1416    retention_mem_area0  rwip.o
    0x07fc8c8c   0x00000008   Zero   RW         1489    retention_mem_area0  rf_531.o
    0x07fc8c94   0x00000030   Zero   RW         1651    retention_mem_area0  prf.o
    0x07fc8cc4   0x0000004e   Zero   RW         1761    retention_mem_area0  app.o
    0x07fc8d12   0x00000014   Zero   RW         1762    retention_mem_area0  app.o
    0x07fc8d26   0x00000001   Zero   RW         1883    retention_mem_area0  app_task.o
    0x07fc8d27   0x00000001   PAD
    0x07fc8d28   0x0000007a   Zero   RW         1915    retention_mem_area0  app_security.o
    0x07fc8da2   0x00000002   PAD
    0x07fc8da4   0x00000018   Zero   RW         2105    retention_mem_area0  app_easy_security.o
    0x07fc8dbc   0x00000050   Zero   RW         2208    retention_mem_area0  app_easy_timer.o
    0x07fc8e0c   0x00000087   Zero   RW         2283    retention_mem_area0  app_bond_db.o
    0x07fc8e93   0x0000004a   Zero   RW         2556    retention_mem_area0  user_peripheral.o
    0x07fc8edd   0x00000001   Zero   RW         2680    retention_mem_area0  comm_task.o
    0x07fc8ede   0x00000002   Zero   RW         2681    retention_mem_area0  comm_task.o


    Execution Region RET_HEAP (Base: 0x07fc8ee0, Size: 0x00000c40, Max: 0x00000c40, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc8ee0   0x0000040c   Zero   RW          581    heap_db_area        jump_table.o
    0x07fc92ec   0x000002b8   Zero   RW          582    heap_env_area       jump_table.o
    0x07fc95a4   0x0000057c   Zero   RW          584    heap_msg_area       jump_table.o



  Load Region LR_FREE_AREA (Base: 0x07fcb4ac, Size: 0x00000000, Max: 0x000003f0, ABSOLUTE)

    Execution Region ER_FREE_AREA (Base: 0x07fcb4ac, Size: 0x000000a0, Max: 0x000003f0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fcb4ac   0x000000a0   Zero   RW         2482    free_area           user_custs1_impl.o



  Load Region LR_RETAINED_TRNG_STATE (Base: 0x07fcb89c, Size: 0x00000000, Max: 0x00000004, ABSOLUTE)

    Execution Region RET_DATA_UNINIT_TRNG_STATE (Base: 0x07fcb89c, Size: 0x00000004, Max: 0x00000004, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fcb89c   0x00000004   Zero   RW         1249    trng_state          trng.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       520         30          0          0          6      15444   adc_531.o
       988         80        124          0         98      19724   app.o
       344         18          0          0        135       3118   app_bond_db.o
        96          8          0          0          0       1724   app_customs.o
        24          0          0          0          0       1337   app_customs_task.o
       500         26          0          0          0       6025   app_default_handlers.o
        60          6          0          0          0       1286   app_diss.o
       300        120          8          0          0       2683   app_diss_task.o
       600         18          7          0         24      14388   app_easy_security.o
       662         34          0          0         80       6267   app_easy_timer.o
         0          0        196          0          0       2540   app_entry_point.o
        40          4          0          0          0       1287   app_msg_utils.o
       116         12          0          0        122       3908   app_security.o
       356         40         56          0          0       4938   app_security_task.o
       632         34        112          0          1      11174   app_task.o
        12          0          0          0          0       1205   app_utils.o
       784         80          0          0          8      32380   arch_main.o
       348        136         80          0          2       2380   arch_rom.o
       192         18          0          0          4       2905   arch_sleep.o
      1304        104          8          0         60      94019   arch_system.o
       744         70        112          0          0      76632   ble_arp.o
       504          8          9          0          0       3387   comm_manager.o
       384         24         32          0         13       5880   comm_task.o
         0          0         16          0          0       3446   custs1.o
       336         22         80          0          0       6938   custs1_task.o
       540         32          0          0         20      13319   gpio.o
        24         10          0          0          0       4065   hardfault_handler.o
        76         26          0          0          0        475   hash.o
       388         38         28          0          1       4694   hw_otpc_531.o
        68          0        616          0       4172      10710   jump_table.o
        48          6          0          0          0       8587   nmi_handler.o
         0          0         22          0          0       4801   nvds.o
       764         66          0          8         96       4418   otp_cs.o
        16          4          0          0          0       1017   otp_hdr.o
       160         10         36          0         48       3338   prf.o
      2996        100        220         57         36      15551   primitivemanager.o
       724          8          0         18        212       3720   primitivequeue.o
      1228        128          0          1         54       6131   rf_531.o
       108          0          0          0          0       2171   ring_buf.o
       444         36          0          0          8      75238   rwble.o
       492         64          0          0         12      74210   rwip.o
       778         38          0         24          0      23552   serialinterface.o
        80         16        160          0       1536        720   startup_da14531.o
       280         18          0          0          2       2700   syscntl.o
       152         22          0          0          0     284806   system_da14531.o
      2212         82        104          2        688      11184   transportmanager.o
       280          4          0          0         26       2204   transportqueue.o
       124         16          0          0         24       2116   trng.o
      1000         26          0          0       1064      23413   uart.o
       160          8          0          0          8       3339   upperlayerinterface.o
         0          0        570          0          0       1479   user_custs1_def.o
       516         42          8        700        164       8276   user_custs1_impl.o
         0          0         56          0          0        749   user_custs_config.o
        72          4          0          0          0       3338   user_periph_setup.o
      1900        142          0          2         80      12058   user_peripheral.o

    ----------------------------------------------------------------------
     25480       <USER>       <GROUP>        816       8904     937394   Object Totals
         0          0         48          0         80          0   (incl. Generated)
         4          0          6          4         20          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

      1064         54          0          0          0        276   patch.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
       386          8          0          0          0        228   printf1.o
        14          0          0          0          0         60   strlen.o

    ----------------------------------------------------------------------
      1554         <USER>          <GROUP>          0          0        632   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1064         54          0          0          0        276   da14531.lib
       486         24          0          0          0        356   mc_p.l

    ----------------------------------------------------------------------
      1554         <USER>          <GROUP>          0          0        632   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27034       1916       2714        816       8904     933954   Grand Totals
     27034       1916       2714        816       8904     933954   ELF Image Totals
     27034       1916       2714        816          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                29748 (  29.05kB)
    Total RW  Size (RW Data + ZI Data)              9720 (   9.49kB)
    Total ROM Size (Code + RO Data + RW Data)      30564 (  29.85kB)

==============================================================================

