/*
 * TransportQueue.h
 *
 *  Created on: 13-Dec-2023
 *      Author: admin
 */

#ifndef AWT_COMM_MANAGER_TRANSPORTQUEUE_H_
#define AWT_COMM_MANAGER_TRANSPORTQUEUE_H_

#include "Comm_Manager.h"

void InitTriggerQueue(void);
int enqueueTrigger(tt_trigger* _trigger);
int dequeueTrigger(tt_trigger* _trigger);
int isTriggerFull(void);
int isTriggerEmpty(void);

#endif /* AWT_COMM_MANAGER_TRANSPORTQUEUE_H_ */
