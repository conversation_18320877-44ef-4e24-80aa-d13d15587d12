uECC_vli_add = 0x07f02001;
uECC_vli_sub = 0x07f02019;
uECC_vli_mult = 0x07f02035;
set_system_clocks = 0x07f02151;
set_peripheral_clocks = 0x07f0216d;
rf_workaround_init = 0x07f021ad;
get_stack_usage = 0x07f021af;
rwip_eif_get_func = 0x07f021ed;
rwip_set_em_base = 0x07f021fd;
platform_initialization = 0x07f02203;
ble_init = 0x07f022b5;
ble_regs_push = 0x07f02327;
ble_regs_pop = 0x07f0237f;
platform_sleep = 0x07f023d5;
rf_reinit = 0x07f026b9;
smpc_check_param = 0x07f026c1;
smpc_pdu_recv = 0x07f026cb;
lld_sleep_compensate = 0x07f026d5;
lld_sleep_init = 0x07f026df;
lld_sleep_us_2_lpcycles = 0x07f026e9;
lld_sleep_lpcycles_2_us = 0x07f026f3;
uart_flow_off = 0x07f026fd;
uart_finish_transfers = 0x07f02705;
uart_read = 0x07f0270d;
uart_write = 0x07f02715;
UART_Handler = 0x07f0271d;
uart_init = 0x07f02725;
uart_flow_on = 0x07f0272d;
gtl_init = 0x07f02735;
gtl_eif_init = 0x07f0273d;
gtl_eif_read_start = 0x07f02745;
gtl_eif_read_hdr = 0x07f0274d;
gtl_eif_read_payl = 0x07f02755;
gtl_eif_tx_done = 0x07f0275d;
gtl_eif_rx_done = 0x07f02765;
h4tl_init = 0x07f0276d;
h4tl_read_start = 0x07f02775;
h4tl_read_hdr = 0x07f0277d;
h4tl_read_payl = 0x07f02785;
h4tl_read_next_out_of_sync = 0x07f0278d;
h4tl_out_of_sync = 0x07f02795;
h4tl_tx_done = 0x07f0279d;
h4tl_rx_done = 0x07f027a5;
ke_task_init = 0x07f027ad;
ke_timer_init = 0x07f027b5;
llm_encryption_done = 0x07f027bd;
nvds_get = 0x07f027c5;
nvds_del = 0x07f027cd;
nvds_put = 0x07f027d5;
rwip_eif_get = 0x07f027dd;
platform_reset = 0x07f027e5;
lld_test_stop = 0x07f027ef;
lld_test_mode_tx = 0x07f027f9;
lld_test_mode_rx = 0x07f02803;
prf_init = 0x07f0280d;
prf_add_profile = 0x07f02817;
prf_create = 0x07f02821;
prf_cleanup = 0x07f0282b;
prf_get_id_from_task = 0x07f02835;
prf_get_task_from_id = 0x07f0283f;
nvds_init = 0x07f02849;
SetSystemVars = 0x07f02851;
dbg_init = 0x07f0285b;
dbg_platform_reset_complete = 0x07f02865;
hci_rd_local_supp_feats_cmd_handler = 0x07f0286f;
l2cc_pdu_pack = 0x07f0287f;
l2cc_pdu_unpack = 0x07f02895;
l2c_send_lecb_message = 0x07f028ad;
l2c_process_sdu = 0x07f028b7;
l2cc_pdu_recv_ind_handler = 0x07f028c1;
gapc_lecb_connect_cfm_handler = 0x07f028d1;
atts_l2cc_pdu_recv_handler = 0x07f028e1;
attc_l2cc_pdu_recv_handler = 0x07f028eb;
crypto_init = 0x07f028f5;
llm_le_adv_report_ind = 0x07f028ff;
PK_PointMult = 0x07f02909;
llm_p256_start = 0x07f02913;
llm_create_p256_key = 0x07f0291d;
llm_p256_req_handler = 0x07f02929;
llc_le_length_effective = 0x07f0293b;
llc_le_length_conn_init = 0x07f02947;
lld_data_tx_prog = 0x07f02953;
lld_data_tx_check = 0x07f0295f;
llc_pdu_send = 0x07f0296b;
dia_rand = 0x07f02977;
llc_data_notif = 0x07f0298f;
ba431_get_rand = 0x07f029a9;
co_buf_init = 0x07f02a01;
co_buf_rx_free = 0x07f02ac7;
co_buf_rx_buffer_get = 0x07f02ae1;
co_buf_tx_buffer_get = 0x07f02aeb;
co_list_init = 0x07f02b15;
co_list_pop_front = 0x07f02b25;
co_list_flush = 0x07f02b47;
co_list_push_back = 0x07f02b5d;
co_list_pool_init = 0x07f02b81;
co_list_push_front = 0x07f02be9;
co_list_extract = 0x07f02c05;
co_list_find = 0x07f02c55;
co_list_merge = 0x07f02c6b;
co_list_insert_before = 0x07f02c87;
co_list_insert_after = 0x07f02cbf;
co_list_size = 0x07f02cfb;
co_bdaddr_compare = 0x07f02d0f;
llc_init = 0x07f02d2d;
llc_stop = 0x07f02d57;
llc_reset = 0x07f02d9d;
llc_le_length_effective_func = 0x07f02dbf;
llc_le_length_conn_init_func = 0x07f02e53;
llc_le_enh_con_cmp_evt_send = 0x07f02eaf;
llc_le_con_cmp_evt_send = 0x07f02fc9;
llc_start = 0x07f03075;
llc_discon_event_complete_send = 0x07f031e7;
llc_con_update_complete_send = 0x07f03207;
llc_ltk_req_send = 0x07f03241;
llc_feats_rd_event_send = 0x07f03279;
llc_version_rd_event_send = 0x07f032b5;
llc_common_cmd_complete_send = 0x07f032e5;
llc_common_cmd_status_send = 0x07f03303;
llc_common_flush_occurred_send = 0x07f0331f;
llc_common_enc_key_ref_comp_evt_send = 0x07f03337;
llc_common_enc_change_evt_send = 0x07f03353;
llc_common_nb_of_pkt_comp_evt_send = 0x07f033b9;
llc_con_update_ind = 0x07f033d9;
llc_lsto_con_update = 0x07f03453;
llc_map_update_ind = 0x07f03485;
llc_chnl_map_req_send = 0x07f03537;
llc_add_bad_chnl = 0x07f03553;
llc_pdu_send_func = 0x07f035f5;
llc_version_ind_pdu_send = 0x07f03665;
llc_ch_map_update_pdu_send = 0x07f036b5;
llc_pause_enc_req_pdu_send = 0x07f03705;
llc_pause_enc_rsp_pdu_send = 0x07f03751;
llc_enc_req_pdu_send = 0x07f037b5;
llc_enc_rsp_pdu_send = 0x07f0387f;
llc_start_enc_rsp_pdu_send = 0x07f0391b;
llc_reject_ind_pdu_send = 0x07f03971;
llc_con_update_pdu_send = 0x07f03a13;
llc_con_param_req_pdu_send = 0x07f03a69;
llc_con_param_rsp_pdu_send = 0x07f03af7;
llc_feats_req_pdu_send = 0x07f03b85;
llc_start_enc_req_pdu_send = 0x07f03be5;
llc_terminate_ind_pdu_send = 0x07f03c8b;
llc_unknown_rsp_send_pdu = 0x07f03cff;
llc_length_req_pdu_send = 0x07f03d33;
llc_length_rsp_pdu_send = 0x07f03de9;
llc_length_ind = 0x07f03e6f;
llc_ping_req_pdu_send = 0x07f03efb;
llc_ping_rsp_pdu_send = 0x07f03f2b;
llc_feats_req_ind = 0x07f03f5b;
llc_feats_rsp_ind = 0x07f03fbd;
llc_vers_ind_ind = 0x07f04015;
llc_terminate_ind = 0x07f04099;
llc_pause_enc_req_ind = 0x07f040d5;
llc_pause_enc_rsp_ind = 0x07f04105;
llc_enc_req_ind = 0x07f04179;
llc_enc_rsp_ind = 0x07f0421d;
llc_start_enc_req_ind = 0x07f042e1;
llc_start_enc_rsp_ind = 0x07f04343;
llc_cntl_rcv = 0x07f043cb;
llcp_con_param_req_pdu_unpk = 0x07f04457;
llcp_con_param_rsp_pdu_unpk = 0x07f044cf;
llc_con_update_req_ind = 0x07f04547;
llc_ch_map_req_ind = 0x07f045a1;
llc_data_rcv = 0x07f04615;
llc_util_get_free_conhdl = 0x07f063c5;
llc_util_dicon_procedure = 0x07f063f5;
llc_util_gen_skdx = 0x07f06457;
llc_util_update_channel_map = 0x07f0646b;
llc_util_set_llcp_discard_enable = 0x07f0647d;
llc_util_set_auth_payl_to_margin = 0x07f06495;
llc_data_notif_func = 0x07f064bd;
lld_init = 0x07f065c9;
lld_reset = 0x07f06717;
lld_adv_start = 0x07f06765;
lld_adv_stop = 0x07f068b3;
lld_scan_start = 0x07f068d9;
lld_scan_stop = 0x07f06a33;
lld_con_start = 0x07f06a6b;
lld_move_to_master = 0x07f06de3;
lld_con_update_req = 0x07f06e79;
lld_con_update_after_param_req = 0x07f06ef3;
lld_con_param_rsp = 0x07f070d9;
lld_con_param_req = 0x07f071d3;
lld_con_stop = 0x07f072a5;
lld_get_mode = 0x07f072fb;
lld_move_to_slave = 0x07f0731f;
lld_ch_map_ind = 0x07f0749f;
lld_con_update_ind = 0x07f074b1;
lld_crypt_isr = 0x07f074bf;
lld_test_mode_tx_func = 0x07f074c9;
lld_test_mode_rx_func = 0x07f0756b;
lld_test_stop_func = 0x07f075ff;
lld_data_rx_check = 0x07f076bd;
lld_data_rx_flush = 0x07f07701;
lld_data_tx_check_func = 0x07f07725;
lld_data_tx_loop = 0x07f077cb;
lld_data_tx_push = 0x07f077f9;
lld_data_tx_prog_func = 0x07f0785b;
lld_data_tx_flush = 0x07f07991;
lld_evt_drift_compute = 0x07f07aa9;
lld_evt_elt_delete = 0x07f07b87;
lld_evt_deffered_elt_handler = 0x07f08181;
lld_evt_init = 0x07f08253;
lld_evt_init_evt = 0x07f082c9;
lld_evt_elt_insert = 0x07f082e7;
lld_evt_conhdl2elt = 0x07f08311;
lld_evt_schedule_next = 0x07f0832d;
lld_evt_schedule = 0x07f08425;
lld_evt_prevent_stop = 0x07f08461;
lld_evt_canceled = 0x07f08463;
lld_evt_scan_create = 0x07f08487;
lld_evt_move_to_master = 0x07f08579;
lld_evt_update_create = 0x07f08673;
lld_evt_ch_map_update_req = 0x07f087a9;
lld_evt_move_to_slave = 0x07f087c1;
lld_evt_slave_update = 0x07f089f1;
lld_evt_adv_create = 0x07f08a87;
lld_evt_end = 0x07f08b0b;
lld_evt_rx = 0x07f08c19;
lld_evt_timer_isr = 0x07f08c49;
lld_evt_end_isr = 0x07f08c53;
lld_evt_rx_isr = 0x07f08cd3;
lld_sleep_us_2_lpcycles_func = 0x07f08e81;
lld_sleep_lpcycles_2_us_func = 0x07f08e9f;
lld_sleep_enter = 0x07f08f9f;
lld_sleep_wakeup = 0x07f08fdf;
lld_sleep_wakeup_end = 0x07f08ff9;
lld_wlcoex_connection_complete = 0x07f0901d;
lld_wlcoex_remove_connection = 0x07f09035;
lld_wlcoex_set = 0x07f09049;
lld_util_get_bd_address = 0x07f0905d;
lld_util_set_bd_address = 0x07f0907d;
lld_util_freq2chnl = 0x07f090b9;
lld_util_get_local_offset = 0x07f090db;
lld_util_get_peer_offset = 0x07f090f5;
lld_util_connection_param_set = 0x07f09111;
llm_wl_clr = 0x07f09161;
llm_init = 0x07f09189;
llm_common_cmd_complete_send = 0x07f0938d;
llm_ble_ready = 0x07f093a5;
llm_wl_from_rl_restore = 0x07f093ab;
llm_con_req_ind = 0x07f0942d;
llm_resolv_addr = 0x07f096a3;
llm_util_rl_wl_update = 0x07f096e5;
llm_alter_conn = 0x07f0971f;
llm_adv_report_set = 0x07f097c3;
llm_direct_adv_report_set = 0x07f0983d;
llm_encryption_start = 0x07f0987f;
llm_resolv_addr_inplace = 0x07f09903;
llm_le_adv_report_ind_func = 0x07f099d5;
llm_con_req_tx_cfm = 0x07f09ee3;
llm_common_cmd_status_send = 0x07f0a003;
llm_test_mode_start_tx = 0x07f0a01b;
llm_test_mode_start_rx = 0x07f0a10f;
llm_gen_rand_addr = 0x07f0a2e5;
llm_set_adv_param = 0x07f0a151;
llm_set_adv_en = 0x07f0a4d3;
llm_set_adv_data = 0x07f0a7cf;
llm_set_scan_rsp_data = 0x07f0a883;
llm_set_scan_param = 0x07f0a94d;
llm_set_scan_en = 0x07f0a9d3;
llm_wl_dev_add = 0x07f0ab47;
llm_wl_dev_rem = 0x07f0ac21;
llm_wl_from_rl = 0x07f0a3a1;
llm_create_con = 0x07f0ac75;
llm_encryption_done_func = 0x07f0af73;
llm_get_chnl_assess_nb_pkt = 0x07f0b215;
llm_get_chnl_assess_nb_bad_pkt = 0x07f0b21b;
llm_get_min_rssi = 0x07f0b221;
llm_le_scan_report_ind = 0x07f0b22b;
llm_set_tx_oct_time = 0x07f0b2c3;
llm_p256_start_func = 0x07f0b2eb;
llm_create_p256_key_func = 0x07f0b323;
llm_p256_req_handler_func = 0x07f0b3cd;
hci_rd_local_supp_feats_cmd_handler_func = 0x07f0bb57;
llm_util_bd_addr_in_wl = 0x07f0c405;
llm_util_check_address_validity = 0x07f0c47d;
llm_util_check_map_validity = 0x07f0c48d;
llm_util_apply_bd_addr = 0x07f0c4d3;
llm_util_set_public_addr = 0x07f0c4eb;
llm_util_check_evt_mask = 0x07f0c4f9;
llm_util_get_channel_map = 0x07f0c51b;
llm_util_get_supp_features = 0x07f0c529;
llm_util_adv_data_update = 0x07f0c535;
llm_util_bl_check = 0x07f0c559;
llm_util_bl_add = 0x07f0c59b;
llm_util_bl_rem = 0x07f0c5f1;
llm_util_rl_check = 0x07f0c641;
llm_util_rl_add = 0x07f0c677;
llm_util_rl_rem = 0x07f0c6f5;
llm_util_rl_peer_find = 0x07f0c71b;
llm_util_rl_peer_resolv = 0x07f0c747;
llm_util_rl_rpa_find = 0x07f0c799;
PK_PointMult_func = 0x07f0c7c3;
ea_time_get_slot_rounded = 0x07f0c8b9;
ea_init = 0x07f0c985;
ea_elt_create = 0x07f0c9d9;
ea_time_get_halfslot_rounded = 0x07f0c9f3;
ea_elt_insert = 0x07f0ca23;
ea_elt_remove = 0x07f0cc67;
ea_elt_delete = 0x07f0ccef;
ea_interval_create = 0x07f0cd09;
ea_interval_insert = 0x07f0cd1f;
ea_interval_delete = 0x07f0cd2d;
ea_finetimer_isr = 0x07f0cd47;
ea_sw_isr = 0x07f0ce19;
ea_offset_req = 0x07f0ce37;
ea_sleep_check = 0x07f0cff5;
ea_interval_duration_req = 0x07f0d04b;
flash_identify = 0x07f0d16b;
flash_init = 0x07f0d1b9;
flash_erase = 0x07f0d1f5;
flash_write = 0x07f0d259;
flash_read = 0x07f0d2bd;
uart_init_func = 0x07f0d34b;
uart_flow_on_func = 0x07f0d3a9;
uart_flow_off_func = 0x07f0d3b1;
uart_finish_transfers_func = 0x07f0d401;
uart_read_func = 0x07f0d419;
uart_write_func = 0x07f0d42f;
UART_Handler_func = 0x07f0d451;
uart_set_flow_off_retries_limit = 0x07f0d4a3;
gtl_init_func = 0x07f0d7e1;
gtl_enter_sleep = 0x07f0d809;
gtl_exit_sleep = 0x07f0d833;
gtl_send_msg = 0x07f0d83b;
gtl_eif_read_start_func = 0x07f0d8c1;
gtl_eif_read_hdr_func = 0x07f0d8e1;
gtl_eif_read_payl_func = 0x07f0d901;
gtl_eif_tx_done_func = 0x07f0d93f;
gtl_eif_rx_done_func = 0x07f0d94f;
gtl_eif_init_func = 0x07f0da81;
gtl_eif_write = 0x07f0da9b;
gtl_eif_start = 0x07f0dabd;
gtl_eif_stop = 0x07f0dac7;
gtl_env_curr_msg_type_set = 0x07f0dae3;
hci_tl_send = 0x07f0ddc5;
hci_tl_init = 0x07f0de0d;
hci_cmd_get_max_param_size = 0x07f0de31;
hci_cmd_received = 0x07f0de7b;
hci_acl_tx_data_alloc = 0x07f0dfb7;
hci_acl_tx_data_received = 0x07f0e03d;
hci_acl_rx_data_alloc = 0x07f0e105;
hci_acl_rx_data_received = 0x07f0e111;
hci_evt_received = 0x07f0e147;
hci_tl_env_tx_queue_cnt_get = 0x07f0e2f3;
hci_util_pack = 0x07f0e411;
hci_util_unpack = 0x07f0e511;
hci_look_for_cmd_desc = 0x07f0e9f5;
hci_look_for_evt_desc = 0x07f0ea41;
hci_look_for_le_evt_desc = 0x07f0ea63;
hci_evt_mask_set = 0x07f0ea95;
hci_init = 0x07f0eadd;
hci_reset = 0x07f0eaf9;
hci_send_2_host = 0x07f0eb11;
hci_send_2_controller = 0x07f0ebfb;
h4tl_read_start_func = 0x07f0ecd5;
h4tl_read_hdr_func = 0x07f0ecf3;
h4tl_read_payl_func = 0x07f0ed0f;
h4tl_read_next_out_of_sync_func = 0x07f0ed29;
h4tl_out_of_sync_func = 0x07f0ed3d;
h4tl_out_of_sync_check = 0x07f0ed63;
h4tl_tx_done_func = 0x07f0edbf;
h4tl_rx_done_func = 0x07f0edd7;
h4tl_init_func = 0x07f0eefb;
h4tl_write = 0x07f0ef17;
h4tl_start = 0x07f0ef3f;
h4tl_stop = 0x07f0ef47;
h4tl_env_rx_type_set = 0x07f0ef5b;
h4tl_env_hdr_set = 0x07f0ef67;
attc_send_att_req = 0x07f0efa5;
attc_allocate_att_req = 0x07f0efe1;
attc_send_hdl_cfm = 0x07f0f003;
attc_send_execute = 0x07f0f019;
attc_send_read_ind = 0x07f0f033;
attc_l2cc_pdu_recv_handler_func = 0x07f0f973;
attm_convert_to128 = 0x07f0f9cd;
attm_uuid_comp = 0x07f0f9fd;
attm_uuid16_comp = 0x07f0fa55;
attm_is_bt16_uuid = 0x07f0fa61;
attm_is_bt32_uuid = 0x07f0fa87;
attmdb_add_service = 0x07f0fe87;
attmdb_destroy = 0x07f0ff0f;
attmdb_get_service = 0x07f0ff29;
attmdb_get_attribute = 0x07f0ff67;
attmdb_get_next_att = 0x07f0ff9b;
attmdb_uuid16_comp = 0x07f0ffff;
attmdb_att_set_value = 0x07f1003b;
attmdb_get_max_len = 0x07f100e7;
attmdb_get_uuid = 0x07f1014d;
attmdb_get_value = 0x07f10223;
attmdb_att_set_permission = 0x07f10373;
attmdb_att_update_perm = 0x07f103e7;
attmdb_svc_get_permission = 0x07f10455;
attmdb_att_get_permission = 0x07f10473;
attmdb_svc_set_permission = 0x07f10575;
attmdb_init = 0x07f10599;
attmdb_get_nb_svc = 0x07f105af;
attmdb_get_svc_info = 0x07f105c3;
attm_svc_create_db = 0x07f105f7;
attmdb_reserve_handle_range = 0x07f10705;
atts_clear_read_cache = 0x07f10895;
atts_send_error = 0x07f109fd;
atts_write_signed_cfm = 0x07f10a19;
atts_send_event = 0x07f10a59;
atts_clear_prep_data = 0x07f10ae1;
atts_clear_rsp_data = 0x07f10b05;
atts_write_rsp_send = 0x07f10b55;
atts_l2cc_pdu_recv_handler_func = 0x07f1177f;
gattc_cleanup = 0x07f119e7;
gattc_init = 0x07f11a6b;
gattc_update_state = 0x07f11a9d;
gattc_create = 0x07f11ac1;
gattc_con_enable = 0x07f11b35;
gattc_get_mtu = 0x07f11b3b;
gattc_set_mtu = 0x07f11b47;
gattc_get_requester = 0x07f11b8d;
gattc_send_complete_evt = 0x07f11ba9;
gattc_send_error_evt = 0x07f11c05;
gattc_get_operation = 0x07f11c2b;
gattc_get_op_seq_num = 0x07f11c41;
gattc_get_operation_ptr = 0x07f11c57;
gattc_set_operation_ptr = 0x07f11c63;
gattc_reschedule_operation = 0x07f11c6f;
gattc_reallocate_svc = 0x07f11cb5;
gattm_svc_get_start_hdl = 0x07f12c6d;
gattm_init = 0x07f12c73;
gattm_init_attr = 0x07f12c91;
gattm_create = 0x07f12ce5;
gattm_cleanup = 0x07f12ced;
gattm_get_max_mtu = 0x07f12cf5;
gattm_set_max_mtu = 0x07f12cfb;
gattm_get_max_mps = 0x07f12d17;
gattm_set_max_mps = 0x07f12d1d;
l2cc_cleanup = 0x07f12f85;
l2cc_init = 0x07f12fc9;
l2cc_create = 0x07f12ffb;
l2cc_update_state = 0x07f13033;
l2cm_init = 0x07f13405;
l2cm_create = 0x07f13419;
l2cm_cleanup = 0x07f13421;
l2cm_set_link_layer_buff_size = 0x07f13429;
smpc_send_use_enc_block_cmd = 0x07f13439;
smpc_send_start_enc_cmd = 0x07f13471;
smpc_send_ltk_req_rsp = 0x07f134eb;
smpc_send_pairing_req_ind = 0x07f13547;
smpc_send_pairing_ind = 0x07f1362b;
smpc_check_pairing_feat = 0x07f13747;
smpc_launch_rep_att_timer = 0x07f13761;
smpc_check_repeated_attempts = 0x07f1379d;
smpc_check_max_key_size = 0x07f137ff;
smpc_check_key_distrib = 0x07f13845;
smpc_xor = 0x07f13885;
smpc_generate_l = 0x07f1389b;
smpc_generate_ci = 0x07f138e9;
smpc_generate_rand = 0x07f1394d;
smpc_generate_e1 = 0x07f13973;
smpc_generate_cfm = 0x07f13a2d;
smpc_generate_stk = 0x07f13aa7;
smpc_calc_subkeys = 0x07f13afd;
smpc_clear_timeout_timer = 0x07f13b73;
smpc_pairing_end = 0x07f13b9d;
smpc_tkdp_rcp_continue = 0x07f13bfb;
smpc_tkdp_rcp_start = 0x07f13c75;
smpc_pdu_send = 0x07f13cc9;
smpc_tkdp_send_start = 0x07f13d67;
smpc_tkdp_send_continue = 0x07f13df3;
smpc_get_key_sec_prop = 0x07f13e6f;
smpc_is_sec_mode_reached = 0x07f13f3b;
smpc_handle_enc_change_evt = 0x07f13f65;
smpc_pdu_recv_func = 0x07f1401f;
smpc_generate_subkey = 0x07f14089;
leftshift_onebit = 0x07f140bd;
padding = 0x07f140d5;
smpc_generate_subkey_P2 = 0x07f140f9;
AES_CMAC_block = 0x07f1419d;
smpc_generate_f4 = 0x07f1425d;
smpc_generate_g2 = 0x07f14371;
smpc_generate_f5 = 0x07f1441b;
smpc_generate_f5_T = 0x07f1442d;
smpc_generate_f5_P2 = 0x07f1449d;
smpc_generate_f6 = 0x07f1466f;
smpm_send_encrypt_req = 0x07f14809;
smpm_send_gen_rand_nb_req = 0x07f14837;
smpm_check_addr_type = 0x07f1484d;
gapc_update_state = 0x07f148b9;
gapc_get_requester = 0x07f148e9;
gapc_send_complete_evt = 0x07f14905;
gapc_init = 0x07f14a2f;
gapc_con_create = 0x07f14a61;
gapc_con_create_enh = 0x07f14b1b;
gapc_con_cleanup = 0x07f14c15;
gapc_send_disconect_ind = 0x07f14c25;
gapc_get_conidx = 0x07f14c47;
gapc_get_conhdl = 0x07f14c81;
gapc_get_role = 0x07f14c99;
gapc_get_bdaddr = 0x07f14cb5;
gapc_get_csrk = 0x07f14cd5;
gapc_get_sign_counter = 0x07f14cf3;
gapc_send_error_evt = 0x07f14d11;
gapc_get_operation = 0x07f14d33;
gapc_get_operation_ptr = 0x07f14d49;
gapc_set_operation_ptr = 0x07f14d55;
gapc_reschedule_operation = 0x07f14d61;
gapc_get_enc_keysize = 0x07f14d91;
gapc_is_sec_set = 0x07f14da9;
gapc_set_enc_keysize = 0x07f14e35;
gapc_link_encrypted = 0x07f14e49;
gapc_auth_set = 0x07f14e63;
gapc_svc_chg_ccc_get = 0x07f14e83;
gapc_svc_chg_ccc_set = 0x07f14e93;
gapc_check_lecb_sec_perm = 0x07f14ea9;
gapc_search_lecb_channel = 0x07f14f11;
gapc_lecnx_check_tx = 0x07f14f4b;
gapc_lecnx_check_rx = 0x07f14f93;
gapc_lecnx_get_field = 0x07f14fd7;
gapc_process_op = 0x07f1504d;
gapc_param_update_sanity = 0x07f151c7;
gapc_param_cb_con_sanity = 0x07f151ef;
l2cc_pdu_recv_ind_handler_func = 0x07f156ab;
gapc_lecb_connect_cfm_handler_func = 0x07f16653;
gapm_init = 0x07f16a27;
gapm_init_attr = 0x07f16a83;
gapm_get_operation = 0x07f16aaf;
gapm_get_requester = 0x07f16ac1;
gapm_reschedule_operation = 0x07f16ad9;
gapm_send_complete_evt = 0x07f16afb;
gapm_send_error_evt = 0x07f16b31;
gapm_con_create = 0x07f16b51;
gapm_con_enable = 0x07f16bd5;
gapm_con_cleanup = 0x07f16be1;
gapm_get_id_from_task = 0x07f16c11;
gapm_get_task_from_id = 0x07f16c51;
gapm_is_disc_connection = 0x07f16c8d;
gapm_adv_sanity = 0x07f17ab9;
gapm_adv_op_sanity = 0x07f17bad;
gapm_set_adv_mode = 0x07f17d2b;
gapm_set_adv_data = 0x07f17d45;
gapm_execute_adv_op = 0x07f17dd1;
gapm_scan_op_sanity = 0x07f17ef7;
gapm_set_scan_mode = 0x07f17fff;
gapm_execute_scan_op = 0x07f1801d;
gapm_connect_op_sanity = 0x07f180d7;
gapm_basic_hci_cmd_send = 0x07f18257;
gapm_execute_connect_op = 0x07f1826b;
gapm_get_role = 0x07f1840d;
gapm_get_ad_type_flag = 0x07f18415;
gapm_add_to_filter = 0x07f1843b;
gapm_is_filtered = 0x07f184bb;
gapm_update_air_op_state = 0x07f1851f;
gapm_get_irk = 0x07f185e7;
gapm_get_bdaddr = 0x07f185ed;
l2cc_pdu_pack_func = 0x07f18609;
l2cc_pdu_unpack_func = 0x07f18b01;
l2cc_detect_dest = 0x07f18e03;
l2c_process_sdu_func = 0x07f18e5f;
l2c_send_lecb_message_func = 0x07f18f77;
smpc_check_param_func = 0x07f19071;
gapc_hci_handler = 0x07f19c73;
gapm_hci_handler = 0x07f1a8c1;
smpc_pairing_start = 0x07f1a951;
smpc_pairing_tk_exch = 0x07f1a9d7;
smpc_pairing_ltk_exch = 0x07f1aa95;
smpc_pairing_csrk_exch = 0x07f1aae9;
smpc_public_key_exchange_start = 0x07f029d9;
smpc_pairing_rsp = 0x07f1ab3f;
smpc_pairing_req_handler = 0x07f1ac23;
smpc_security_req_send = 0x07f1ac5b;
smpc_encrypt_start = 0x07f1ac85;
smpc_encrypt_start_handler = 0x07f1acab;
smpc_encrypt_cfm = 0x07f1acdd;
smpc_sign_command = 0x07f1ad09;
smpc_sign_cont = 0x07f1ade1;
smpc_calc_confirm_cont = 0x07f1af87;
smpc_confirm_gen_rand = 0x07f1b4c9;
smpc_dhkey_calc_start = 0x07f1b5b3;
smpc_sec_authentication_start = 0x07f1b601;
smpc_dhkey_calc_ind = 0x07f029e5;
smpm_gen_rand_addr = 0x07f1b675;
smpm_resolv_addr = 0x07f1b68d;
smpm_use_enc_block = 0x07f1b6af;
smpm_gen_rand_nb = 0x07f1b6b7;
smpm_ecdh_key_create = 0x07f029f1;
ke_init = 0x07f1b6dd;
ke_flush = 0x07f1b70f;
ke_sleep_check = 0x07f1b74f;
ke_stats_get = 0x07f1b761;
ke_event_init = 0x07f1b77d;
ke_event_callback_set = 0x07f1b789;
ke_event_set = 0x07f1b79d;
ke_event_clear = 0x07f1b7c9;
ke_event_get = 0x07f1b7f5;
ke_event_get_all = 0x07f1b81b;
ke_event_flush = 0x07f1b821;
ke_event_schedule = 0x07f1b829;
ke_mem_init = 0x07f1b879;
ke_mem_is_empty = 0x07f1b8c5;
ke_check_malloc = 0x07f1b905;
ke_malloc = 0x07f1b995;
ke_free = 0x07f1ba8b;
ke_is_free = 0x07f1bb6d;
ke_get_mem_usage = 0x07f1bb7f;
ke_get_max_mem_usage = 0x07f1bb8b;
ke_msg_alloc = 0x07f1bbb1;
ke_msg_send = 0x07f1bbe3;
ke_msg_send_basic = 0x07f1bc0f;
ke_msg_forward = 0x07f1bc1d;
ke_msg_forward_new_id = 0x07f1bc27;
ke_msg_free = 0x07f1bc37;
ke_msg_dest_id_get = 0x07f1bc3f;
ke_msg_src_id_get = 0x07f1bc45;
ke_msg_in_queue = 0x07f1bc4b;
ke_queue_extract = 0x07f1bc5d;
ke_queue_insert = 0x07f1bcad;
ke_task_init_func = 0x07f1be3f;
ke_task_create = 0x07f1be53;
ke_task_delete = 0x07f1be8b;
ke_state_set = 0x07f1beb7;
ke_state_get = 0x07f1bf1b;
ke_msg_discard = 0x07f1bf39;
ke_msg_save = 0x07f1bf3d;
ke_task_msg_flush = 0x07f1bf41;
ke_timer_init_func = 0x07f1c049;
ke_timer_set = 0x07f1c055;
ke_timer_clear = 0x07f1c0e9;
ke_timer_active = 0x07f1c13f;
ke_timer_sleep_check = 0x07f1c165;
rwble_hl_reset = 0x07f1c28b;
rwble_hl_send_message = 0x07f1c2ad;
rwip_check_wakeup_boundary = 0x07f1c2b1;
rwip_init = 0x07f1c2d7;
rwip_reset = 0x07f1c39b;
rwip_version = 0x07f1c3d3;
rwip_schedule = 0x07f1c3db;
rwip_prevent_sleep_set = 0x07f1c47f;
rwip_wakeup = 0x07f1c4a1;
rwip_prevent_sleep_clear = 0x07f1c4b7;
rwip_wakeup_end = 0x07f1c4d9;
rwip_wakeup_delay_set = 0x07f1c4f5;
rwip_sleep_enable = 0x07f1c503;
rwip_ext_wakeup_enable = 0x07f1c509;
rwble_init = 0x07f1c52d;
rwble_reset = 0x07f1c593;
rwble_version = 0x07f1c5c7;
rwble_send_message = 0x07f1c5f3;
YieldToScheduler = 0x07f1c6d9;
xorshift64star = 0x07f1c6e1;
uECC_set_rng = 0x07f1c747;
uECC_get_rng = 0x07f1c74d;
uECC_curve_private_key_size = 0x07f1c753;
uECC_curve_public_key_size = 0x07f1c763;
uECC_vli_clear = 0x07f1c76b;
uECC_vli_isZero = 0x07f1c781;
uECC_vli_testBit = 0x07f1c7a3;
uECC_vli_numBits = 0x07f1c7b5;
uECC_vli_set = 0x07f1c7ef;
uECC_vli_equal = 0x07f1c82d;
uECC_vli_cmp = 0x07f1c851;
uECC_vli_rshift1 = 0x07f1c887;
uECC_vli_square = 0x07f1c8a5;
uECC_vli_modAdd = 0x07f1c8b1;
uECC_vli_modSub = 0x07f1c8df;
uECC_vli_mmod = 0x07f1c8ff;
uECC_vli_modMult = 0x07f1ca09;
uECC_vli_modMult_fast = 0x07f1ca2b;
uECC_vli_modSquare = 0x07f1ca4b;
uECC_vli_modSquare_fast = 0x07f1ca59;
uECC_vli_modInv = 0x07f1ca99;
uECC_secp256r1 = 0x07f1cdd5;
uECC_vli_nativeToBytes = 0x07f1d3d3;
uECC_vli_bytesToNative = 0x07f1d3f5;
uECC_generate_random_int = 0x07f1d433;
uECC_make_key = 0x07f1d495;
uECC_shared_secret = 0x07f1d513;
uECC_compress = 0x07f1d5cf;
uECC_decompress = 0x07f1d5fd;
uECC_valid_point = 0x07f1d66d;
uECC_valid_public_key = 0x07f1d6cf;
uECC_compute_public_key = 0x07f1d703;
uECC_sign = 0x07f1d989;
uECC_sign_deterministic = 0x07f1da6f;
uECC_verify = 0x07f1dbdd;
uECC_curve_num_words = 0x07f1de89;
uECC_curve_num_bytes = 0x07f1de91;
uECC_curve_num_bits = 0x07f1de99;
uECC_curve_num_n_words = 0x07f1dea1;
uECC_curve_num_n_bytes = 0x07f1deb1;
uECC_curve_num_n_bits = 0x07f1dec1;
uECC_curve_p = 0x07f1dec9;
uECC_curve_n = 0x07f1decd;
uECC_curve_G = 0x07f1ded1;
uECC_curve_b = 0x07f1ded5;
uECC_vli_mod_sqrt = 0x07f1ded9;
uECC_vli_mmod_fast = 0x07f1dedf;
uECC_point_mult = 0x07f1dee5;
__aeabi_uidiv = 0x07f1dfb9;
__aeabi_uidivmod = 0x07f1dfb9;
__aeabi_idiv = 0x07f1dfe5;
__aeabi_idivmod = 0x07f1dfe5;
__aeabi_lmul = 0x07f1e00d;
_ll_mul = 0x07f1e00d;
rand = 0x07f1e089;
srand = 0x07f1e09b;
__aeabi_memcpy = 0x07f1e0ad;
__aeabi_memcpy4 = 0x07f1e0ad;
__aeabi_memcpy8 = 0x07f1e0ad;
__aeabi_memset = 0x07f1e0d1;
__aeabi_memset4 = 0x07f1e0d1;
__aeabi_memset8 = 0x07f1e0d1;
__aeabi_memclr = 0x07f1e0df;
__aeabi_memclr4 = 0x07f1e0df;
__aeabi_memclr8 = 0x07f1e0df;
_memset$wrapper = 0x07f1e0e3;
memcmp = 0x07f1e0f5;
__aeabi_uread4 = 0x07f1e10f;
__rt_uread4 = 0x07f1e10f;
_uread4 = 0x07f1e10f;
__aeabi_uwrite4 = 0x07f1e123;
__rt_uwrite4 = 0x07f1e123;
_uwrite4 = 0x07f1e123;
__aeabi_llsl = 0x07f1e135;
_ll_shift_l = 0x07f1e135;
__ARM_common_switch8 = 0x07f1e155;
uart_api = 0x07f1e170;
co_sca2ppm = 0x07f1e180;
co_null_bdaddr = 0x07f1e190;
co_default_bdaddr = 0x07f1e196;
llc_state_handler = 0x07f1e41c;
llc_default_handler = 0x07f1e4ec;
llm_local_cmds = 0x07f1e51c;
llm_local_le_feats = 0x07f1e55c;
llm_local_le_states = 0x07f1e564;
llm_state_handler = 0x07f1e74c;
llm_default_handler = 0x07f1e77c;
LLM_AA_CT1 = 0x07f1e784;
LLM_AA_CT2 = 0x07f1e787;
ecc_p256_G = 0x07f1e789;
gtl_default_state = 0x07f1e7dc;
gtl_default_handler = 0x07f1e7e4;
hci_cmd_desc_tab_lk_ctrl = 0x07f1e7f0;
hci_cmd_desc_tab_ctrl_bb = 0x07f1e814;
hci_cmd_desc_tab_info_par = 0x07f1e88c;
hci_cmd_desc_tab_stat_par = 0x07f1e8c8;
hci_cmd_desc_tab_le = 0x07f1e8d4;
hci_cmd_desc_tab_vs = 0x07f1eb14;
hci_evt_desc_tab = 0x07f1ec88;
hci_evt_le_desc_tab = 0x07f1ecd0;
attc_handlers = 0x07f1ed40;
atts_handlers = 0x07f1edb0;
gattc_default_state = 0x07f1ee30;
gattc_default_handler = 0x07f1ef10;
gattm_default_state = 0x07f1ef5c;
gattm_default_handler = 0x07f1efb4;
l2cc_default_state = 0x07f1efcc;
l2cc_default_handler = 0x07f1efe4;
const_Rb = 0x07f1f005;
const_Zero = 0x07f1f015;
gapc_default_state = 0x07f1f038;
gapc_default_handler = 0x07f1f188;
gapm_default_state = 0x07f1f224;
gapm_default_handler = 0x07f1f30c;
smpc_construct_pdu = 0x07f1f430;
ble_wakeup_executed = 0x07fd7600;
rf_in_sleep = 0x07fd7601;
custom_preinit = 0x07fd7604;
custom_postinit = 0x07fd7608;
custom_appinit = 0x07fd760c;
custom_preloop = 0x07fd7610;
custom_preschedule = 0x07fd7614;
custom_postschedule = 0x07fd7618;
custom_postschedule_async = 0x07fd761c;
custom_presleepcheck = 0x07fd7620;
custom_appsleepset = 0x07fd7624;
custom_postsleepcheck = 0x07fd7628;
custom_presleepenter = 0x07fd762c;
custom_postsleepexit = 0x07fd7630;
custom_prewakeup = 0x07fd7634;
custom_postwakeup = 0x07fd7638;
custom_preidlecheck = 0x07fd763c;
custom_pti_set = 0x07fd7640;
REG_BLE_EM_TX_BUFFER_SIZE = 0x07fd7644;
REG_BLE_EM_RX_BUFFER_SIZE = 0x07fd7648;
_ble_base = 0x07fd764c;
gap_cfg_user = 0x07fd7650;
rom_func_addr_table = 0x07fd7654;
rom_cfg_table = 0x07fd7658;
length_exchange_needed = 0x07fd7674;
enh_con_cmp_cnt = 0x07fd7678;
rx_pkt_cnt = 0x07fd7680;
rx_pkt_cnt_bad = 0x07fd7684;
rx_pkt_cnt_bad_adv = 0x07fd7688;
rx_pkt_cnt_bad_scn = 0x07fd768c;
rx_pkt_cnt_bad_oth = 0x07fd7690;
rx_pkt_cnt_bad_wo_sync_err = 0x07fd7694;
rx_pkt_cnt_bad_con = 0x07fd7698;
connect_req_cnt = 0x07fd769c;
last_status = 0x07fd76a0;
llc_state = 0x07fd76a4;
lld_wlcoex_enable = 0x07fd76b0;
ble_duplicate_filter_max = 0x07fd76b4;
ble_duplicate_filter_found = 0x07fd76b5;
alter_conn_adv_all_cnt = 0x07fd76b8;
alter_conn_adv_dir_cnt = 0x07fd76bc;
alter_conn_adv_cnt = 0x07fd76c0;
create_conn_cnt = 0x07fd76c4;
alter_conn_cnt = 0x07fd76c8;
alter_conn_restart_cnt = 0x07fd76cc;
alter_conn_peer_addr = 0x07fd76d0;
alter_conn_local_addr = 0x07fd76d6;
set_adv_data_discard_old = 0x07fd76dc;
llm_resolving_list_max = 0x07fd76dd;
llm_bt_env = 0x07fd76de;
init_tx_cnt_cntl_cnt1 = 0x07fd76e8;
init_tx_cnt_cntl_cnt = 0x07fd76ec;
tx_cnt_cntl_cnt = 0x07fd76f0;
llm_state = 0x07fd76f4;
gtl_state = 0x07fd76f8;
use_h4tl = 0x07fd76f9;
hci_cmd_desc_root_tab = 0x07fd76fc;
gattc_state = 0x07fd772c;
gattm_state = 0x07fd7734;
l2cc_state = 0x07fd7735;
l2cm_env = 0x07fd773e;
gapc_state = 0x07fd7744;
gapm_state = 0x07fd774c;
ke_free_bad = 0x07fd7754;
rwip_env = 0x07fd7758;
ble_reg_save = 0x07fd7764;
sleep_env = 0x07fd77b4;
uart_env = 0x07fd77b8;
ke_mem_heaps_used = 0x07fd77dc;
co_buf_env = 0x07fd77e0;
llc_env = 0x07fd7918;
lld_evt_env = 0x07fd7938;
llm_le_env = 0x07fd7964;
llm_resolving_list = 0x07fd7a60;
gtl_env = 0x07fd7cf8;
hci_env = 0x07fd7d40;
gattc_env = 0x07fd7d68;
gattm_env = 0x07fd7d88;
l2cc_env = 0x07fd7dac;
ecdh_key = 0x07fd7dcc;
gapc_env = 0x07fd7e2c;
gapm_env = 0x07fd7e4c;
ke_env = 0x07fd7e78;
rwip_rf = 0x07fd7fb4;
dia_srand = 0x07f02983;
rom_hci_cmd_desc_root_tab = 0x07f1ec58;
modulationGainCalibration_func = 0x07f0d595;
DCoffsetCalibration_func = 0x07f0d68b;
PllLF_IFF_MIX_CapCalibration_func = 0x07f0d6d7;
BLE_TX_DESC_DATA_USER = 0x07fd765c;
BLE_TX_DESC_CNTL_USER = 0x07fd7660;
LLM_LE_ADV_DUMMY_IDX = 0x07fd7664;
LLM_LE_SCAN_CON_REQ_ADV_DIR_IDX = 0x07fd7668;
LLM_LE_SCAN_RSP_IDX = 0x07fd766c;
LLM_LE_ADV_IDX = 0x07fd7670;
smpc_public_key_exchange_start_func = 0x07f1b58f;
smpc_dhkey_calc_ind_func = 0x07f1b62f;
smpm_ecdh_key_create_func = 0x07f1b6bf;
ecdh_key_creation_in_progress = 0x07fd7750;
lld_sleep_env = 0x07fd76ac;
h4tl_env = 0x07fd7d50;
ea_env = 0x07fd7cb8;
modulationGainCalibration = 0x07f029b5;
DCoffsetCalibration = 0x07f029c1;
PllLF_IFF_MIX_CapCalibration = 0x07f029cd;

/* symbols used by patch library */
atts_mtu_exc_req = 0x07f10b75;
atts_allocate_pdu = 0x07f11957;
atts_send_pdu = 0x07f11979;
llcp_length_req_handler = 0x07f05fe3;
smpc_construct_id_addr_info_pdu = 0x07f19157;
lld_evt_channel_next = 0x07f07cc9;
l2cc_connor_pkt_format = 0x07f1f314;
l2cc_signaling_pkt_format = 0x07f1f31c;
l2cc_security_pkt_format = 0x07f1f378;
l2cc_attribute_pkt_format = 0x07f1f3b4;
