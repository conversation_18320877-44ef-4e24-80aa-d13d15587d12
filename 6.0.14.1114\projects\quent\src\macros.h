#ifndef __MACROS
#define __MACROS

#include <stdint.h>

# define PARAM_ID			    0x00
# define PARAM_VAL_MSB		0x01
# define PARAM_VAL_LSB		0x02
# define TIMESTAMP_3			0x03
# define TIMESTAMP_2			0x04
# define TIMESTAMP_1			0x05
# define TIMESTAMP_0			0x06
# define ORIGIN_TYPE			0x07
# define TYPE_OFFSET			0x02

//typedef enum identifier{

//	
//	sensor = 1,
//	scheduler = 2,
//	conn_type = 3,
//	user_info = 4,
//	misc = 5,

//}identifier_t;

/*typedef enum vitals{


	heart_rate = 1,
	spo2,
	cbt,
	skin_temp,
	bp,
	ecg_samples,
	ppg_samples

}vitals_t;
*/

/*typedef enum connection_status{
	connected,
	disconnected,
	passkey,
	bond_info
}conn_type_t;

typedef enum miscellaneous{
time_sync,
mac_id,
step_count,
step_goal,
batt_perct,


}misc_t;
*/

/*enum _Indication_Type_Sub
{
	// Id's from 0x0000 to 0x0028 are reserved for system related functions
		// VITALS START
		i_NONE                  = 0x0028,
		i_wHR                   = 0x0029,
		i_wSPO2                 = 0x002A,
		i_wCBT                  = 0x002B,
		i_wskintemp             = 0x002C,
		i_wBP                   = 0x002D,
		i_wAlgoHr               = 0x002E,
		i_wECG                  = 0x002F,
		i_wrawData              = 0x0030,
		// enum from 8-20 are reserved for raw data

		// CONNECTION PARAMETERS START
		i_wbleconn              = 0x003D,
		i_wblediscon            = 0x003E,
		i_wblepasskey           = 0x003F,
		i_wblebondinfo          = 0x0040,
		i_wblemacid             = 0x0041,
		// enum from 25-40 is related for connection parameters.

		// CRITICAL PARAMETERS START
		i_wsos                  = 0x0051,
		i_wfall                 = 0x0052,

		// OTHER SYSTEM PARAMETERS START
		i_wbatt                 = 0x0053,
		i_wstepcount            = 0x0054,
		i_wStepgoal             = 0x0055,
		i_wtimesync             = 0x0056,
		i_wUpdate               = 0x0057,
		i_wValidBuild						= 0x0058,
		i_wOtaReset							= 0x0059,
		i_wVitalBusy						= 0x005A,
		i_wScheduler						= 0x005B,
		i_wFactory_Rst          = 0x005C,
		i_wStepnctRst           = 0x005D,
		i_wUcConigwrite         = 0x005E,
		i_wSetupWrComplete      = 0x005F,  
		i_wStepgoalSet          = 0x0060,
		i_wSchedlrControl       = 0x0061,
		i_wSchedulerTimeChange  = 0x0062,
		i_wChargerConnected     = 0x0063,
		
		
		// CONFIG PARAMETERS START
		i_wAlerts               = 0x006F,
		// USER CoNFIG PARAMETERS START
		i_wUconfig               = 0x0070,
		i_wUcWeight             = 0x0071,
		i_wUcHeight             = 0x0072,
		i_wUcCountry            = 0x0073,
		i_wUcPhNO               = 0x0074,
		i_wUcAddr               = 0x0075,
		i_wUcVitalsRng          = 0x0076,
		i_wUcID                 = 0x0077,
		i_wUcWatchId            = 0x0078,
		i_wUcConfig             = 0x0079,
		i_wUcWifiCred1          = 0x007A,
		i_wUcWifiCred2          = 0x007B,
		i_wUcWifiCred3          = 0x007C,
		i_wUcWifiCred4          = 0x007D,
		i_wUcWifiCred5          = 0x007E,
		i_wUcWifiCred6          = 0x007F,
		i_wUcWifiCred7          = 0x0080,
		i_wUcWifiCred8          = 0x0081,
		i_wUcWifiCred9          = 0x0082,
		i_wUcWifiCred10         = 0x0083,
		i_wUcSosAnalysis        = 0x0084,
		i_wUcDiagData           = 0x0085,
		i_wUcDevPref            = 0x0086,
		i_wUcPasscode           = 0x0087,
		i_wUcDevName            = 0x0088,
		i_wUcTimeZone           = 0x0089,
		i_wUcHrCoeff            = 0x008A,
		i_wUcSpo2Coeff          = 0x008B,
		i_wUcCbtCoeff           = 0x008C,
		i_wUcSbpCoeff           = 0x008D,
		i_wUcDbpCoeff           = 0x008E,
		i_wUcEcgCoeff           = 0x008F,
		i_wUcPpgCoeff           = 0x0090,
		i_UcSchdlrTime          = 0x0091,
		// Id's From 0x007C to 0xFFFF are unused.

	// Id's From 0x007C to 0xFFFF are unused.
};

enum _Command_Type_Sub
{
	// Id's from 0x0000 to 0x0028 are reserved for system related functions
	// VITALS START
	c_NONE                  = 0x0028,
	c_wHR                   = 0x0029,
	c_wSPO2                 = 0x002A,
	c_wCBT                  = 0x002B,
	c_wskintemp             = 0x002C,
	c_wBP                   = 0x002D,
	c_wAlgoHr               = 0x002E,
	c_wECG                  = 0x002F,
	c_wrawData              = 0x0030,
	// enum from 8-20 are reserved for raw data

	// CONNECTION PARAMETERS START
	c_wbleconn              = 0x003D,
	c_wblediscon            = 0x003E,
	c_wblepasskey           = 0x003F,
	c_wblebondinfo          = 0x0040,
	c_wblemacid             = 0x0041,
	// enum from 25-40 is related for connection parameters.

	// CRITICAL PARAMETERS START
	c_wsos                  = 0x0051,
	c_wfall                 = 0x0052,

	// OTHER SYSTEM PARAMETERS START
	c_wbatt                 = 0x0053,
	c_wstepcount            = 0x0054,
	c_wStepgoal             = 0x0055,
	c_wtimesync             = 0x0056,
	c_wUpdate               = 0x0057,
	c_wValidBuild						= 0x0058,
	c_wOtaReset							= 0x0059,
	c_wVitalBusy						= 0x005A,
	c_wScheduler						= 0x005B,
	c_wFactory_Rst          = 0x005C,
	c_wStepnctRst           = 0x005D,
	c_wUcConigwrite         = 0x005E,
	c_wSetupWrComplete      = 0x005F,
	c_wStepgoalSet          = 0x0060,
	c_wSchedlrControl       = 0x0061,
	c_wSchedulerChangeTime  = 0x0062,
	c_wChargerConnected     = 0x0063,

	// CONFIG PARAMETERS START
	c_wAlerts               = 0x006F,
	// USER CoNFIG PARAMETERS START
	c_wUconfig              = 0x0070,
	c_wUcWeight             = 0x0071,
	c_wUcHeight             = 0x0072,
	c_wUcCountry            = 0x0073,
	c_wUcPhNO               = 0x0074,
	c_wUcAddr               = 0x0075,
	c_wUcVitalsRng          = 0x0076,
	c_wUcID                 = 0x0077,
	c_wUcWatchId            = 0x0078,
	c_wUcConfig             = 0x0079,
	c_wUcWifiCred1          = 0x007A,
	c_wUcWifiCred2          = 0x007B,
	c_wUcWifiCred3          = 0x007C,
	c_wUcWifiCred4          = 0x007D,
	c_wUcWifiCred5          = 0x007E,
	c_wUcWifiCred6          = 0x007F,
	c_wUcWifiCred7          = 0x0080,
	c_wUcWifiCred8          = 0x0081,
	c_wUcWifiCred9          = 0x0082,
	c_wUcWifiCred10         = 0x0083,
	c_wUcSosAnalysis        = 0x0084,
	c_wUcDiagData           = 0x0085,
	c_wUcDevPref            = 0x0086,
	c_wUcPasscode           = 0x0087,
	c_wUcDevName            = 0x0088,
	c_wUcTimeZone           = 0x0089,
	c_wUcHrCoeff            = 0x008A,
	c_wUcSpo2Coeff          = 0x008B,
	c_wUcCbtCoeff           = 0x008C,
	c_wUcSbpCoeff           = 0x008D,
	c_wUcDbpCoeff           = 0x008E,
	c_wUcEcgCoeff           = 0x008F,
	c_wUcPpgCoeff           = 0x0090,
	c_UcSchdlrTime          = 0x0091,
	// Id's From 0x007C to 0xFFFF are unused.
};
*/

/*
* These enums are as per the ID assigned to the vitals mentioned in the Ble communication spec.
* These Id's are used when the GATT Client wants to make a request/enquiry on a particular feild
*/
typedef enum
{
 HEART_RATE 	    			=	0x01,
 BLOOD_OXYGEN 		    	=	0x02,
 BODY_TEMPERATURE 		  =	0x03,
 SKIN_TEMPERATURE 			= 0x04,
 BATTERY_PERCENTAGE 		=	0x05,
 BLOOD_PRESSURE 			  =	0x06,
 PEDOMETER_SENSOR 			= 0x07,
 STEP_GOAL 					    = 0x08,
 TIME_Sync 					    = 0x09,
 SOS 						        = 0x0A,
 BLE_MACID              = 0x0B,
 UPDATE_HANDLER_OTA 		=	0x0C,
 SETUP_CONFIG_WRITTEN   = 0x0D,
 VITAL_ERROR						= 0x0E,
 VITAL_OUT_RANGE				= 0x0F,
 VITAL_BUSY             = 0x10,	
 CHECK_UPDATE						= 0x12,
 INIT_UPDATE						= 0x13,
 FACTORY_RST						= 0x14,
 CONFIG_DOWNLOAD				= 0x15,
 SETUP_CONFIG           = 0x0D,   
 EKG                    = 0xAA,
 ALGO_HR                = 0x16,
 _END
}write_to_watch_t;

#if 0
 #define HEART_RATE 	    			(uint8_t)(	0x01)
 #define BLOOD_OXYGEN 		    	(uint8_t)(	0x02)
 #define BODY_TEMPERATURE 		  (uint8_t)(	0x03)
 #define SKIN_TEMPERATURE 			(uint8_t)( 0x04)
 #define BATTERY_PERCENTAGE 		(uint8_t)(	0x05)
 #define BLOOD_PRESSURE 			  (uint8_t)(	0x06)
 #define PEDOMETER_SENSOR 			(uint8_t)( 0x07)
 #define STEP_GOAL 					    (uint8_t)( 0x08)
 #define TIME_Sync 					    (uint8_t)( 0x09)
 #define SOS 						        (uint8_t)( 0x0A)
 #define ACKNOWLEDMENT          (uint8_t)( 0x0B)
 #define UPDATE_HANDLER_OTA 		(uint8_t)(	0x0C)
 #define SETUP_CONFIG_WRITTEN   (uint8_t)( 0x0D)
 #define VITAL_ERROR						(uint8_t)( 0x0E)
 #define VITAL_OUT_RANGE				(uint8_t)( 0x0F)
 #define VITAL_BUSY             (uint8_t)( 0x10)	
 #define CHECK_UPDATE						(uint8_t)( 0x12)
 #define INIT_UPDATE						(uint8_t)( 0x13)
 #define FACTORY_RST						(uint8_t)( 0x14)
 #define CONFIG_DOWNLOAD				(uint8_t)( 0x15)
 #define SETUP_CONFIG           (uint8_t)( 0x0D)  
 #define EKG                    (uint8_t)( 0xAA)
 #define ALGO_HR                (uint8_t)( 0x16)
 #define _END										(uint8_t)( 0x17)
#endif

#define SOS_IND        0x0C
#define WATCH_STATUS	 0x0A
#define STEP_COUNT_RST 0x0B
#define FALL_IND 			 0x0D


/***************************************************************/
/*typedef enum STATE
{
	REQUEST_HR = 0,
	HR_WAIT_INTERVAL = 1,
	REQUEST_SPO2 = 2,
	SPO2_WAIT_INTERVAL = 3,
	REQUEST_TEMPERATURE = 4,
	TEMPERATURE_WAIT_INTERVAL = 5,
	START_WAIT_INTERVAL = 6,
} state_t;*/

/*typedef struct schedulerState
{
	state_t NextState;
	state_t currentState;
} schedulerState_t;
*/
/***************************************************************/


#endif
