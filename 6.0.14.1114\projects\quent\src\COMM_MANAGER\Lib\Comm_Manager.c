#include"Comm_Manager.h"
#include"PrimitiveQueue.h"
#include"SerialInterface.h"
#include"TransportQueue.h"
#include"Comm_Debug.h"



#if COMM_MANAGER_DEBUG
#define DEBUG_PRINTS
#else
#undef DEBUG_PRINTS
#endif

#ifdef ST
static w_trigger_t wTrigger = w_invalid;
#endif

void print_comm_queue(Comm_Queue_t* msg)
{
	(void)msg;
#ifdef DEBUG_PRINTS
	COM_PRINTI("module:%d\r\n", msg->module);
	if(msg->module == ul || msg->module == ul_rt)
	{
		COM_PRINTI("primitive trigger:%d primitive subid:%d\n", msg->event.pt.trigger, msg->event.pt.sub_id);
	}
	else if(msg->module == m_wireless)
	{
		COM_PRINTI("wireless trigger:%d \n", msg->event.tt.trigger);
	}
#endif
}

#ifdef COMM_MANAGER_TEST
void preparetest(void);
#endif


void COM_RXEvent()
{
	Comm_Queue_t msg;
	msg.module 			= m_isr;
	msg.event.tt.trigger	= rx_event;
	triggerCommMngr(&msg);

}

void COM_TXCEvent()
{
	Comm_Queue_t msg;
	msg.module 			= m_isr;
	msg.event.tt.trigger	= tx_comp;
	triggerCommMngr(&msg);
}

void COM_BreakEvent()
{
	Comm_Queue_t msg;
	msg.module 			= m_isr;
	msg.event.tt.trigger	= t_break;
	triggerCommMngr(&msg);
}

/*API expose by comm manager to */

void Command_Status(uint32_t status)
{
	Comm_Queue_t queue;
	queue.module 			= ul;
	queue.event.pt.trigger  = status?cr_exec_s:cr_exec_f;
	queue.event.pt.len 		= 0;
	if(status == ERR_VITAL_DECLINE)
	{
		queue.event.pt.payload 	= "\xfe";
	}
	else if(status == ERR_VITAL_BUSY)
	{
		queue.event.pt.payload 	= "\xff";
	}
	else
	{
		queue.event.pt.payload 	= 0;
	}
	queue.event.pt.sub_id	= comm_NONE;
	COM_PRINTI("In  Command_Status %ld \n",status);
	triggerCommMngr(&queue);

}


void Response_Status(uint32_t status)
{
	Comm_Queue_t queue;
	queue.module 			= ul_rt;
	queue.event.pt.trigger  = status?cr_exec_s:cr_exec_f;
	queue.event.pt.len 		= 0;
	queue.event.pt.payload 	= 0;
	queue.event.pt.sub_id	= comm_NONE;
	COM_PRINTI("In  Response_Status %ld \n",status);
	triggerCommMngr(&queue);
}

void Indication_Status(uint32_t status)
{
//	return ;
	Comm_Queue_t queue;
	queue.module 			= ul_rt;
	queue.event.pt.trigger  = status?ind_exec_s:ind_exec_f;
	queue.event.pt.len 		= 0;
	queue.event.pt.payload 	= 0;
	queue.event.pt.sub_id	= comm_NONE;
	COM_PRINTI("In  Indication_Status %ld \n",status);
    triggerCommMngr(&queue);
}

void Bufferxfer_Status(uint32_t status)
{
	Comm_Queue_t queue;
	queue.module 			= ul_rt;
	queue.event.pt.trigger  = status?bufferxfer_exec_s:bufferxfer_exec_f;
	queue.event.pt.len 		= 0;
	queue.event.pt.payload 	= 0;
	queue.event.pt.sub_id	= comm_NONE;
	COM_PRINTI("In  Bufferxfer_Status %ld \n",status);
	triggerCommMngr(&queue);
}

int32_t Com_Get_Data(enum comm_subid subid)
{
	Comm_Queue_t msg;
	msg.event.pt.trigger = cmd_tx;
	msg.module 		 = ul;
	msg.event.pt.sub_id = subid;
	msg.event.pt.retry   = 3;
	COM_PRINTI("In  Com_Get_Data %d \n",subid);
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_Send_Data(enum comm_subid subid,uint8_t* data, uint16_t len)
{
	Comm_Queue_t msg;
	msg.module 			 = ul;
	msg.event.pt.sub_id  = subid;
	msg.event.pt.payload = data;
	msg.event.pt.len	 = len;
	msg.event.pt.trigger = ind_tx;
	msg.event.pt.retry   = 3;
	COM_PRINTI("In  Com_Send_Data ->triggerCommMngr %d \n",subid);
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_BufferXfer(enum comm_subid subid,uint8_t* data, uint16_t len)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));

	msg.module 			 = ul;
	msg.event.pt.sub_id  = subid;
	msg.event.pt.payload = data;
	msg.event.pt.len	 = len;
	msg.event.pt.trigger = buff_tx;
	msg.event.pt.retry   = 3;
	COM_PRINTI("In  Com_BufferXfer ->triggerCommMngr %d \n",subid);
	triggerCommMngr(&msg);
	return 1;
}


int32_t resetCommManager(void)
{	
	resetPrimitiveManager();
	resetTransportManager();
  Close_Port();
	return 1;
}

#ifdef ST

static uint32_t Handle_Wireless_Trigger(w_trigger_t trigger)
{
	switch(trigger)
	{
	case w_enable_ble:
	case w_disable_wifi:
		resetCommManager();
		switchOnBle();
		break;
	case w_disable_ble:
	case w_enable_wifi:
		resetCommManager();
		switchOnWifi();
		break;
	case w_disable_wireless:
		break;
	case w_ble_boot_success:
		BleBootSuccess();
		break;
	case w_ble_boot_failure:
		break;
	case w_wifi_boot_success:
		break;
	case w_wifi_boot_failure:
		break;
	case w_wifi_scan_initiate:
		break;
	default:
		break;

	}

	return 1;
}
#endif

static uint32_t Handle_Message(Comm_Queue_t msg)
{

	COM_PRINTI("In  Handle_Message \n");
	print_comm_queue(&msg);

	if(msg.module == m_self)
	{
		UpdatePrimitiveTriggerRetry(&msg.event.pt);
	}
	else if(msg.module == ul)
	{
		COM_PRINTI(" Setting TriggerUL %d %p %d %d \n",msg.event.pt.trigger,msg.event.pt.payload,msg.event.pt.len, msg.event.pt.sub_id);
		SetPrimitiveTrigger_UL(msg.event.pt.trigger, msg.event.pt.payload,msg.event.pt.len, msg.event.pt.sub_id, msg.event.pt.retry);
	}
	else if (msg.module == ul_rt)
	{

		COM_PRINTI(" Setting TriggerULRT %d %p %d %d\n",msg.event.pt.trigger, msg.event.pt.payload,msg.event.pt.len, msg.event.pt.sub_id);
		SetPrimitiveTrigger_RT(msg.event.pt.trigger,msg.event.pt.payload,msg.event.pt.len, msg.event.pt.sub_id);
	}
#ifdef ST
	else if(msg.module == m_wireless)
	{
		wTrigger = msg.event.wt;
		COM_PRINTI("Wireless Trigger %d \n",msg.event.wt);
		Handle_Wireless_Trigger(msg.event.wt);
	}
#endif
	else
	{
		COM_PRINTI("TRansport Trigger %d \n",msg.event.tt.trigger);
		SetTransportTrigger(msg.event.tt.trigger);
	}

	COM_PRINTI("Exiting Handle_Message TRUE \n");

	return 1;
}

uint32_t RunMainLoop(Comm_Queue_t msg)
{
	enum _ttrigger transport = t_invalid;
	pe_trigger primitive = {comm_NONE, p_invalid, 0, 0, 0};

	Handle_Message(msg);

	do
	{

		COM_PRINTI("Looping in RunMainLoop Primitive[%d] Transport[%d] \n",primitive.trigger,transport);

		if(primitive.trigger != p_invalid)
		{

			COM_PRINTI("Calling ->PrimitiveMain Primitive[%d] Transport[%d] \n",primitive.trigger,transport);
			PrimitiveMain(&primitive);
		}

		transport = GetTransportTrigger();
		if(transport != t_invalid)
		{

			COM_PRINTI("Calling ->TransportMain Primitive[%d] Transport[%d] \n",primitive.trigger,transport);
			TransportMain(transport);
		}

		if(!GetPrimitiveTrigger(&primitive))
		{
			primitive.trigger = p_invalid;
		}

		transport = GetTransportTrigger();

	}while(primitive.trigger  != p_invalid || transport != t_invalid);

	COM_PRINTI("Exiting RunMainLoop\n");
	return 0;
}



void WriteCmd (struct _Exchange* pcommand,enum comm_subid c_subid)
{
		memset(pcommand, 0, sizeof(struct _Exchange));
		pcommand->ID       = COMMAND;
		pcommand->SUB_ID   = c_subid;
		pcommand->size     = 0;
}

#ifdef COMM_MANAGER_TEST
//# include "Comm_Manager_Test.c"
#endif

