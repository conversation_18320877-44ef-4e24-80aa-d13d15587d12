--cpu Cortex-M0+
".\out_da14531\objects\system_da14531.o"
".\out_da14531\objects\startup_da14531.o"
".\out_da14531\objects\hardfault_handler.o"
".\out_da14531\objects\nmi_handler.o"
".\out_da14531\objects\nvds.o"
".\out_da14531\objects\arch_main.o"
".\out_da14531\objects\jump_table.o"
".\out_da14531\objects\arch_sleep.o"
".\out_da14531\objects\arch_system.o"
".\out_da14531\objects\arch_hibernation.o"
".\out_da14531\objects\arch_rom.o"
".\out_da14531\objects\chacha20.o"
".\out_da14531\objects\hash.o"
"..\..\..\sdk\platform\system_library\output\Keil_5\da14531.lib"
".\out_da14531\objects\otp_cs.o"
".\out_da14531\objects\otp_hdr.o"
".\out_da14531\objects\syscntl.o"
".\out_da14531\objects\gpio.o"
".\out_da14531\objects\hw_otpc_531.o"
".\out_da14531\objects\uart.o"
".\out_da14531\objects\trng.o"
".\out_da14531\objects\wlan_coex.o"
".\out_da14531\objects\adc_531.o"
".\out_da14531\objects\rwble.o"
".\out_da14531\objects\rwip.o"
".\out_da14531\objects\rf_585.o"
".\out_da14531\objects\ble_arp.o"
".\out_da14531\objects\rf_531.o"
".\out_da14531\objects\attm_db_128.o"
".\out_da14531\objects\prf_utils.o"
".\out_da14531\objects\custom_common.o"
".\out_da14531\objects\diss.o"
".\out_da14531\objects\diss_task.o"
".\out_da14531\objects\custs1.o"
".\out_da14531\objects\custs1_task.o"
".\out_da14531\objects\prf.o"
".\out_da14531\objects\app_default_handlers.o"
".\out_da14531\objects\app.o"
".\out_da14531\objects\app_task.o"
".\out_da14531\objects\app_security.o"
".\out_da14531\objects\app_security_task.o"
".\out_da14531\objects\app_diss.o"
".\out_da14531\objects\app_diss_task.o"
".\out_da14531\objects\app_entry_point.o"
".\out_da14531\objects\app_msg_utils.o"
".\out_da14531\objects\app_easy_msg_utils.o"
".\out_da14531\objects\app_easy_security.o"
".\out_da14531\objects\app_easy_timer.o"
".\out_da14531\objects\app_customs.o"
".\out_da14531\objects\app_customs_task.o"
".\out_da14531\objects\app_customs_common.o"
".\out_da14531\objects\app_bond_db.o"
".\out_da14531\objects\app_utils.o"
".\out_da14531\objects\app_easy_whitelist.o"
".\out_da14531\objects\user_custs_config.o"
".\out_da14531\objects\user_custs1_def.o"
".\out_da14531\objects\user_periph_setup.o"
".\out_da14531\objects\user_custs1_impl.o"
".\out_da14531\objects\user_peripheral.o"
".\out_da14531\objects\scheduler.o"
".\out_da14531\objects\ring_buf.o"
".\out_da14531\objects\comm_task.o"
".\out_da14531\objects\serialinterface.o"
".\out_da14531\objects\upperlayerinterface.o"
".\out_da14531\objects\comm_manager.o"
".\out_da14531\objects\primitivemanager.o"
".\out_da14531\objects\primitivequeue.o"
".\out_da14531\objects\transportmanager.o"
".\out_da14531\objects\transportqueue.o"
--library_type=microlib --strict --scatter "..\..\..\sdk\common_project_files\scatterfiles\DA14531.sct"
--feedback=".\unused_531.txt" ..\..\..\sdk\common_project_files\misc\da14531_symbols.txt --symdefs=quent_531_symdef.txt --any_placement=best_fit --datacompressor off --summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\out_DA14531\Listings\quent_531.map" -o .\out_DA14531\Objects\quent_531.axf