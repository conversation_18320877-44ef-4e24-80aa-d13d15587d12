#! armcc -E -I ..\src\config,..\..\..\sdk\common_project_files\ --cpu Cortex-M0+

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;                                       RAM                                                   ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;+******************************+******************************+******************************+
;+ 1st RAM block (16KB)         + 2nd RAM block (12KB)         + 3rd RAM block (20KB)         +
;+******************************+******************************+******************************+
;                                                                        ^             ^      ;
;                                                                        |             |      ;
;                                                                 RET_MEM_BASE_ADDR    |      ;
;                                                                                      |      ;
;                                                                              __SCT_BLE_BASE ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Comments:                                                                                   ;
; 1. The __SCT_BLE_BASE address is always in the RAM3 block.                                  ;
; 2. The RET_MEM_BASE_ADDR address can be (theoretically) in any RAM block.                   ;
; 3. The FREE_AREA_BASE_ADDR address is always in the RAM3 block.                             ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

; Scatter file definitions required by the inclusion of the da1458x_scatter_config.h (do not remove)
#define ARM_SCATTERFILE

; Define the DA14531 chip (must be defined - do not remove)
#define __DA14531__

#include "da1458x_config_basic.h"
#include "da1458x_config_advanced.h"
#include "da1458x_scatter_config.h"

; Macro to align val on the multiple of 4 equal or nearest higher
#define ALIGN4_HI(val) (((val)+3) AND (~3))

#if !defined(CFG_RET_DATA_SIZE)
    #error "CFG_RET_DATA_SIZE is not defined!"
#endif

#if !defined(CFG_RET_DATA_UNINIT_SIZE)
    #error "CFG_RET_DATA_UNINIT_SIZE is not defined!"
#endif

; *******************************************************************************************;
; Memory area where retained data will be stored.                                            ;
; *******************************************************************************************;
#define RET_MEM_SIZE        (CFG_RET_DATA_UNINIT_SIZE + CFG_RET_DATA_SIZE + RET_HEAP_SIZE)
; Retained data base address
#define RET_MEM_BASE_ADDR    ALIGN4_HI(__SCT_BLE_BASE - RET_MEM_SIZE)

; chacha20_state base address
#define CHACHA_STATE_BASE_ADDR   ALIGN4_HI(ROM_DATA_BASE_ADDR - RET_DATA_UNINIT_CHACHA_STATE_SIZE)

; trng_state base address
#define TRNG_STATE_BASE_ADDR     ALIGN4_HI(ROM_DATA_BASE_ADDR - RET_DATA_UNINIT_CHACHA_STATE_SIZE - RET_DATA_UNINIT_TRNG_STATE_SIZE)

; *******************************************************************************************;
; Free area resides between the Exchange memory and ROM data.                                ;
; *******************************************************************************************;
; Free area base address
#define FREE_AREA_BASE_ADDR     ALIGN4_HI(__SCT_BLE_BASE + __SCT_EM_BLE_END)
; Free area size
#define FREE_AREA_SIZE          (ROM_DATA_BASE_ADDR - FREE_AREA_BASE_ADDR) - (RET_DATA_UNINIT_CHACHA_STATE_SIZE + RET_DATA_UNINIT_TRNG_STATE_SIZE)

#if defined(CFG_CODE_LOCATION_OTP) && defined(CFG_CODE_LOCATION_EXT)
    #error "Only one of CFG_CODE_LOCATION_OTP and CFG_CODE_LOCATION_EXT must be defined!"
#elif defined(CFG_CODE_LOCATION_OTP)
    #define CODE_LOCATION_OTP   1
    #define CODE_LOCATION_EXT   0
#elif defined(CFG_CODE_LOCATION_EXT)
    #define CODE_LOCATION_OTP   0
    #define CODE_LOCATION_EXT   1
#else
    #error "One of CFG_CODE_LOCATION_OTP and CFG_CODE_LOCATION_EXT must be defined!"
#endif

; OTP memory size = 32K
#define OTP_MEM_SIZE            (32 * 1024)

; OTP header section size = 64 bytes
#define OTP_HEADER_SIZE         (64)

; OTP CS section size = 240 bytes
#define OTP_CS_SIZE             (240)

; Useful OTP memory size
#define OTP_MEM_USEFUL_SIZE     (OTP_MEM_SIZE - OTP_HEADER_SIZE - OTP_CS_SIZE)

; Base address of code (RAM base address + interrupt vector table size + patch table size)
#define CODE_AREA_BASE          (0x07fc0000 + 0xC0 + 80)

; Max needs in RAM per application - excluding the retained data, the interrupt vector table and the patch table
#define CODE_AREA_MAX_SIZE      (RET_MEM_BASE_ADDR - CODE_AREA_BASE)

#if CODE_LOCATION_OTP
    #define CODE_AREA_SIZE      (OTP_MEM_USEFUL_SIZE - (0xC0 + 80))
#elif CODE_LOCATION_EXT
    #define CODE_AREA_SIZE      CODE_AREA_MAX_SIZE
#endif


LR_IROM1 0x07fc0000 0xc0 {                      ;
    ER_IROM1 0x07fc0000 0xc0 {                  ; load address = execution address
        *.o (RESET, +FIRST)
        .ANY (otp_cs_booter)
    }
}

LR_IROM2 0x07fc00c0 80 {                        ; 20 patch function slots
    ER_IROM2 0x07fc00c0 EMPTY 80 {              ; load address = execution address
    }
}

LR_IROM3 CODE_AREA_BASE CODE_AREA_MAX_SIZE {

    ER_IROM3 CODE_AREA_BASE CODE_AREA_SIZE {
       *(InRoot$$Sections)                      ; All library sections that must be in a
                                                ; root region, for example, __main.o,
                                                ; __scatter*.o, __dc*.o, and * Region$$Table
        startup_DA14531.o (+RO)
        system_DA14531.o (+RO)
        .ANY (+RO)
        .ANY (+RW)
    }

; *********************************************************************************************
; * END OF OTP - ANYTHING BELOW THIS POINT IS NOT WRITTEN WHEN THE CODE IS BURNED TO THE OTP! *
; *********************************************************************************************

    ER_PRODTEST AlignExpr(+0,8) UNINIT {
        .ANY (prodtest_uninit)
    }

    ER_ZI +0 {
        .ANY (+ZI)
        .ANY (STACK)
    }

    ER_NZI +0 UNINIT {
        jump_table.o (heap_mem_area_not_ret)    ; not retained HEAP
    }

    ER_STATEFUL_HIBERNATION +0 UNINIT {
        .ANY (stateful_hibernation)
    }
}

LR_RETAINED_RAM0 RET_MEM_BASE_ADDR RET_MEM_SIZE {

    RET_DATA_UNINIT RET_MEM_BASE_ADDR UNINIT CFG_RET_DATA_UNINIT_SIZE {
        .ANY (retention_mem_area_uninit)        ; uninitialized retained data
    }

    RET_DATA +0 CFG_RET_DATA_SIZE {
        .ANY (retention_mem_area0)              ; zero initialized retained data
    }

    RET_HEAP +0 UNINIT RET_HEAP_SIZE {
        jump_table.o (heap_env_area)            ; environment heap
        jump_table.o (heap_db_area)             ; database heap
        jump_table.o (heap_msg_area)            ; message heap - TRNG algorithm uses this heap at startup
    }
}

;*********************************************************************************************;
; Check if the user selected retained data (the zero initialized) size fits in the RET_DATA   ;
; execution region.                                                                           ;
; If the check fails, then the CFG_RET_DATA_SIZE value must be increased accordingly.         ;
; Note: If the selected size is equal to the value calculated by the linker, then the check   ;
;       can be omitted.                                                                       ;
;*********************************************************************************************;
ScatterAssert(CFG_RET_DATA_SIZE > ImageLength(RET_DATA))

; Free area to be used by the application
LR_FREE_AREA FREE_AREA_BASE_ADDR FREE_AREA_SIZE{
    ER_FREE_AREA FREE_AREA_BASE_ADDR FREE_AREA_SIZE {
     .ANY (free_area)
    }
}

#if defined (CFG_TRNG)
; Place the RET_DATA_UNINIT_TRNG_STATE always in RAM3 block at fixed address
LR_RETAINED_TRNG_STATE TRNG_STATE_BASE_ADDR RET_DATA_UNINIT_TRNG_STATE_SIZE {

    RET_DATA_UNINIT_TRNG_STATE +0 UNINIT RET_DATA_UNINIT_TRNG_STATE_SIZE {
        trng.o (trng_state)                     ; flag to decide if TRNG must run or not in order to generate the seed number
    }
}
#endif

#if defined (CFG_USE_CHACHA20_RAND)
; Place the RET_DATA_UNINIT_CHACHA_STATE always in RAM3 block at fixed address
LR_RETAINED_CHACHA_STATE CHACHA_STATE_BASE_ADDR RET_DATA_UNINIT_CHACHA_STATE_SIZE {

    RET_DATA_UNINIT_CHACHA_STATE +0 UNINIT RET_DATA_UNINIT_CHACHA_STATE_SIZE {
        chacha20.o (chacha20_state)             ; random state in case chacha20 is used
        }
}
#endif
