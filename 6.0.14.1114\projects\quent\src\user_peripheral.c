/**
 ****************************************************************************************
 *
 * @file user_peripheral.c
 *
 * @brief Peripheral project source code.
 *
 * Copyright (C) 2015-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */

/**
 ****************************************************************************************
 * @addtogroup APP
 * @{
 ****************************************************************************************
 */

/*
 * INCLUDE FILES
 ****************************************************************************************
 */

#include "rwip_config.h" // SW configuration
#include "gap.h"
#include "app_easy_timer.h"
#include "user_peripheral.h"
#include "user_custs1_impl.h"
#include "user_custs1_def.h"
#include "co_bt.h"
#include "uart.h"
#include "app.h"
#include "app_prf_perm_types.h"
#include "app_easy_security.h"
#include "app_security.h"
#include "app_task.h"
#include "app_utils.h"
#include "User_Application.h"
#include "UpperLayerInterface.h"

#if (WLAN_COEX_ENABLED)
#include "wlan_coex.h"
#include "lld.h"
#endif
/*
 * TYPE DEFINITIONS
 ****************************************************************************************
 */
 
#define PACKET_TIMESTAMP 0
#define PACKET_ORIGIN 4
#define PACKET_PAYLOAD 5

#define GATT_ECG_COUNT 			0
#define GATT_ECG_CRC 				1
#define GATT_ECG_ORIGIN 		5
#define GATT_ECG_TIMESTAMP 	6
#define GATT_ECG_INFERENCE 	10

#define ERR_HR_ERROR					0x01
#define ERR_BO_ERROR					0x02
#define ERR_BP_ERROR					0x03
#define ERR_ECG_ERROR					0x04
#define ERR_TEMP_ERROR					0x05

#define ERR_NW_ID_OFFSET 0x00
#define ERR_NW_ERROR_ID 0x0E
#define ERR_PARAM_ID		0x01
#define ERR_ERR_NO			0x02

# define UNSERICEABLE_ID_OFFSET 		0x00
# define UNSERICEABLE_VITAL_OFFSET	0x01
# define UNSERICEABLE_REASON_OFFSET	0x02

# define RAW_DATA_ID                0x00
# define TOTAL_SIZE_3               0x01
# define TOTAL_SIZE_2               0x02
# define TOTAL_SIZE_1               0x03
# define TOTAL_SIZE_0               0x04
# define PKT_N0_3                   0x05
# define PKT_N0_2                   0x06
# define PKT_N0_1                   0x07
# define PKT_N0_0                   0x08
# define TIMESTAMP_RAW_3            0x09
# define TIMESTAMP_RAW_2            0x0A
# define TIMESTAMP_RAW_1            0x0B
# define TIMESTAMP_RAW_0            0x0C
# define PYLOD_PKT_SIZE_OFFSET      0x0D
# define PYLOAD_OFFSET              0x0E


#define _30SECONDS 3000
#define _1SECOND 100
#define PPG_HEADER 0x01

uint16_t ADVERTISEMENT_DURATION = _1SECOND;

uint8_t BLE_MAC_ID[6];

uint8_t ReadUart(uart_t *Uart_Instance, uint8_t *Dest, uint16_t Length);
void USER_INFO_SENDING_Function(struct custs1_val_write_ind const *ptr);
void Send_PPG(uint8_t *ppg_data);
void PPG_SAMPLES_Write_Handler(uint8_t *PPg_Request);
void Send_NTF_User_Info_Data(uint8_t *usr_info);
void update_alert_data(struct custs1_val_write_ind const *ptr);
void handle_Ind_Cfm( struct custs1_val_ind_cfm const *msg_param);
void Stop_Scheduler(void);
void InitialiseScheduler(void);
void ModifySchTime(uint32_t Delay);
//void send_Ack(uint8_t type);

extern bool Comm_Lock;


void UpdateBleMacId(uint8_t *Mac_ID)
{
	for (uint8_t i = 0, j = 5; i < 6; i++)
	{
		BLE_MAC_ID[i] = Mac_ID[j--];
	}
	notify_stm(INDICATION, comm_wblemacid, NULL, 0);
}

enum exec_req updateMacIDPayload(uint8_t* data, uint16_t* len)
{
	memcpy(data, BLE_MAC_ID, sizeof(BLE_MAC_ID));
	*len = sizeof(BLE_MAC_ID);
	return UL_C;
}

enum exec_req hdlVitalBusy(enum comm_subid sub_id, uint8_t * data, uint16_t len)
{
			uint8_t vital_id = 0;
			switch(sub_id)
			{
				case comm_wHR:
					vital_id = HEART_RATE;
					break;
				case comm_wSPO2:
					vital_id = BLOOD_OXYGEN;
					break;
				case comm_wECG:
					vital_id = EKG;
					break;
				case comm_wCBT:
					vital_id = BODY_TEMPERATURE;
					break;
				case comm_wBP:
					vital_id = BLOOD_PRESSURE;
					break;
				case comm_wStepgoal:
					vital_id = STEP_GOAL;
					break;
			}
			uint8_t vitals_pkt[8] = {0};
			vitals_pkt[PARAM_ID] 	     	= VITAL_BUSY;
			vitals_pkt[1] 	= vital_id;
			vitals_pkt[2] 	= 0; //flag_num for error
			return Send_To_Gatt_Client(vitals_pkt, VITAL_CHAR_LEN, VITAL_VAL);	//sending over network
}

static inline enum exec_req hdlVitalError(uint8_t vital_id, uint8_t * data, uint16_t len)
{
			uint8_t vitals_pkt[8];
	
			memset(vitals_pkt, 0, sizeof(vitals_pkt));
			vitals_pkt[ERR_NW_ID_OFFSET]						= ERR_NW_ERROR_ID;
			vitals_pkt[ERR_PARAM_ID] 	     				= vital_id;
			vitals_pkt[ERR_ERR_NO]								= data[PACKET_PAYLOAD];
			return Send_To_Gatt_Client(vitals_pkt, VITAL_CHAR_LEN, VITAL_VAL);	//sending over network	
}

static inline enum exec_req hdlVitalUnservicable(uint8_t vital_id, uint8_t * data, uint16_t len)
{
	uint8_t vitals_pkt[8];
	memset(vitals_pkt, 0, sizeof(vitals_pkt));
	
	vitals_pkt[UNSERICEABLE_ID_OFFSET]					= 16; // Unservicable Id as per spec.
	vitals_pkt[UNSERICEABLE_VITAL_OFFSET] 	    = vital_id;
	
	if(data[PACKET_PAYLOAD] == ERR_VITAL_BUSY)
	   vitals_pkt[UNSERICEABLE_REASON_OFFSET]			= iReading_inprogress;
	
	else if(data[PACKET_PAYLOAD] == ERR_VITAL_DECLINE) 
	{
		if(vital_id == EKG)
		{
			vitals_pkt[UNSERICEABLE_REASON_OFFSET]			= iRemote_Ekgreq_declined;
		}
		else if(vital_id == BLOOD_PRESSURE)
		{
		  vitals_pkt[UNSERICEABLE_REASON_OFFSET]			= iRemote_bpreq_declined;
		}
		else
		{
			vitals_pkt[UNSERICEABLE_REASON_OFFSET]			= iMeasurement_failed;
		}
	}
	
	return Send_To_Gatt_Client(vitals_pkt, VITAL_CHAR_LEN, VITAL_VAL);
}

#if 0 // size optimisation
enum exec_req sendUserInfoAck(enum comm_subid sub_id, uint8_t *data, uint16_t len)
{
				uint8_t ack = sub_id;
				return Send_To_Gatt_Client(&ack, (uint16_t)sizeof(ack), USR_NAME_1_VAL);
}

enum exec_req HdlCommand(enum comm_subid sub_id, uint8_t *data, uint16_t len)
{
	switch(sub_id)
	{
			case comm_wUpdate:
				data[0] = 18;
				return Send_To_Gatt_Client(data, 8, VITAL_VAL);
			default:
				return UL_F;
	}
}
#endif

/*
param_id list
0-Heart Rate
1-Blood Oxygen
2-BLOOD Pressure
3-ECG

*/

enum exec_req awt_handle_error(uint8_t param_id, uint8_t *data, uint16_t len, uint8_t *gattPacket)
{
	if((data[PACKET_PAYLOAD] == ERR_VITAL_DECLINE) || (data[PACKET_PAYLOAD] == ERR_VITAL_BUSY))
	{
		return hdlVitalUnservicable(param_id, data, len);
	}
	else if((data[PACKET_PAYLOAD] == ERR_VITAL_LBATT))
	{
			gattPacket[0] = 0x0A; // Watch status
			gattPacket[1] = 0;
			gattPacket[2] = 3; // LOW BATTERY
			memcpy(&gattPacket[TIMESTAMP_3], &data[PACKET_TIMESTAMP], 4);
			gattPacket[ORIGIN_TYPE] = data[PACKET_ORIGIN] & 0x1F;
			return Send_To_Gatt_Client(gattPacket, 8, VITAL_VAL);
	}
	else
	{
		switch(param_id)
		{
			case HEART_RATE:
			param_id = ERR_HR_ERROR;
			break;

			case BLOOD_OXYGEN:
			param_id = ERR_BO_ERROR;
			break;

			case BODY_TEMPERATURE:
			param_id = ERR_TEMP_ERROR;
			break;

			case BLOOD_PRESSURE:
			param_id = ERR_BP_ERROR;
			break;

			case EKG:
			param_id = ERR_ECG_ERROR;
			break;
			default:
			param_id = 255; // Invalid case
			break;
		}
		return hdlVitalError(param_id, data, len);
	}
}
enum exec_req sendOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len)
{
	uint8_t gattPacket[25] = {0};
	uint8_t param_id, param_val_msb, param_val_lsb, gatt_handle = 0;
	
	switch (sub_id)
	{
		case comm_wHR:
			{ 
				param_id = HEART_RATE;
				if(data[PACKET_ORIGIN] & 0x80)
				{
					return awt_handle_error(param_id, data, len, gattPacket);
				}
				else
				{
					param_val_msb = 0;
					param_val_lsb = data[PACKET_PAYLOAD];
				}
				gatt_handle = VITAL_VAL;
			}break;
			
	  case comm_wSPO2:
			{
				param_id = BLOOD_OXYGEN;
				if(data[PACKET_ORIGIN] & 0x80)
				{
						return awt_handle_error(param_id, data, len, gattPacket);
				}
				else
				{
					param_val_msb = 0;
					param_val_lsb = data[PACKET_PAYLOAD];
				}
				gatt_handle = VITAL_VAL;
			}break;

	  case comm_wCBT:
			{
				param_id = BODY_TEMPERATURE;
				if(data[PACKET_ORIGIN] & 0x80)
				{
					return awt_handle_error(param_id, data, len, gattPacket);
				}
				else
				{
					param_val_msb = data[PACKET_PAYLOAD];
					param_val_lsb = data[PACKET_PAYLOAD+1];
				}
				gatt_handle = VITAL_VAL;
			}break;
			
	  case comm_wBP:
			{
				param_id 			= BLOOD_PRESSURE;
				if(data[PACKET_ORIGIN] & 0x80)
				{
						return awt_handle_error(param_id, data, len, gattPacket);
				}
				else
				{
					param_val_msb = data[PACKET_PAYLOAD];
					param_val_lsb = data[PACKET_PAYLOAD+1];
				}
				gatt_handle = VITAL_VAL;
			}break;
			
		case comm_wbatt:
			{
				param_id = BATTERY_PERCENTAGE;
				param_val_msb = 0;
				param_val_lsb = data[PACKET_PAYLOAD];
				gatt_handle = VITAL_VAL;
			}break;
	
		case comm_wstepcount:
			{
				param_id = PEDOMETER_SENSOR;
				param_val_msb = data[PACKET_PAYLOAD+1];
				param_val_lsb = data[PACKET_PAYLOAD];
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wStepgoal:
			{
				param_id = STEP_GOAL;
				param_val_msb = data[PACKET_PAYLOAD+1];
				param_val_lsb = data[PACKET_PAYLOAD];
				gatt_handle = VITAL_VAL;
			}break;
			
		case comm_wWatchStatus:
			{
				param_id = WATCH_STATUS;
				param_val_msb = 0;
				param_val_lsb = data[PACKET_PAYLOAD];
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wsos:
			{
				param_id = SOS_IND;
				param_val_lsb = data[PACKET_PAYLOAD];
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wVitalAlertRange:
			{
				param_id = VITAL_OUT_RANGE;
				param_val_msb = data[PACKET_PAYLOAD];
				param_val_lsb = data[PACKET_PAYLOAD+1];
				gatt_handle = VITAL_VAL;
			}break;
			
		case comm_wUpdate:
			{
				param_id = CHECK_UPDATE;
				len = 0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wOtaReset:
			{
				param_id = INIT_UPDATE;
				len = 0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wUcConigwrite:
			{
				param_id = CONFIG_DOWNLOAD;
				len = 0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wFactory_Rst:
			{
				param_id = FACTORY_RST;
				len =0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wStepnctRst:
			{
				param_id = STEP_COUNT_RST;
				len=0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wfall:
			{
				param_id = FALL_IND;
				len = 0;
				gatt_handle = VITAL_VAL;
			}break;
		
		case comm_wECG:
			{
				param_id = EKG;
				if(data[PACKET_ORIGIN] & 0x80)
				{
					return awt_handle_error(param_id, data, len, gattPacket);
				}
				else
				{
					gattPacket[GATT_ECG_COUNT] = 0;				
					memcpy(&gattPacket[GATT_ECG_CRC], &data[PACKET_PAYLOAD+10], 4);
					gattPacket[GATT_ECG_ORIGIN] = data[PACKET_ORIGIN] & 0x1F;
					memcpy(&gattPacket[GATT_ECG_TIMESTAMP], &data[PACKET_TIMESTAMP], 4);
					memcpy(&gattPacket[GATT_ECG_INFERENCE], &data[PACKET_PAYLOAD], 10);
					memset(data, 0, 155);
					memcpy(data, gattPacket, 20);
					gatt_handle = SVC1_ECG_1_VAL;
				}
			}break;
			
		case comm_wUconfig:
			{
				memcpy(data, data + 5, len);
				len-=6;
				gatt_handle = USR_NAME_1_VAL;
			}break;
		case comm_wUconfigack:
		{
			data[0] = data[PACKET_PAYLOAD];
			len = 1;
			gatt_handle = USR_NAME_1_VAL;
		}break;
		
		case comm_wblemacid:
		{
			param_id = 9; // as mentioned in comm spec.
			gatt_handle = VITAL_VAL;
		}break;
		
		default:
			break;
	}
	
	switch (gatt_handle){	//sending over network
		case VITAL_VAL:
			
				gattPacket[PARAM_ID] = param_id;				
				if(param_id == 9)
				{
						memcpy(gattPacket + PARAM_VAL_MSB, data+5, len - 5); // 5: timestamp (4byte) + origin(1-byte)
				}
				else
				{
					if(len ==0)
					{
						memset(gattPacket + PARAM_VAL_MSB, 0, 8);
					}
					else
					{
						gattPacket[PARAM_VAL_MSB] = param_val_msb;
						gattPacket[PARAM_VAL_LSB] = param_val_lsb;
						memcpy(&gattPacket[TIMESTAMP_3], &data[PACKET_TIMESTAMP], 4);
						gattPacket[ORIGIN_TYPE] = data[PACKET_ORIGIN] & 0x1F;
					}
				}
			return Send_To_Gatt_Client(gattPacket, 8, VITAL_VAL);
		case SVC1_ECG_1_VAL:
			return Send_To_Gatt_Client(data, 155, SVC1_ECG_1_VAL);
		case USR_NAME_1_VAL:
			return Send_To_Gatt_Client(data, len, USR_NAME_1_VAL);
		default:
			break;
	}
	return UL_F;
	
}

enum exec_req sendBuffOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len, uint8_t seqNo, uint32_t crc)
{
	switch (sub_id)
	{
		case comm_wECGSamples:
			{
				if (len % 2 != 0) {
					// Error if Odd length data
					return UL_F;
				}
				for (uint16_t i = 0; i < len; i += 2) { //converting little endian to big endian for BLE
					uint8_t temp = data[i];
					data[i] = data[i + 1];
					data[i + 1] = temp;
				}
				for(uint16_t i = 0; i < len; i++)
					data[len - i - 1 + 5] = data[len - i -1];
				
				data[0] = seqNo + 1;
				data[1] = crc>>24;
				data[2] = crc>>16 & 0xff;
				data[3] = crc>>8 & 0xff;
				data[4] = crc & 0xff;
				
				return Send_To_Gatt_Client(data, len + 5, SVC1_ECG_1_VAL);	//sending over network
			}
			
	 case comm_wEcgrawData:
	 case comm_wSpo2rawData:
	{
						for(int i=0; i < len; i++)
            {
                data[len - i - 1 + PYLOAD_OFFSET] = data[len - i -1];
            }
						
            struct Buffer_Xfer* context = GetBufferXferContext();
            data[RAW_DATA_ID]  = sub_id;
            data[TOTAL_SIZE_3] = context->tot_len >> 24;
            data[TOTAL_SIZE_2] = context->tot_len >> 16;
            data[TOTAL_SIZE_1] = context->tot_len >> 8;
            data[TOTAL_SIZE_0] = context->tot_len & 0xff;
            data[PKT_N0_3]     = 0;
            data[PKT_N0_2]     = 0;
            data[PKT_N0_1]     = 0;
            data[PKT_N0_0]     = context->current_segment;
            data[TIMESTAMP_RAW_3]  = context->timestamp >> 24;
            data[TIMESTAMP_RAW_2]  = context->timestamp >> 16;
            data[TIMESTAMP_RAW_1]  = context->timestamp >> 8;
            data[TIMESTAMP_RAW_0]  = context->timestamp & 0xfF;
            data[PYLOD_PKT_SIZE_OFFSET] = len;
			
					return Send_To_Gatt_Client(data, len + PYLOAD_OFFSET, SVC1_PPG_1_VAL);	//sending over network
	}
	break;
			
		default:
			return UL_F;	
	}
}

void handleUserConfigAck(uint8_t status)
{
	uint8_t val = status?0x06:0x15;
	Send_To_Gatt_Client(&val,1, USR_NAME_1_VAL); //send ACK 
}

/*
 * TYPE DEFINITIONS
 ****************************************************************************************
 */

// Manufacturer Specific Data ADV structure type
struct mnf_specific_data_ad_structure
{
	uint8_t ad_structure_size;
	uint8_t ad_structure_type;
	uint8_t company_id[APP_AD_MSD_COMPANY_ID_LEN];
	uint8_t proprietary_data[APP_AD_MSD_DATA_LEN];
};

/*
 * GLOBAL VARIABLE DEFINITIONS
 ****************************************************************************************
 */

uint8_t app_connection_idx __SECTION_ZERO("retention_mem_area0");
timer_hnd app_adv_data_update_timer_used __SECTION_ZERO("retention_mem_area0");
timer_hnd app_param_update_request_timer_used __SECTION_ZERO("retention_mem_area0");

// Retained variables
struct mnf_specific_data_ad_structure mnf_data __SECTION_ZERO("retention_mem_area0"); //@RETENTION MEMORY
// Index of manufacturer data in advertising data or scan response data (when MSB is 1)
uint8_t mnf_data_index __SECTION_ZERO("retention_mem_area0");						   //@RETENTION MEMORY
uint8_t stored_adv_data_len __SECTION_ZERO("retention_mem_area0");					   //@RETENTION MEMORY
uint8_t stored_scan_rsp_data_len __SECTION_ZERO("retention_mem_area0");				   //@RETENTION MEMORY
uint8_t stored_adv_data[ADV_DATA_LEN] __SECTION_ZERO("retention_mem_area0");		   //@RETENTION MEMORY
uint8_t stored_scan_rsp_data[SCAN_RSP_DATA_LEN] __SECTION_ZERO("retention_mem_area0"); //@RETENTION MEMORY

/*
 * FUNCTION DEFINITIONS
 ****************************************************************************************
 */

/**
 ****************************************************************************************
 * @brief Initialize Manufacturer Specific Data
 ****************************************************************************************
 */
static void mnf_data_init()
{
	mnf_data.ad_structure_size = sizeof(struct mnf_specific_data_ad_structure) - sizeof(uint8_t); // minus the size of the ad_structure_size field
	mnf_data.ad_structure_type = GAP_AD_TYPE_MANU_SPECIFIC_DATA;
	mnf_data.company_id[0] = APP_AD_MSD_COMPANY_ID & 0xFF;		  // LSB
	mnf_data.company_id[1] = (APP_AD_MSD_COMPANY_ID >> 8) & 0xFF; // MSB
	mnf_data.proprietary_data[0] = 0;
	mnf_data.proprietary_data[1] = 0;
}

/**
 ****************************************************************************************
 * @brief Update Manufacturer Specific Data
 ****************************************************************************************
 */
static void mnf_data_update()
{
	uint16_t data;

	data = mnf_data.proprietary_data[0] | (mnf_data.proprietary_data[1] << 8);
	data += 1;
	mnf_data.proprietary_data[0] = data & 0xFF;
	mnf_data.proprietary_data[1] = (data >> 8) & 0xFF;

	if (data == 0xFFFF)
	{
		mnf_data.proprietary_data[0] = 0;
		mnf_data.proprietary_data[1] = 0;
	}
}

/**
 ****************************************************************************************
 * @brief Add an AD structure in the Advertising or Scan Response Data of the
 *        GAPM_START_ADVERTISE_CMD parameter struct.
 * @param[in] cmd               GAPM_START_ADVERTISE_CMD parameter struct
 * @param[in] ad_struct_data    AD structure buffer
 * @param[in] ad_struct_len     AD structure length
 * @param[in] adv_connectable   Connectable advertising event or not. It controls whether
 *                              the advertising data use the full 31 bytes length or only
 *                              28 bytes (Document CCSv6 - Part 1.3 Flags).
 ****************************************************************************************
 */
static void app_add_ad_struct(struct gapm_start_advertise_cmd *cmd, void *ad_struct_data, uint8_t ad_struct_len, uint8_t adv_connectable)
{
	uint8_t adv_data_max_size = (adv_connectable) ? (ADV_DATA_LEN - 3) : (ADV_DATA_LEN);

	if ((adv_data_max_size - cmd->info.host.adv_data_len) >= ad_struct_len)
	{
		// Append manufacturer data to advertising data
		memcpy(&cmd->info.host.adv_data[cmd->info.host.adv_data_len], ad_struct_data, ad_struct_len);

		// Update Advertising Data Length
		cmd->info.host.adv_data_len += ad_struct_len;

		// Store index of manufacturer data which are included in the advertising data
		mnf_data_index = cmd->info.host.adv_data_len - sizeof(struct mnf_specific_data_ad_structure);
	}
	else if ((SCAN_RSP_DATA_LEN - cmd->info.host.scan_rsp_data_len) >= ad_struct_len)
	{
		// Append manufacturer data to scan response data
		memcpy(&cmd->info.host.scan_rsp_data[cmd->info.host.scan_rsp_data_len], ad_struct_data, ad_struct_len);

		// Update Scan Response Data Length
		cmd->info.host.scan_rsp_data_len += ad_struct_len;

		// Store index of manufacturer data which are included in the scan response data
		mnf_data_index = cmd->info.host.scan_rsp_data_len - sizeof(struct mnf_specific_data_ad_structure);
		// Mark that manufacturer data is in scan response and not advertising data
		mnf_data_index |= 0x80;
	}
	else
	{
		// Manufacturer Specific Data do not fit in either Advertising Data or Scan Response Data
		ASSERT_WARNING(0);
	}
	// Store advertising data length
	stored_adv_data_len = cmd->info.host.adv_data_len;
	// Store advertising data
	memcpy(stored_adv_data, cmd->info.host.adv_data, stored_adv_data_len);
	// Store scan response data length
	stored_scan_rsp_data_len = cmd->info.host.scan_rsp_data_len;
	// Store scan_response data
	memcpy(stored_scan_rsp_data, cmd->info.host.scan_rsp_data, stored_scan_rsp_data_len);
}

/**
 ****************************************************************************************
 * @brief Advertisement data update timer callback function.
 ****************************************************************************************
 */
static void adv_data_update_timer_cb()
{
	// If mnd_data_index has MSB set, manufacturer data is stored in scan response
	uint8_t *mnf_data_storage = (mnf_data_index & 0x80) ? stored_scan_rsp_data : stored_adv_data;

	// Update manufacturer data
	mnf_data_update();

	// Update the selected fields of the advertising data (manufacturer data)
	memcpy(mnf_data_storage + (mnf_data_index & 0x7F), &mnf_data, sizeof(struct mnf_specific_data_ad_structure));

	// Update advertising data on the fly
	app_easy_gap_update_adv_data(stored_adv_data, stored_adv_data_len, stored_scan_rsp_data, stored_scan_rsp_data_len);

	// Restart timer for the next advertising update
	app_adv_data_update_timer_used = app_easy_timer(ADVERTISEMENT_DURATION, adv_data_update_timer_cb);
}

/**
 ****************************************************************************************
 * @brief Parameter update request timer callback function.
 ****************************************************************************************
 */
static void param_update_request_timer_cb()
{
	app_easy_gap_param_update_start(app_connection_idx);
	app_param_update_request_timer_used = EASY_TIMER_INVALID_TIMER;
}

void user_app_init(void)
{
	app_param_update_request_timer_used = EASY_TIMER_INVALID_TIMER;

	// Initialize Manufacturer Specific Data
	mnf_data_init();

	// Initialize Advertising and Scan Response Data
	memcpy(stored_adv_data, USER_ADVERTISE_DATA, USER_ADVERTISE_DATA_LEN);
	stored_adv_data_len = USER_ADVERTISE_DATA_LEN;
	memcpy(stored_scan_rsp_data, USER_ADVERTISE_SCAN_RESPONSE_DATA, USER_ADVERTISE_SCAN_RESPONSE_DATA_LEN);
	stored_scan_rsp_data_len = USER_ADVERTISE_SCAN_RESPONSE_DATA_LEN;
#if (WLAN_COEX_ENABLED)
	wlan_coex_init();

	// Adds priority case for a specific connection
	// wlan_coex_prio_criteria_add(WLAN_COEX_BLE_PRIO_ADV, LLD_ADV_HDL, 0);
#endif

GPIO_EnableIRQ(GPIO_PORT_0, GPIO_PIN_4, ASM_GPIO_IRQ, false, true, 25);
GPIO_RegisterCallback(ASM_GPIO_IRQ, uart_trigger);

	default_app_on_init();

#if (BLE_APP_SEC)
	// Set service security requirements
	app_set_prf_srv_perm(TASK_ID_CUSTS1, APP_CUSTS1_SEC_REQ); // // Sandeep
	// Fetch bond data from the external memory
	app_easy_security_bdb_init();
#endif
}

void user_app_on_pairing_succeeded(uint8_t conidx)
{
	if (app_sec_env[conidx].auth & GAP_AUTH_BOND)
	{
		
		app_easy_security_bdb_add_entry(&app_sec_env[conidx]);
	}
}

void user_app_adv_start(void)
{
	// Schedule the next advertising data update
	app_adv_data_update_timer_used = app_easy_timer(300, adv_data_update_timer_cb);

	struct gapm_start_advertise_cmd *cmd;
	cmd = app_easy_gap_undirected_advertise_get_active();

	// Add manufacturer data to initial advertising or scan response data, if there is enough space
	app_add_ad_struct(cmd, &mnf_data, sizeof(struct mnf_specific_data_ad_structure), 1);

	app_easy_gap_undirected_advertise_start();

#ifdef AWT_SCHEDULER	
	InitialiseScheduler();
#endif
}

void user_svc_changed_indication(uint8_t arg1, uint16_t arg2)
{
	
	app_gattc_svc_changed_cmd_send(arg1, 0x1234, 0x01, 0xFFFF);
	
}

void user_app_connection(uint8_t connection_idx, struct gapc_connection_req_ind const *param)
{
	

	if (app_env[connection_idx].conidx != GAP_INVALID_CONIDX)
	{
		app_connection_idx = connection_idx;

		// Stop the advertising data update timer
		app_easy_timer_cancel(app_adv_data_update_timer_used);

		// Check if the parameters of the established connection are the preferred ones.
		// If not then schedule a connection parameter update request.
		if ((param->con_interval < user_connection_param_conf.intv_min) ||
			(param->con_interval > user_connection_param_conf.intv_max) ||
			(param->con_latency != user_connection_param_conf.latency) ||
			(param->sup_to != user_connection_param_conf.time_out))
		{
			// Connection params are not these that we expect
			app_param_update_request_timer_used = app_easy_timer(APP_PARAM_UPDATE_REQUEST_TO, param_update_request_timer_cb);
		}
		Comm_Lock = false; // debug
		
		// For Rohith, Add connection counter
	}
	else
	{
		// No connection has been established, restart advertising
		user_app_adv_start();
	}

	default_app_on_connection(connection_idx, param);
}

void user_app_adv_undirect_complete(uint8_t status)
{
	// If advertising was canceled then update advertising data and start advertising again
	if (status == GAP_ERR_CANCELED)
	{
		user_app_adv_start();
	}
}

void user_app_disconnect(struct gapc_disconnect_ind const *param)
{
	
	// Cancel the parameter update request timer
	if (app_param_update_request_timer_used != EASY_TIMER_INVALID_TIMER)
	{
		app_easy_timer_cancel(app_param_update_request_timer_used);
		app_param_update_request_timer_used = EASY_TIMER_INVALID_TIMER;
	}
	// Update manufacturer data for the next advertsing event
	mnf_data_update();
	// Restart Advertising
	user_app_adv_start();

	/* Disconnect Code Goes Here */

	Comm_Lock = false; // debug
	
	
	notify_stm(INDICATION, comm_wblediscon, NULL, NULL);
}
#if (BLE_APP_SEC)
void user_app_on_tk_exch(uint8_t conidx,
						 struct gapc_bond_req_ind const *param)
{
	bool accept = true;

	if (param->data.tk_type == GAP_TK_OOB)
	{
		uint8_t oob_tk[KEY_LEN] = APP_SECURITY_OOB_TK_VAL;
		app_easy_security_tk_exch(conidx, (uint8_t *)oob_tk, KEY_LEN, accept);
	}
	else if (param->data.tk_type == GAP_TK_DISPLAY)
	{
		uint32_t KEY = 0;

		uint8_t buf[6] = {0};
		
#ifdef QUENT_STATIC_PAIRING_KEY
     KEY = 123456;
#else		
	   KEY = app_sec_gen_tk(); // For Generating a Passkey	
#endif		
		
		app_easy_security_tk_exch(conidx, (uint8_t *)&KEY, sizeof(KEY), accept); // Regestreing a passkey

		for (uint8_t i = 0; i < 6; i++)
		{
			buf[5 - i] = KEY % 10;
			KEY /= 10;
		}

	
		Comm_Lock = false; // debug
		notify_stm(INDICATION, comm_wblepasskey, buf, sizeof(buf));

		app_easy_security_tk_exch(conidx, (uint8_t *)&KEY, sizeof(KEY), accept);
	}
	else if (param->data.tk_type == GAP_TK_KEY_ENTRY)
	{

#if defined(CFG_PRINTF)
		arch_printf("\r\n Passkey Entered: %u", passkey);
#endif
	}
	else if (param->data.tk_type == GAP_TK_KEY_CONFIRM)
	{
#if defined(CFG_PRINTF)
		uint32_t passkey;
		// Print the 6 Least Significant Digits of Passkey
		char buf[6];
		passkey = (param->tk.key[0] << 0) | (param->tk.key[1] << 8) |
				  (param->tk.key[2] << 16) | (param->tk.key[3] << 24);
		arch_printf("\r\n Confirmation Value: ");
		for (uint8_t i = 0; i < 6; i++)
		{
			buf[5 - i] = passkey % 10;
			passkey /= 10;
		}
		for (uint8_t i = 0; i < 6; i++)
		{
			arch_printf("%u", buf[i]);
		}
#endif

		app_easy_security_tk_exch(conidx, (uint8_t *)&param->tk, sizeof(param->tk), accept);
	}
}
#endif // (BLE_APP_SEC)

void user_catch_rest_hndl(ke_msg_id_t const msgid,
						  void const *param,
						  ke_task_id_t const dest_id,
						  ke_task_id_t const src_id)
{
	Update_Ind_srcid(src_id);

	switch (msgid)
	{
	case CUSTS1_VAL_WRITE_IND:
	{
		struct custs1_val_write_ind const *msg_param = (struct custs1_val_write_ind const *)(param);
		// update src_id
		Update_Ind_srcid(src_id);

		switch (msg_param->handle)
		{
			case VITAL_IND:
			update_conn_flag(VITAL_IND, msg_param->value[0]);
			break;
			case ALERT_TTL_IND:
			update_conn_flag(ALERT_TTL_IND, msg_param->value[0]);
			break;
			case USR_NAME_1_IND:
			update_conn_flag(USR_NAME_1_IND, msg_param->value[0]);
			break;
			case SVC1_ECG_1_IND_CFG:
				update_conn_flag(SVC1_ECG_1_IND_CFG, msg_param->value[0]);
			break;
			case SVC1_PPG_1_IND_CFG:
				update_conn_flag(SVC1_PPG_1_IND_CFG, msg_param->value[0]);
			break;

		case VITAL_VAL:
			UpdateVitalsReq((uint8_t *)msg_param->value);
			break;

		case SVC1_ECG_1_VAL: /// SVC1_PPG_1_VAL
			ECG_SAMPLES_Write_Handler((uint8_t *)msg_param->value);
				
			break;

		case SVC1_PPG_1_VAL: /// SVC1_PPG_1_VAL
			PPG_SAMPLES_Write_Handler((uint8_t *)msg_param->value);
			break;

		case USR_NAME_1_VAL:
			USER_INFO_SENDING_Function(msg_param);
			break;

		case ALRT_TTL_VAL:
			update_alert_data(msg_param);
			break;
#ifdef DEBUG_ENABLE
		// NODO: for debug
		case WIRELESSS_DBG_1_VAL:
			if(msg_param->value[0] ==0xFF)
			{
					debug_statemachine();
			}
			if(msg_param->value[0] ==0xFE)
			{
				resetPrimitiveManager();
				resetTransportManager();
				wakeupST(0);
			}
			break;
#endif

		default:
			break;
		}
	}
	break;

	case CUSTS1_VAL_NTF_CFM:
	{
	}
	break;

	case CUSTS1_VAL_IND_CFM:
	{
		struct custs1_val_ind_cfm const *msg_param = (struct custs1_val_ind_cfm const *)(param);
		handle_Ind_Cfm(msg_param);
	}
	break;

	case GAPC_PARAM_UPDATED_IND:
	{
		// Cast the "param" pointer to the appropriate message structure
		struct gapc_param_updated_ind const *msg_param = (struct gapc_param_updated_ind const *)(param);

		// Check if updated Conn Params filled to preferred ones
		if ((msg_param->con_interval >= user_connection_param_conf.intv_min) &&
			(msg_param->con_interval <= user_connection_param_conf.intv_max) &&
			(msg_param->con_latency == user_connection_param_conf.latency) &&
			(msg_param->sup_to == user_connection_param_conf.time_out))
		{
		}
	}
	break;

	case CUSTS1_VALUE_REQ_IND:
	{
		struct custs1_value_req_ind const *msg_param = (struct custs1_value_req_ind const *)param;

		switch (msg_param->att_idx)
		{

		default:
		{
			// Send Error message
			struct custs1_value_req_rsp *rsp = KE_MSG_ALLOC(CUSTS1_VALUE_REQ_RSP,
															src_id,
															dest_id,
															custs1_value_req_rsp);

			// Provide the connection index.
			rsp->conidx = app_env[msg_param->conidx].conidx;
			// Provide the attribute index.
			rsp->att_idx = msg_param->att_idx;
			// Force current length to zero.
			rsp->length = 0;
			// Set Error status
			rsp->status = ATT_ERR_APP_ERROR;
			// Send message
			ke_msg_send(rsp);
		}
		break;
		}
	}
	break;

	default:
		break;
	}
}

//void send_Ack(uint8_t ack){}
/// @} APP
