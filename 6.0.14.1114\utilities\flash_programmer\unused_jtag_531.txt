;#<FEEDBACK># ARM Linker, 5060750: Last Updated: Wed Apr 29 13:34:41 2020
;VERSION 0.2
;FILE arch_system.o
__asm___13_arch_system_c_f9d1779f____REV16 <= USED 0
__asm___13_arch_system_c_f9d1779f____REVSH <= USED 0
arch_set_pxact_gpio <= USED 0
;FILE crc32.o
crc32 <= USED 0
;FILE gpio.o
GPIO_EnableIRQ <= USED 0
GPIO_GetIRQInputLevel <= USED 0
GPIO_GetPinFunction <= USED 0
GPIO_GetPinStatus <= USED 0
GPIO_GetPorTime <= USED 0
GPIO_RegisterCallback <= USED 0
GPIO_SetIRQInputLevel <= USED 0
GPIO_SetPorTime <= USED 0
__asm___6_gpio_c_7b3262c7____REV16 <= USED 0
__asm___6_gpio_c_7b3262c7____REVSH <= USED 0
;FILE hardfault_handler.o
__asm___19_hardfault_handler_c_34b5b6d4____REV16 <= USED 0
__asm___19_hardfault_handler_c_34b5b6d4____REVSH <= USED 0
;FILE hw_otpc_531.o
__asm___13_hw_otpc_531_c_5e36599e____REV16 <= USED 0
__asm___13_hw_otpc_531_c_5e36599e____REVSH <= USED 0
hw_otpc_clear_dcdc_reserved <= USED 0
hw_otpc_read <= USED 0
hw_otpc_word_prog_and_verify <= USED 0
hw_otpc_word_read <= USED 0
;FILE i2c.o
__asm___5_i2c_c_i2c_init____REV16 <= USED 0
__asm___5_i2c_c_i2c_init____REVSH <= USED 0
i2c_master_receive_buffer_async <= USED 0
i2c_master_receive_buffer_sync <= USED 0
i2c_master_transmit_buffer_async <= USED 0
i2c_master_transmit_buffer_sync <= USED 0
i2c_register_int <= USED 0
i2c_release <= USED 0
i2c_slave_receive_buffer_async <= USED 0
i2c_slave_receive_buffer_sync <= USED 0
i2c_slave_transmit_buffer_async <= USED 0
i2c_slave_transmit_buffer_sync <= USED 0
i2c_unregister_int <= USED 0
;FILE i2c_eeprom.o
__asm___12_i2c_eeprom_c_ba011902____REV16 <= USED 0
__asm___12_i2c_eeprom_c_ba011902____REVSH <= USED 0
i2c_eeprom_get_configuration <= USED 0
i2c_eeprom_read_byte <= USED 0
i2c_eeprom_release <= USED 0
i2c_eeprom_update_slave_address <= USED 0
i2c_eeprom_write_byte <= USED 0
;FILE nmi_handler.o
__asm___13_nmi_handler_c_34da3f24____REV16 <= USED 0
__asm___13_nmi_handler_c_34da3f24____REVSH <= USED 0
;FILE otp_cs.o
__asm___8_otp_cs_c_8475e5a4____REV16 <= USED 0
__asm___8_otp_cs_c_8475e5a4____REVSH <= USED 0
otp_cs_get_adc_25_cal <= USED 0
otp_cs_get_adc_diff_ge <= USED 0
otp_cs_get_adc_diff_offset <= USED 0
otp_cs_get_adc_offsh_ge <= USED 0
otp_cs_get_adc_offsh_offset <= USED 0
otp_cs_get_adc_single_ge <= USED 0
otp_cs_get_adc_single_offset <= USED 0
otp_cs_get_adc_trim_val <= USED 0
otp_cs_get_low_power_clock <= USED 0
otp_cs_get_xtal_wait_trim <= USED 0
otp_cs_load_pd_adpll <= USED 0
otp_cs_load_pd_rad <= USED 0
;FILE peripherals.o
__asm___13_peripherals_c_e6264286____REV16 <= USED 0
__asm___13_peripherals_c_e6264286____REVSH <= USED 0
;FILE programmer.o
__asm___12_programmer_c_buffer____REV16 <= USED 0
__asm___12_programmer_c_buffer____REVSH <= USED 0
communication_error <= USED 0
get_action <= USED 0
get_address <= USED 0
get_read_position <= USED 0
get_size <= USED 0
get_write_position <= USED 0
gpio_wd_timer0_stop <= USED 0
read_data <= USED 0
response_invalid_crc_result <= USED 0
response_retrieve_result <= USED 0
;FILE spi_531.o
__asm___9_spi_531_c_484a6895____REV16 <= USED 0
__asm___9_spi_531_c_484a6895____REVSH <= USED 0
spi_disable <= USED 0
spi_master_transfer <= USED 0
spi_receive <= USED 0
spi_register_receive_cb <= USED 0
spi_register_send_cb <= USED 0
spi_register_transfer_cb <= USED 0
spi_send <= USED 0
spi_set_cs_mode <= USED 0
spi_transfer <= USED 0
;FILE spi_flash.o
__asm___11_spi_flash_c_c7a633d6____REV16 <= USED 0
__asm___11_spi_flash_c_c7a633d6____REVSH <= USED 0
spi_flash_block_erase_no_wait <= USED 0
spi_flash_chip_erase_forced <= USED 0
spi_flash_enable <= USED 0
spi_flash_erase_fail <= USED 0
spi_flash_fill <= USED 0
spi_flash_get_power_mode <= USED 0
spi_flash_page_erase <= USED 0
spi_flash_page_fill <= USED 0
spi_flash_page_program_buffer <= USED 0
spi_flash_power_down <= USED 0
spi_flash_program_fail <= USED 0
spi_flash_read_config_reg <= USED 0
spi_flash_read_data_buffer <= USED 0
spi_flash_read_rems_id <= USED 0
spi_flash_read_security_reg <= USED 0
spi_flash_read_unique_id <= USED 0
spi_flash_set_power_mode <= USED 0
spi_flash_set_write_disable <= USED 0
spi_flash_set_write_enable <= USED 0
spi_flash_write_data_buffer <= USED 0
spi_flash_write_enable_volatile <= USED 0
spi_flash_write_status_config_reg <= USED 0
;FILE startup_da14531.o
;FILE syscntl.o
__asm___9_syscntl_c_bee48145____REV16 <= USED 0
__asm___9_syscntl_c_bee48145____REVSH <= USED 0
syscntl_cfg_xtal32m_amp_reg <= USED 0
syscntl_dcdc_turn_off <= USED 0
syscntl_dcdc_turn_on_in_buck <= USED 0
syscntl_por_vbat_high_cfg <= USED 0
syscntl_por_vbat_low_cfg <= USED 0
syscntl_use_highest_amba_clocks <= USED 0
syscntl_use_lowest_amba_clocks <= USED 0
;FILE system_da14531.o
SystemCoreClockUpdate <= USED 0
__asm___16_system_DA14531_c_5d646a67____REV16 <= USED 0
__asm___16_system_DA14531_c_5d646a67____REVSH <= USED 0
;FILE timer0.o
__asm___8_timer0_c_1f57d2d6____REV16 <= USED 0
__asm___8_timer0_c_1f57d2d6____REVSH <= USED 0
timer0_disable_irq <= USED 0
timer0_enable_irq <= USED 0
timer0_register_callback <= USED 0
timer0_release <= USED 0
;FILE uart.o
__asm___6_uart_c_17d47916____REV16 <= USED 0
__asm___6_uart_c_17d47916____REVSH <= USED 0
uart_baudrate_setf <= USED 0
uart_disable <= USED 0
uart_disable_flow_control <= USED 0
uart_enable <= USED 0
uart_enable_flow_control <= USED 0
uart_initialize <= USED 0
uart_read_buffer <= USED 0
uart_read_byte <= USED 0
uart_receive <= USED 0
uart_register_err_cb <= USED 0
uart_register_rx_cb <= USED 0
uart_register_tx_cb <= USED 0
uart_send <= USED 0
uart_wait_tx_finish <= USED 0
uart_write_buffer <= USED 0
uart_write_byte <= USED 0
