# QUENT BLE - Full Flow Documentation

## Overview
This document describes the complete BLE (Bluetooth Low Energy) flow implementation for the QUENT project running on DA14531 SoC. The system acts as a BLE peripheral device that communicates with STM32 SoC to provide various health monitoring features.

## System Architecture

### Hardware Components
- **DA14531 SoC**: BLE processor running the peripheral firmware
- **STM32 SoC**: Main processor handling sensor data and application logic
- **Communication Interface**: UART-based communication between DA14531 and STM32

### Software Stack
```
┌─────────────────────────────────────────┐
│           Application Layer             │
├─────────────────────────────────────────┤
│         Communication Manager          │
├─────────────────────────────────────────┤
│            BLE Stack (Host)             │
├─────────────────────────────────────────┤
│          BLE Stack (Controller)         │
├─────────────────────────────────────────┤
│              Hardware (RF)              │
└─────────────────────────────────────────┘
```

## BLE Services and Characteristics

### 1. ECG Service
- **Service UUID**: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x01, 0x00, 0x00}`
- **ECG Samples Characteristic**: 
  - UUID: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x01, 0x00, 0x00}`
  - Properties: Read, Write, Indicate
  - Length: 155 bytes
- **PPG Samples Characteristic**:
  - UUID: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x02, 0x01, 0x00, 0x00}`
  - Properties: Read, Write, Indicate
  - Length: 174 bytes

### 2. Vitals Service
- **Service UUID**: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x0F, 0x18, 0x00, 0x00}`
- **Vital Status Characteristic**:
  - UUID: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x19, 0x2A, 0x00, 0x00}`
  - Properties: Write, Indicate
  - Length: 8 bytes
  - Supports: Heart Rate, Blood Oxygen, Body Temperature, Blood Pressure, ECG

### 3. Alert Service
- **Service UUID**: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x08, 0x00, 0x00}`
- **Alert Status Characteristic**:
  - UUID: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x08, 0x00, 0x00}`
  - Properties: Write, Indicate
  - Length: 129 bytes

### 4. User Info Service
- **Service UUID**: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x05, 0x00, 0x00}`
- **User Name Characteristic**:
  - UUID: `{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x05, 0x00, 0x00}`
  - Properties: Write, Indicate
  - Length: 140 bytes

## BLE Connection Flow

### 1. Initialization Phase
```mermaid
sequenceDiagram
    participant App as Application
    participant BLE as BLE Stack
    participant RF as RF Hardware
    
    App->>BLE: Initialize BLE Stack
    BLE->>RF: Configure RF Parameters
    App->>BLE: Create GATT Database
    BLE->>BLE: Register Services & Characteristics
    App->>BLE: Start Advertising
    BLE->>RF: Begin Advertisement Transmission
```

### 2. Advertising Configuration
- **Advertising Interval**: 687.5ms (1100 BLE slots)
- **Advertising Channels**: All channels (37, 38, 39)
- **Advertising Mode**: General Discoverable
- **Advertising Type**: Undirected Connectable
- **Advertising Data**: Device name, manufacturer data, service UUIDs

### 3. Connection Establishment
```mermaid
sequenceDiagram
    participant Client as BLE Client
    participant DA14531 as DA14531 (Peripheral)
    participant STM32 as STM32 SoC
    
    Client->>DA14531: Connection Request
    DA14531->>DA14531: Accept Connection
    DA14531->>Client: Connection Response
    DA14531->>STM32: Notify Connection Status
    Client->>DA14531: Service Discovery
    DA14531->>Client: Service & Characteristic Info
    Client->>DA14531: Enable Notifications/Indications
```

### 4. Connection Parameters
- **Connection Interval**: 10-20ms (8-16 BLE intervals)
- **Slave Latency**: 0
- **Supervision Timeout**: 1250ms (125 timer units)
- **Maximum MTU**: 200 bytes

## Communication Manager Flow

### Architecture Overview
The Communication Manager handles data flow between BLE and STM32 using a layered approach:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Upper Layer    │    │   Primitive     │    │   Transport     │
│   Interface     │<-->│    Manager      │<-->│    Manager      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ^                       ^                       ^
         │                       │                       │
         v                       v                       v
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   BLE Stack     │    │  Message Queue  │    │ Serial Interface│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Message Flow Types

#### 1. Command/Response Flow
```mermaid
sequenceDiagram
    participant STM32
    participant CommMgr as Communication Manager
    participant BLE as BLE Stack
    participant Client as BLE Client
    
    STM32->>CommMgr: Command Request
    CommMgr->>CommMgr: Queue Command
    CommMgr->>BLE: Process Command
    BLE->>Client: Send Response
    Client->>BLE: Acknowledgment
    BLE->>CommMgr: Response Status
    CommMgr->>STM32: Command Complete
```

#### 2. Indication Flow
```mermaid
sequenceDiagram
    participant STM32
    participant CommMgr as Communication Manager
    participant BLE as BLE Stack
    participant Client as BLE Client
    
    STM32->>CommMgr: Send Data (Vitals/Alerts)
    CommMgr->>CommMgr: Queue Indication
    CommMgr->>BLE: Format Data Packet
    BLE->>Client: Send Indication
    Client->>BLE: Confirmation
    BLE->>CommMgr: Indication Complete
    CommMgr->>STM32: Status Update
```

#### 3. Buffer Transfer Flow
```mermaid
sequenceDiagram
    participant STM32
    participant CommMgr as Communication Manager
    participant BLE as BLE Stack
    participant Client as BLE Client
    
    STM32->>CommMgr: Buffer Transfer Request (ECG Data)
    CommMgr->>CommMgr: Segment Large Data
    loop For Each Segment
        CommMgr->>BLE: Send Segment with CRC
        BLE->>Client: Transmit Segment
        Client->>BLE: Segment Acknowledgment
    end
    BLE->>CommMgr: Transfer Complete
    CommMgr->>STM32: Buffer Transfer Status
```

## Data Processing Flow

### 1. Vital Signs Processing
When STM32 sends vital sign data:

1. **Data Reception**: Communication Manager receives data from STM32
2. **Data Validation**: Check data integrity and format
3. **Parameter Mapping**: Map to appropriate vital parameter ID:
   - Heart Rate (0)
   - Blood Oxygen (1) 
   - Body Temperature (2)
   - Blood Pressure (3)
   - ECG (4)
4. **Packet Formation**: Create GATT packet with timestamp and origin
5. **BLE Transmission**: Send via GATT indication to connected client

### 2. ECG Data Processing
ECG data requires special handling due to large size:

1. **Buffer Management**: Large ECG samples stored in buffer
2. **Endian Conversion**: Convert little-endian to big-endian for BLE
3. **Segmentation**: Add sequence number and CRC for integrity
4. **Transmission**: Send via ECG characteristic with indication

### 3. Error Handling
- **Timeout Management**: Retry mechanisms for failed transmissions
- **Error Codes**: Specific error codes for different failure types
- **Recovery**: Automatic recovery and reconnection procedures

## Key Implementation Files

### Core BLE Implementation
- `user_peripheral.c`: Main BLE peripheral implementation
- `user_custs1_def.c/h`: Custom service definitions
- `user_custs1_impl.c`: Custom service implementation
- `user_config.h`: BLE configuration parameters

### Communication Manager
- `Comm_Manager.c/h`: Main communication manager
- `PrimitiveManager.c`: Handles primitive operations
- `TransportManager.c`: Manages transport layer
- `UpperLayerInterface.c`: Interface to upper layers
- `SerialInterface.c`: UART communication with STM32

### Configuration
- `da1458x_config_basic.h`: Basic DA14531 configuration
- `user_periph_setup.c`: Peripheral setup and GPIO configuration

## Power Management
- **Sleep Mode**: Device enters sleep when idle
- **Wake-up**: GPIO interrupt from STM32 wakes up DA14531
- **Connection Management**: Optimized connection parameters for power efficiency

## Security Features
- **Bonding Support**: Persistent pairing information
- **Service Security**: Configurable security requirements per service
- **Authentication**: Support for authenticated connections

## Debugging and Monitoring
- **Debug Service**: Optional debug service for development
- **Logging**: Comprehensive logging system for troubleshooting
- **Status Monitoring**: Real-time status monitoring capabilities

## Detailed State Machine Flows

### BLE Connection State Machine
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    IDLE     │───▶│ ADVERTISING │───▶│  CONNECTED  │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   │                   │
       │                   ▼                   ▼
       │            ┌─────────────┐    ┌─────────────┐
       └────────────│   TIMEOUT   │◀───│DISCONNECTED │
                    └─────────────┘    └─────────────┘
```

### Communication Manager State Machine
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   T_IDLE    │───▶│   M_INIT    │───▶│    M_TX     │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   │                   │
       │                   ▼                   ▼
       │            ┌─────────────┐    ┌─────────────┐
       └────────────│   T_CLOSE   │◀───│   M_WAIT    │
                    └─────────────┘    └─────────────┘
```

## Message Types and Protocols

### 1. Command Messages (cmd_tx)
- **Purpose**: Request data from STM32
- **Flow**: BLE → STM32 → BLE
- **Retry**: Up to 3 attempts
- **Timeout**: Configurable timeout with callback

### 2. Indication Messages (ind_tx)
- **Purpose**: Send data to BLE client
- **Flow**: STM32 → BLE → Client
- **Confirmation**: Requires client acknowledgment
- **Data Types**: Vitals, alerts, user info

### 3. Buffer Transfer Messages (buff_tx)
- **Purpose**: Transfer large data blocks (ECG samples)
- **Flow**: STM32 → BLE → Client (segmented)
- **Integrity**: CRC checksum per segment
- **Sequencing**: Sequential segment numbering

## GPIO and Hardware Interface

### GPIO Configuration
```c
// BLE-STM32 Communication Pins
GPIO_PIN_4: ST_BLE (Input, Pull-down)  // STM32 wake-up signal
GPIO_PIN_3: BLE_ST (Output)            // BLE status to STM32
GPIO_PIN_5: RF_SWITCH (Output)         // RF switch control
```

### UART Configuration
```c
// UART Settings for STM32 Communication
Baud Rate: 230400 bps
Data Bits: 8
Parity: None
Stop Bits: 1
Flow Control: None
FIFO: Enabled
```

## Data Packet Formats

### Vital Signs Packet Format
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│  Param ID   │ Param MSB   │ Param LSB   │ Timestamp   │ Origin Type │
│   (1 byte)  │  (1 byte)   │  (1 byte)   │  (4 bytes)  │  (1 byte)   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

### ECG Buffer Transfer Packet Format
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ Sequence No │    CRC      │    CRC      │    CRC      │    CRC      │
│   (1 byte)  │  (1 byte)   │  (1 byte)   │  (1 byte)   │  (1 byte)   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
│                           ECG Sample Data                            │
│                        (Variable Length)                            │
└─────────────────────────────────────────────────────────────────────┘
```

### Transport Layer Packet Format
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   Header    │     ID      │   Sub ID    │    Size     │    Data     │
│  (1 byte)   │  (1 byte)   │  (1 byte)   │  (2 bytes)  │ (Variable)  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
│                    CRC                   │   Footer    │
│                 (4 bytes)                │  (1 byte)   │
└─────────────────────────────────────────┴─────────────┘
```

## Error Handling and Recovery

### Error Types and Codes
```c
// Vital Sign Error Codes
#define ERR_HR_ERROR    10  // Heart Rate Error
#define ERR_BO_ERROR    11  // Blood Oxygen Error
#define ERR_TEMP_ERROR  12  // Temperature Error
#define ERR_BP_ERROR    13  // Blood Pressure Error
#define ERR_ECG_ERROR   14  // ECG Error

// Execution Status Codes
typedef enum {
    UL_C,  // Complete
    UL_F,  // Failed
    UL_S   // Success
} exec_req;
```

### Recovery Mechanisms
1. **Automatic Retry**: Failed operations retry up to 3 times
2. **Timeout Handling**: Configurable timeouts with callback functions
3. **Connection Recovery**: Automatic reconnection on connection loss
4. **Data Integrity**: CRC validation for critical data transfers
5. **State Reset**: Ability to reset communication manager state

## Performance Characteristics

### Throughput Specifications
- **Maximum MTU**: 200 bytes
- **ECG Data Rate**: Up to 155 bytes per indication
- **PPG Data Rate**: Up to 174 bytes per indication
- **Vital Signs**: 8 bytes per measurement
- **Connection Interval**: 10-20ms for optimal performance

### Memory Usage
- **GATT Database**: Dynamic allocation based on enabled services
- **Communication Buffers**: Ring buffers for data queuing
- **Retention Memory**: Critical state preserved during sleep

### Power Consumption
- **Active Mode**: During data transmission
- **Sleep Mode**: Between transmissions
- **Wake-up Latency**: Optimized for quick response to STM32

## Integration Guidelines

### STM32 Integration Points
1. **GPIO Signaling**: Use GPIO_PIN_4 for wake-up signaling
2. **UART Protocol**: Follow transport layer packet format
3. **Data Timing**: Respect BLE connection intervals
4. **Error Handling**: Implement retry mechanisms
5. **Power Management**: Coordinate sleep/wake cycles

### Client Application Integration
1. **Service Discovery**: Discover all custom services
2. **Characteristic Configuration**: Enable indications for data characteristics
3. **Data Parsing**: Handle different data packet formats
4. **Error Recovery**: Implement connection recovery logic
5. **Security**: Handle pairing and bonding procedures

## Testing and Validation

### Test Scenarios
1. **Connection Establishment**: Verify advertising and connection flow
2. **Data Transmission**: Test all data types and packet formats
3. **Error Conditions**: Validate error handling and recovery
4. **Performance**: Measure throughput and latency
5. **Power Consumption**: Validate sleep/wake behavior
6. **Interoperability**: Test with different BLE clients

### Debug Features
- **Debug Service**: Optional service for development debugging
- **Logging System**: Comprehensive logging with different levels
- **Status Indicators**: GPIO indicators for system status
- **Memory Monitoring**: Track memory usage and leaks

This comprehensive documentation covers the complete BLE implementation flow in the QUENT project, providing detailed technical information for developers working with the system.
