#<SYMDEFS># ARM Linker, 5060422: Last Updated: Wed Mar 15 11:21:38 2017
;0x00000000 N __ARM_use_no_argv
;0x000000a0 N __Vectors_Size
0x07f02001 T uECC_vli_add
0x07f02019 T uECC_vli_sub
0x07f02035 T uECC_vli_mult
;0x07f020b9 T SystemCoreClockUpdate
;0x07f020c1 T SystemInit
0x07f02151 T set_system_clocks
0x07f0216d T set_peripheral_clocks
0x07f021ad T rf_workaround_init
0x07f021af T get_stack_usage
;0x07f021b3 T platform_reset_func
0x07f021ed T rwip_eif_get_func
0x07f021fd T rwip_set_em_base
0x07f02203 T platform_initialization
0x07f022b5 T ble_init
0x07f02327 T ble_regs_push
0x07f0237f T ble_regs_pop
0x07f023d5 T platform_sleep
;0x07f0256f T BLE_WAKEUP_LP_Handler
;0x07f02669 T app_disable_sleep
;0x07f02675 T app_set_extended_sleep
;0x07f02681 T app_set_deep_sleep
;0x07f0268f T app_get_sleep_mode
;0x07f026b1 T rf_init
0x07f026b9 T rf_reinit
0x07f026c1 T smpc_check_param
0x07f026cb T smpc_pdu_recv
0x07f026d5 T lld_sleep_compensate
0x07f026df T lld_sleep_init
0x07f026e9 T lld_sleep_us_2_lpcycles
0x07f026f3 T lld_sleep_lpcycles_2_us
0x07f026fd T uart_flow_off
0x07f02705 T uart_finish_transfers
0x07f0270d T uart_read
0x07f02715 T uart_write
0x07f0271d T UART_Handler
0x07f02725 T uart_init
0x07f0272d T uart_flow_on
0x07f02735 T gtl_init
0x07f0273d T gtl_eif_init
0x07f02745 T gtl_eif_read_start
0x07f0274d T gtl_eif_read_hdr
0x07f02755 T gtl_eif_read_payl
0x07f0275d T gtl_eif_tx_done
0x07f02765 T gtl_eif_rx_done
0x07f0276d T h4tl_init
0x07f02775 T h4tl_read_start
0x07f0277d T h4tl_read_hdr
0x07f02785 T h4tl_read_payl
0x07f0278d T h4tl_read_next_out_of_sync
0x07f02795 T h4tl_out_of_sync
0x07f0279d T h4tl_tx_done
0x07f027a5 T h4tl_rx_done
0x07f027ad T ke_task_init
0x07f027b5 T ke_timer_init
0x07f027bd T llm_encryption_done
0x07f027c5 T nvds_get
0x07f027cd T nvds_del
0x07f027d5 T nvds_put
0x07f027dd T rwip_eif_get
0x07f027e5 T platform_reset
0x07f027ef T lld_test_stop
0x07f027f9 T lld_test_mode_tx
0x07f02803 T lld_test_mode_rx
0x07f0280d T prf_init
0x07f02817 T prf_add_profile
0x07f02821 T prf_create
0x07f0282b T prf_cleanup
0x07f02835 T prf_get_id_from_task
0x07f0283f T prf_get_task_from_id
0x07f02849 T nvds_init
0x07f02851 T SetSystemVars
0x07f0285b T dbg_init
0x07f02865 T dbg_platform_reset_complete
0x07f0286f T hci_rd_local_supp_feats_cmd_handler
0x07f0287f T l2cc_pdu_pack
0x07f02895 T l2cc_pdu_unpack
0x07f028ad T l2c_send_lecb_message
0x07f028b7 T l2c_process_sdu
0x07f028c1 T l2cc_pdu_recv_ind_handler
0x07f028d1 T gapc_lecb_connect_cfm_handler
0x07f028e1 T atts_l2cc_pdu_recv_handler
0x07f028eb T attc_l2cc_pdu_recv_handler
0x07f028f5 T crypto_init
0x07f028ff T llm_le_adv_report_ind
0x07f02909 T PK_PointMult
0x07f02913 T llm_p256_start
0x07f0291d T llm_create_p256_key
0x07f02929 T llm_p256_req_handler
0x07f0293b T llc_le_length_effective
0x07f02947 T llc_le_length_conn_init
0x07f02953 T lld_data_tx_prog
0x07f0295f T lld_data_tx_check
0x07f0296b T llc_pdu_send
0x07f02977 T dia_rand
0x07f0298f T llc_data_notif
0x07f029a9 T ba431_get_rand
0x07f02a01 T co_buf_init
0x07f02ac7 T co_buf_rx_free
0x07f02ae1 T co_buf_rx_buffer_get
0x07f02aeb T co_buf_tx_buffer_get
0x07f02b15 T co_list_init
0x07f02b25 T co_list_pop_front
0x07f02b47 T co_list_flush
0x07f02b5d T co_list_push_back
0x07f02b81 T co_list_pool_init
0x07f02be9 T co_list_push_front
0x07f02c05 T co_list_extract
0x07f02c55 T co_list_find
0x07f02c6b T co_list_merge
0x07f02c87 T co_list_insert_before
0x07f02cbf T co_list_insert_after
0x07f02cfb T co_list_size
0x07f02d0f T co_bdaddr_compare
0x07f02d2d T llc_init
0x07f02d57 T llc_stop
0x07f02d9d T llc_reset
0x07f02dbf T llc_le_length_effective_func
0x07f02e53 T llc_le_length_conn_init_func
0x07f02eaf T llc_le_enh_con_cmp_evt_send
0x07f02fc9 T llc_le_con_cmp_evt_send
0x07f03075 T llc_start
0x07f031e7 T llc_discon_event_complete_send
0x07f03207 T llc_con_update_complete_send
0x07f03241 T llc_ltk_req_send
0x07f03279 T llc_feats_rd_event_send
0x07f032b5 T llc_version_rd_event_send
0x07f032e5 T llc_common_cmd_complete_send
0x07f03303 T llc_common_cmd_status_send
0x07f0331f T llc_common_flush_occurred_send
0x07f03337 T llc_common_enc_key_ref_comp_evt_send
0x07f03353 T llc_common_enc_change_evt_send
0x07f033b9 T llc_common_nb_of_pkt_comp_evt_send
0x07f033d9 T llc_con_update_ind
0x07f03453 T llc_lsto_con_update
0x07f03485 T llc_map_update_ind
0x07f03537 T llc_chnl_map_req_send
0x07f03553 T llc_add_bad_chnl
0x07f035f5 T llc_pdu_send_func
0x07f03665 T llc_version_ind_pdu_send
0x07f036b5 T llc_ch_map_update_pdu_send
0x07f03705 T llc_pause_enc_req_pdu_send
0x07f03751 T llc_pause_enc_rsp_pdu_send
0x07f037b5 T llc_enc_req_pdu_send
0x07f0387f T llc_enc_rsp_pdu_send
0x07f0391b T llc_start_enc_rsp_pdu_send
0x07f03971 T llc_reject_ind_pdu_send
0x07f03a13 T llc_con_update_pdu_send
0x07f03a69 T llc_con_param_req_pdu_send
0x07f03af7 T llc_con_param_rsp_pdu_send
0x07f03b85 T llc_feats_req_pdu_send
0x07f03be5 T llc_start_enc_req_pdu_send
0x07f03c8b T llc_terminate_ind_pdu_send
0x07f03cff T llc_unknown_rsp_send_pdu
0x07f03d33 T llc_length_req_pdu_send
0x07f03de9 T llc_length_rsp_pdu_send
0x07f03e6f T llc_length_ind
0x07f03efb T llc_ping_req_pdu_send
0x07f03f2b T llc_ping_rsp_pdu_send
0x07f03f5b T llc_feats_req_ind
0x07f03fbd T llc_feats_rsp_ind
0x07f04015 T llc_vers_ind_ind
0x07f04099 T llc_terminate_ind
0x07f040d5 T llc_pause_enc_req_ind
0x07f04105 T llc_pause_enc_rsp_ind
0x07f04179 T llc_enc_req_ind
0x07f0421d T llc_enc_rsp_ind
0x07f042e1 T llc_start_enc_req_ind
0x07f04343 T llc_start_enc_rsp_ind
0x07f043cb T llc_cntl_rcv
0x07f04457 T llcp_con_param_req_pdu_unpk
0x07f044cf T llcp_con_param_rsp_pdu_unpk
0x07f04547 T llc_con_update_req_ind
0x07f045a1 T llc_ch_map_req_ind
0x07f04615 T llc_data_rcv
0x07f063c5 T llc_util_get_free_conhdl
0x07f063f5 T llc_util_dicon_procedure
0x07f06457 T llc_util_gen_skdx
0x07f0646b T llc_util_update_channel_map
0x07f0647d T llc_util_set_llcp_discard_enable
0x07f06495 T llc_util_set_auth_payl_to_margin
0x07f064bd T llc_data_notif_func
0x07f065c9 T lld_init
0x07f06717 T lld_reset
0x07f06765 T lld_adv_start
0x07f068b3 T lld_adv_stop
0x07f068d9 T lld_scan_start
0x07f06a33 T lld_scan_stop
0x07f06a6b T lld_con_start
0x07f06de3 T lld_move_to_master
0x07f06e79 T lld_con_update_req
0x07f06ef3 T lld_con_update_after_param_req
0x07f070d9 T lld_con_param_rsp
0x07f071d3 T lld_con_param_req
0x07f072a5 T lld_con_stop
0x07f072fb T lld_get_mode
0x07f0731f T lld_move_to_slave
0x07f0749f T lld_ch_map_ind
0x07f074b1 T lld_con_update_ind
0x07f074bf T lld_crypt_isr
0x07f074c9 T lld_test_mode_tx_func
0x07f0756b T lld_test_mode_rx_func
0x07f075ff T lld_test_stop_func
0x07f076bd T lld_data_rx_check
0x07f07701 T lld_data_rx_flush
0x07f07725 T lld_data_tx_check_func
0x07f077cb T lld_data_tx_loop
0x07f077f9 T lld_data_tx_push
0x07f0785b T lld_data_tx_prog_func
0x07f07991 T lld_data_tx_flush
0x07f07aa9 T lld_evt_drift_compute
0x07f07b87 T lld_evt_elt_delete
0x07f08181 T lld_evt_deffered_elt_handler
0x07f08253 T lld_evt_init
0x07f082c9 T lld_evt_init_evt
0x07f082e7 T lld_evt_elt_insert
0x07f08311 T lld_evt_conhdl2elt
0x07f0832d T lld_evt_schedule_next
0x07f08425 T lld_evt_schedule
0x07f08461 T lld_evt_prevent_stop
0x07f08463 T lld_evt_canceled
0x07f08487 T lld_evt_scan_create
0x07f08579 T lld_evt_move_to_master
0x07f08673 T lld_evt_update_create
0x07f087a9 T lld_evt_ch_map_update_req
0x07f087c1 T lld_evt_move_to_slave
0x07f089f1 T lld_evt_slave_update
0x07f08a87 T lld_evt_adv_create
0x07f08b0b T lld_evt_end
0x07f08c19 T lld_evt_rx
0x07f08c49 T lld_evt_timer_isr
0x07f08c53 T lld_evt_end_isr
0x07f08cd3 T lld_evt_rx_isr
0x07f08e81 T lld_sleep_us_2_lpcycles_func
0x07f08e9f T lld_sleep_lpcycles_2_us_func
;0x07f08ec3 T lld_sleep_compensate_func
;0x07f08ef5 T lld_sleep_init_func
0x07f08f9f T lld_sleep_enter
0x07f08fdf T lld_sleep_wakeup
0x07f08ff9 T lld_sleep_wakeup_end
0x07f0901d T lld_wlcoex_connection_complete
0x07f09035 T lld_wlcoex_remove_connection
0x07f09049 T lld_wlcoex_set
0x07f0905d T lld_util_get_bd_address
0x07f0907d T lld_util_set_bd_address
0x07f090b9 T lld_util_freq2chnl
0x07f090db T lld_util_get_local_offset
0x07f090f5 T lld_util_get_peer_offset
0x07f09111 T lld_util_connection_param_set
0x07f09161 T llm_wl_clr
0x07f09189 T llm_init
0x07f0938d T llm_common_cmd_complete_send
0x07f093a5 T llm_ble_ready
0x07f093ab T llm_wl_from_rl_restore
0x07f0942d T llm_con_req_ind
0x07f096a3 T llm_resolv_addr
0x07f096e5 T llm_util_rl_wl_update
0x07f0971f T llm_alter_conn
0x07f097c3 T llm_adv_report_set
0x07f0983d T llm_direct_adv_report_set
0x07f0987f T llm_encryption_start
0x07f09903 T llm_resolv_addr_inplace
0x07f099d5 T llm_le_adv_report_ind_func
0x07f09ee3 T llm_con_req_tx_cfm
0x07f0a003 T llm_common_cmd_status_send
0x07f0a01b T llm_test_mode_start_tx
0x07f0a10f T llm_test_mode_start_rx
0x07f0a2e5 T llm_gen_rand_addr
0x07f0a151 T llm_set_adv_param
0x07f0a4d3 T llm_set_adv_en
0x07f0a7cf T llm_set_adv_data
0x07f0a883 T llm_set_scan_rsp_data
0x07f0a94d T llm_set_scan_param
0x07f0a9d3 T llm_set_scan_en
0x07f0ab47 T llm_wl_dev_add
0x07f0ac21 T llm_wl_dev_rem
0x07f0a3a1 T llm_wl_from_rl
0x07f0ac75 T llm_create_con
0x07f0af73 T llm_encryption_done_func
0x07f0b215 T llm_get_chnl_assess_nb_pkt
0x07f0b21b T llm_get_chnl_assess_nb_bad_pkt
0x07f0b221 T llm_get_min_rssi
0x07f0b22b T llm_le_scan_report_ind
0x07f0b2c3 T llm_set_tx_oct_time
0x07f0b2eb T llm_p256_start_func
0x07f0bb57 T hci_rd_local_supp_feats_cmd_handler_func
0x07f0c405 T llm_util_bd_addr_in_wl
0x07f0c47d T llm_util_check_address_validity
0x07f0c48d T llm_util_check_map_validity
0x07f0c4d3 T llm_util_apply_bd_addr
0x07f0c4eb T llm_util_set_public_addr
0x07f0c4f9 T llm_util_check_evt_mask
0x07f0c51b T llm_util_get_channel_map
0x07f0c529 T llm_util_get_supp_features
0x07f0c535 T llm_util_adv_data_update
0x07f0c559 T llm_util_bl_check
0x07f0c59b T llm_util_bl_add
0x07f0c5f1 T llm_util_bl_rem
0x07f0c641 T llm_util_rl_check
0x07f0c677 T llm_util_rl_add
0x07f0c6f5 T llm_util_rl_rem
0x07f0c71b T llm_util_rl_peer_find
0x07f0c747 T llm_util_rl_peer_resolv
0x07f0c799 T llm_util_rl_rpa_find
0x07f0c8b9 T ea_time_get_slot_rounded
0x07f0c985 T ea_init
0x07f0c9d9 T ea_elt_create
0x07f0c9f3 T ea_time_get_halfslot_rounded
0x07f0ca23 T ea_elt_insert
0x07f0cc67 T ea_elt_remove
0x07f0ccef T ea_elt_delete
0x07f0cd09 T ea_interval_create
0x07f0cd1f T ea_interval_insert
0x07f0cd2d T ea_interval_delete
0x07f0cd47 T ea_finetimer_isr
0x07f0ce19 T ea_sw_isr
0x07f0ce37 T ea_offset_req
0x07f0cff5 T ea_sleep_check
0x07f0d04b T ea_interval_duration_req
0x07f0d16b T flash_identify
0x07f0d1b9 T flash_init
0x07f0d1f5 T flash_erase
0x07f0d259 T flash_write
0x07f0d2bd T flash_read
0x07f0d34b T uart_init_func
0x07f0d3a9 T uart_flow_on_func
0x07f0d3b1 T uart_flow_off_func
0x07f0d401 T uart_finish_transfers_func
0x07f0d419 T uart_read_func
0x07f0d42f T uart_write_func
0x07f0d451 T UART_Handler_func
0x07f0d4a3 T uart_set_flow_off_retries_limit
0x07f0d7e1 T gtl_init_func
0x07f0d809 T gtl_enter_sleep
0x07f0d833 T gtl_exit_sleep
0x07f0d83b T gtl_send_msg
0x07f0d8c1 T gtl_eif_read_start_func
0x07f0d8e1 T gtl_eif_read_hdr_func
0x07f0d901 T gtl_eif_read_payl_func
0x07f0d93f T gtl_eif_tx_done_func
0x07f0d94f T gtl_eif_rx_done_func
0x07f0da81 T gtl_eif_init_func
0x07f0da9b T gtl_eif_write
0x07f0dabd T gtl_eif_start
0x07f0dac7 T gtl_eif_stop
0x07f0dae3 T gtl_env_curr_msg_type_set
0x07f0ddc5 T hci_tl_send
0x07f0de0d T hci_tl_init
0x07f0de31 T hci_cmd_get_max_param_size
0x07f0de7b T hci_cmd_received
0x07f0dfb7 T hci_acl_tx_data_alloc
0x07f0e03d T hci_acl_tx_data_received
0x07f0e105 T hci_acl_rx_data_alloc
0x07f0e111 T hci_acl_rx_data_received
0x07f0e147 T hci_evt_received
0x07f0e2f3 T hci_tl_env_tx_queue_cnt_get
0x07f0e411 T hci_util_pack
0x07f0e511 T hci_util_unpack
0x07f0e9f5 T hci_look_for_cmd_desc
0x07f0ea41 T hci_look_for_evt_desc
0x07f0ea63 T hci_look_for_le_evt_desc
0x07f0ea95 T hci_evt_mask_set
0x07f0eadd T hci_init
0x07f0eaf9 T hci_reset
0x07f0eb11 T hci_send_2_host
0x07f0ebfb T hci_send_2_controller
0x07f0ecd5 T h4tl_read_start_func
0x07f0ecf3 T h4tl_read_hdr_func
0x07f0ed0f T h4tl_read_payl_func
0x07f0ed29 T h4tl_read_next_out_of_sync_func
0x07f0ed3d T h4tl_out_of_sync_func
0x07f0ed63 T h4tl_out_of_sync_check
0x07f0edbf T h4tl_tx_done_func
0x07f0edd7 T h4tl_rx_done_func
0x07f0eefb T h4tl_init_func
0x07f0ef17 T h4tl_write
0x07f0ef3f T h4tl_start
0x07f0ef47 T h4tl_stop
0x07f0ef5b T h4tl_env_rx_type_set
0x07f0ef67 T h4tl_env_hdr_set
0x07f0efa5 T attc_send_att_req
0x07f0efe1 T attc_allocate_att_req
0x07f0f003 T attc_send_hdl_cfm
0x07f0f019 T attc_send_execute
0x07f0f033 T attc_send_read_ind
0x07f0f973 T attc_l2cc_pdu_recv_handler_func
0x07f0f9cd T attm_convert_to128
0x07f0f9fd T attm_uuid_comp
0x07f0fa55 T attm_uuid16_comp
0x07f0fa61 T attm_is_bt16_uuid
0x07f0fa87 T attm_is_bt32_uuid
0x07f0fe87 T attmdb_add_service
0x07f0ff0f T attmdb_destroy
0x07f0ff29 T attmdb_get_service
0x07f0ff67 T attmdb_get_attribute
0x07f0ff9b T attmdb_get_next_att
0x07f0ffff T attmdb_uuid16_comp
0x07f1003b T attmdb_att_set_value
0x07f100e7 T attmdb_get_max_len
0x07f1014d T attmdb_get_uuid
0x07f10223 T attmdb_get_value
0x07f10373 T attmdb_att_set_permission
0x07f103e7 T attmdb_att_update_perm
0x07f10455 T attmdb_svc_get_permission
0x07f10473 T attmdb_att_get_permission
0x07f10575 T attmdb_svc_set_permission
0x07f10599 T attmdb_init
0x07f105af T attmdb_get_nb_svc
0x07f105c3 T attmdb_get_svc_info
0x07f105f7 T attm_svc_create_db
0x07f10705 T attmdb_reserve_handle_range
0x07f10895 T atts_clear_read_cache
0x07f109fd T atts_send_error
0x07f10a19 T atts_write_signed_cfm
0x07f10a59 T atts_send_event
0x07f10ae1 T atts_clear_prep_data
0x07f10b05 T atts_clear_rsp_data
0x07f10b55 T atts_write_rsp_send
0x07f1177f T atts_l2cc_pdu_recv_handler_func
0x07f119e7 T gattc_cleanup
0x07f11a6b T gattc_init
0x07f11a9d T gattc_update_state
0x07f11ac1 T gattc_create
0x07f11b35 T gattc_con_enable
0x07f11b3b T gattc_get_mtu
0x07f11b47 T gattc_set_mtu
0x07f11b8d T gattc_get_requester
0x07f11ba9 T gattc_send_complete_evt
0x07f11c05 T gattc_send_error_evt
0x07f11c2b T gattc_get_operation
0x07f11c41 T gattc_get_op_seq_num
0x07f11c57 T gattc_get_operation_ptr
0x07f11c63 T gattc_set_operation_ptr
0x07f11c6f T gattc_reschedule_operation
0x07f11cb5 T gattc_reallocate_svc
0x07f12c6d T gattm_svc_get_start_hdl
0x07f12c73 T gattm_init
0x07f12c91 T gattm_init_attr
0x07f12ce5 T gattm_create
0x07f12ced T gattm_cleanup
0x07f12cf5 T gattm_get_max_mtu
0x07f12cfb T gattm_set_max_mtu
0x07f12d17 T gattm_get_max_mps
0x07f12d1d T gattm_set_max_mps
0x07f12f85 T l2cc_cleanup
0x07f12fc9 T l2cc_init
0x07f12ffb T l2cc_create
0x07f13033 T l2cc_update_state
0x07f13405 T l2cm_init
0x07f13419 T l2cm_create
0x07f13421 T l2cm_cleanup
0x07f13429 T l2cm_set_link_layer_buff_size
0x07f13439 T smpc_send_use_enc_block_cmd
0x07f13471 T smpc_send_start_enc_cmd
0x07f134eb T smpc_send_ltk_req_rsp
0x07f13547 T smpc_send_pairing_req_ind
0x07f1362b T smpc_send_pairing_ind
0x07f13747 T smpc_check_pairing_feat
0x07f13761 T smpc_launch_rep_att_timer
0x07f1379d T smpc_check_repeated_attempts
0x07f137ff T smpc_check_max_key_size
0x07f13845 T smpc_check_key_distrib
0x07f13885 T smpc_xor
0x07f1389b T smpc_generate_l
0x07f138e9 T smpc_generate_ci
0x07f1394d T smpc_generate_rand
0x07f13973 T smpc_generate_e1
0x07f13a2d T smpc_generate_cfm
0x07f13aa7 T smpc_generate_stk
0x07f13afd T smpc_calc_subkeys
0x07f13b73 T smpc_clear_timeout_timer
0x07f13b9d T smpc_pairing_end
0x07f13bfb T smpc_tkdp_rcp_continue
0x07f13c75 T smpc_tkdp_rcp_start
0x07f13cc9 T smpc_pdu_send
0x07f13d67 T smpc_tkdp_send_start
0x07f13df3 T smpc_tkdp_send_continue
0x07f13e6f T smpc_get_key_sec_prop
0x07f13f3b T smpc_is_sec_mode_reached
0x07f13f65 T smpc_handle_enc_change_evt
0x07f1401f T smpc_pdu_recv_func
0x07f14089 T smpc_generate_subkey
0x07f140bd T leftshift_onebit
0x07f140d5 T padding
0x07f140f9 T smpc_generate_subkey_P2
0x07f1419d T AES_CMAC_block
0x07f1425d T smpc_generate_f4
0x07f14371 T smpc_generate_g2
0x07f1441b T smpc_generate_f5
0x07f1442d T smpc_generate_f5_T
0x07f1449d T smpc_generate_f5_P2
0x07f1466f T smpc_generate_f6
0x07f14809 T smpm_send_encrypt_req
0x07f14837 T smpm_send_gen_rand_nb_req
0x07f1484d T smpm_check_addr_type
0x07f148b9 T gapc_update_state
0x07f148e9 T gapc_get_requester
0x07f14905 T gapc_send_complete_evt
0x07f14a2f T gapc_init
0x07f14a61 T gapc_con_create
0x07f14b1b T gapc_con_create_enh
0x07f14c15 T gapc_con_cleanup
0x07f14c25 T gapc_send_disconect_ind
0x07f14c47 T gapc_get_conidx
0x07f14c81 T gapc_get_conhdl
0x07f14c99 T gapc_get_role
0x07f14cb5 T gapc_get_bdaddr
0x07f14cd5 T gapc_get_csrk
0x07f14cf3 T gapc_get_sign_counter
0x07f14d11 T gapc_send_error_evt
0x07f14d33 T gapc_get_operation
0x07f14d49 T gapc_get_operation_ptr
0x07f14d55 T gapc_set_operation_ptr
0x07f14d61 T gapc_reschedule_operation
0x07f14d91 T gapc_get_enc_keysize
0x07f14da9 T gapc_is_sec_set
0x07f14e35 T gapc_set_enc_keysize
0x07f14e49 T gapc_link_encrypted
0x07f14e63 T gapc_auth_set
0x07f14e83 T gapc_svc_chg_ccc_get
0x07f14e93 T gapc_svc_chg_ccc_set
0x07f14ea9 T gapc_check_lecb_sec_perm
0x07f14f11 T gapc_search_lecb_channel
0x07f14f4b T gapc_lecnx_check_tx
0x07f14f93 T gapc_lecnx_check_rx
0x07f14fd7 T gapc_lecnx_get_field
0x07f1504d T gapc_process_op
0x07f151c7 T gapc_param_update_sanity
0x07f151ef T gapc_param_cb_con_sanity
0x07f156ab T l2cc_pdu_recv_ind_handler_func
0x07f16653 T gapc_lecb_connect_cfm_handler_func
0x07f16a27 T gapm_init
0x07f16a83 T gapm_init_attr
0x07f16aaf T gapm_get_operation
0x07f16ac1 T gapm_get_requester
0x07f16ad9 T gapm_reschedule_operation
0x07f16afb T gapm_send_complete_evt
0x07f16b31 T gapm_send_error_evt
0x07f16b51 T gapm_con_create
0x07f16bd5 T gapm_con_enable
0x07f16be1 T gapm_con_cleanup
0x07f16c11 T gapm_get_id_from_task
0x07f16c51 T gapm_get_task_from_id
0x07f16c8d T gapm_is_disc_connection
0x07f17ab9 T gapm_adv_sanity
0x07f17bad T gapm_adv_op_sanity
0x07f17d2b T gapm_set_adv_mode
0x07f17d45 T gapm_set_adv_data
0x07f17dd1 T gapm_execute_adv_op
0x07f17ef7 T gapm_scan_op_sanity
0x07f17fff T gapm_set_scan_mode
0x07f1801d T gapm_execute_scan_op
0x07f180d7 T gapm_connect_op_sanity
0x07f18257 T gapm_basic_hci_cmd_send
0x07f1826b T gapm_execute_connect_op
0x07f1840d T gapm_get_role
0x07f18415 T gapm_get_ad_type_flag
0x07f1843b T gapm_add_to_filter
0x07f184bb T gapm_is_filtered
0x07f1851f T gapm_update_air_op_state
0x07f185e7 T gapm_get_irk
0x07f185ed T gapm_get_bdaddr
0x07f18609 T l2cc_pdu_pack_func
0x07f18b01 T l2cc_pdu_unpack_func
0x07f18e03 T l2cc_detect_dest
0x07f18e5f T l2c_process_sdu_func
0x07f18f77 T l2c_send_lecb_message_func
0x07f19071 T smpc_check_param_func
0x07f19c73 T gapc_hci_handler
0x07f1a8c1 T gapm_hci_handler
0x07f1a951 T smpc_pairing_start
0x07f1a9d7 T smpc_pairing_tk_exch
0x07f1aa95 T smpc_pairing_ltk_exch
0x07f1aae9 T smpc_pairing_csrk_exch
0x07f029d9 T smpc_public_key_exchange_start
0x07f1ab3f T smpc_pairing_rsp
0x07f1ac23 T smpc_pairing_req_handler
0x07f1ac5b T smpc_security_req_send
0x07f1ac85 T smpc_encrypt_start
0x07f1acab T smpc_encrypt_start_handler
0x07f1acdd T smpc_encrypt_cfm
0x07f1ad09 T smpc_sign_command
0x07f1ade1 T smpc_sign_cont
0x07f1af87 T smpc_calc_confirm_cont
0x07f1b4c9 T smpc_confirm_gen_rand
0x07f1b5b3 T smpc_dhkey_calc_start
0x07f1b601 T smpc_sec_authentication_start
0x07f029e5 T smpc_dhkey_calc_ind
0x07f1b675 T smpm_gen_rand_addr
0x07f1b68d T smpm_resolv_addr
0x07f1b6af T smpm_use_enc_block
0x07f1b6b7 T smpm_gen_rand_nb
0x07f029f1 T smpm_ecdh_key_create
0x07f1b6dd T ke_init
0x07f1b70f T ke_flush
0x07f1b74f T ke_sleep_check
0x07f1b761 T ke_stats_get
0x07f1b77d T ke_event_init
0x07f1b789 T ke_event_callback_set
0x07f1b79d T ke_event_set
0x07f1b7c9 T ke_event_clear
0x07f1b7f5 T ke_event_get
0x07f1b81b T ke_event_get_all
0x07f1b821 T ke_event_flush
0x07f1b829 T ke_event_schedule
0x07f1b879 T ke_mem_init
0x07f1b8c5 T ke_mem_is_empty
0x07f1b905 T ke_check_malloc
0x07f1b995 T ke_malloc
0x07f1ba8b T ke_free
0x07f1bb6d T ke_is_free
0x07f1bb7f T ke_get_mem_usage
0x07f1bb8b T ke_get_max_mem_usage
0x07f1bbb1 T ke_msg_alloc
0x07f1bbe3 T ke_msg_send
0x07f1bc0f T ke_msg_send_basic
0x07f1bc1d T ke_msg_forward
0x07f1bc27 T ke_msg_forward_new_id
0x07f1bc37 T ke_msg_free
0x07f1bc3f T ke_msg_dest_id_get
0x07f1bc45 T ke_msg_src_id_get
0x07f1bc4b T ke_msg_in_queue
0x07f1bc5d T ke_queue_extract
0x07f1bcad T ke_queue_insert
0x07f1be3f T ke_task_init_func
0x07f1be53 T ke_task_create
0x07f1be8b T ke_task_delete
0x07f1beb7 T ke_state_set
0x07f1bf1b T ke_state_get
0x07f1bf39 T ke_msg_discard
0x07f1bf3d T ke_msg_save
0x07f1bf41 T ke_task_msg_flush
0x07f1c049 T ke_timer_init_func
0x07f1c055 T ke_timer_set
0x07f1c0e9 T ke_timer_clear
0x07f1c13f T ke_timer_active
0x07f1c165 T ke_timer_sleep_check
;0x07f1c231 T nvds_init_func
;0x07f1c235 T nvds_get_func
;0x07f1c255 T nvds_del_func
;0x07f1c259 T nvds_lock
;0x07f1c25d T nvds_put_func
;0x07f1c269 T rwble_hl_init
0x07f1c28b T rwble_hl_reset
0x07f1c2ad T rwble_hl_send_message
0x07f1c2b1 T rwip_check_wakeup_boundary
0x07f1c2d7 T rwip_init
0x07f1c39b T rwip_reset
0x07f1c3d3 T rwip_version
0x07f1c3db T rwip_schedule
;0x07f1c3eb T rwip_sleep
0x07f1c47f T rwip_prevent_sleep_set
0x07f1c4a1 T rwip_wakeup
0x07f1c4b7 T rwip_prevent_sleep_clear
0x07f1c4d9 T rwip_wakeup_end
0x07f1c4f5 T rwip_wakeup_delay_set
0x07f1c503 T rwip_sleep_enable
0x07f1c509 T rwip_ext_wakeup_enable
0x07f1c52d T rwble_init
0x07f1c593 T rwble_reset
0x07f1c5c7 T rwble_version
0x07f1c5f3 T rwble_send_message
;0x07f1c627 T rwble_isr
0x07f1c6d9 T YieldToScheduler
0x07f1c6e1 T xorshift64star
0x07f1c747 T uECC_set_rng
0x07f1c74d T uECC_get_rng
0x07f1c753 T uECC_curve_private_key_size
0x07f1c763 T uECC_curve_public_key_size
0x07f1c76b T uECC_vli_clear
0x07f1c781 T uECC_vli_isZero
0x07f1c7a3 T uECC_vli_testBit
0x07f1c7b5 T uECC_vli_numBits
0x07f1c7ef T uECC_vli_set
0x07f1c82d T uECC_vli_equal
0x07f1c851 T uECC_vli_cmp
0x07f1c887 T uECC_vli_rshift1
0x07f1c8a5 T uECC_vli_square
0x07f1c8b1 T uECC_vli_modAdd
0x07f1c8df T uECC_vli_modSub
0x07f1c8ff T uECC_vli_mmod
0x07f1ca09 T uECC_vli_modMult
0x07f1ca2b T uECC_vli_modMult_fast
0x07f1ca4b T uECC_vli_modSquare
0x07f1ca59 T uECC_vli_modSquare_fast
0x07f1ca99 T uECC_vli_modInv
0x07f1cdd5 T uECC_secp256r1
0x07f1d3d3 T uECC_vli_nativeToBytes
0x07f1d3f5 T uECC_vli_bytesToNative
0x07f1d433 T uECC_generate_random_int
0x07f1d495 T uECC_make_key
0x07f1d513 T uECC_shared_secret
0x07f1d5cf T uECC_compress
0x07f1d5fd T uECC_decompress
0x07f1d66d T uECC_valid_point
0x07f1d6cf T uECC_valid_public_key
0x07f1d703 T uECC_compute_public_key
0x07f1d989 T uECC_sign
0x07f1da6f T uECC_sign_deterministic
0x07f1dbdd T uECC_verify
0x07f1de89 T uECC_curve_num_words
0x07f1de91 T uECC_curve_num_bytes
0x07f1de99 T uECC_curve_num_bits
0x07f1dea1 T uECC_curve_num_n_words
0x07f1deb1 T uECC_curve_num_n_bytes
0x07f1dec1 T uECC_curve_num_n_bits
0x07f1dec9 T uECC_curve_p
0x07f1decd T uECC_curve_n
0x07f1ded1 T uECC_curve_G
0x07f1ded5 T uECC_curve_b
0x07f1ded9 T uECC_vli_mod_sqrt
0x07f1dedf T uECC_vli_mmod_fast
0x07f1dee5 T uECC_point_mult
0x07f1dfb9 T __aeabi_uidiv
0x07f1dfb9 T __aeabi_uidivmod
0x07f1dfe5 T __aeabi_idiv
0x07f1dfe5 T __aeabi_idivmod
0x07f1e00d T __aeabi_lmul
0x07f1e00d T _ll_mul
0x07f1e089 T rand
0x07f1e09b T srand
0x07f1e0ad T __aeabi_memcpy
0x07f1e0ad T __aeabi_memcpy4
0x07f1e0ad T __aeabi_memcpy8
0x07f1e0d1 T __aeabi_memset
0x07f1e0d1 T __aeabi_memset4
0x07f1e0d1 T __aeabi_memset8
0x07f1e0df T __aeabi_memclr
0x07f1e0df T __aeabi_memclr4
0x07f1e0df T __aeabi_memclr8
0x07f1e0e3 T _memset$wrapper
0x07f1e0f5 T memcmp
0x07f1e10f T __aeabi_uread4
0x07f1e10f T __rt_uread4
0x07f1e10f T _uread4
0x07f1e123 T __aeabi_uwrite4
0x07f1e123 T __rt_uwrite4
0x07f1e123 T _uwrite4
0x07f1e135 T __aeabi_llsl
0x07f1e135 T _ll_shift_l
0x07f1e155 T __ARM_common_switch8
0x07f1e170 D uart_api
0x07f1e180 D co_sca2ppm
0x07f1e190 D co_null_bdaddr
0x07f1e196 D co_default_bdaddr
0x07f1e41c D llc_state_handler
0x07f1e4ec D llc_default_handler
0x07f1e51c D llm_local_cmds
0x07f1e55c D llm_local_le_feats
0x07f1e564 D llm_local_le_states
0x07f1e74c D llm_state_handler
0x07f1e77c D llm_default_handler
0x07f1e784 D LLM_AA_CT1
0x07f1e787 D LLM_AA_CT2
0x07f1e789 D ecc_p256_G
0x07f1e7dc D gtl_default_state
0x07f1e7e4 D gtl_default_handler
0x07f1e7f0 D hci_cmd_desc_tab_lk_ctrl
0x07f1e814 D hci_cmd_desc_tab_ctrl_bb
0x07f1e88c D hci_cmd_desc_tab_info_par
0x07f1e8c8 D hci_cmd_desc_tab_stat_par
0x07f1e8d4 D hci_cmd_desc_tab_le
0x07f1eb14 D hci_cmd_desc_tab_vs
0x07f1ec88 D hci_evt_desc_tab
0x07f1ecd0 D hci_evt_le_desc_tab
0x07f1ed40 D attc_handlers
0x07f1edb0 D atts_handlers
0x07f1ee30 D gattc_default_state
0x07f1ef10 D gattc_default_handler
0x07f1ef5c D gattm_default_state
0x07f1efb4 D gattm_default_handler
0x07f1efcc D l2cc_default_state
0x07f1efe4 D l2cc_default_handler
0x07f1f005 D const_Rb
0x07f1f015 D const_Zero
0x07f1f038 D gapc_default_state
0x07f1f188 D gapc_default_handler
0x07f1f224 D gapm_default_state
0x07f1f30c D gapm_default_handler
0x07f1f430 D smpc_construct_pdu
;0x07f1f46c D smpc_recv_pdu
;0x07f1f6b4 D dev_bdaddr
;0x07fc0000 D __Vectors
;0x07fc00a0 D __Vectors_End
;0x07fc00a1 T __main
;0x07fc00a1 T _main_stk
;0x07fc00a5 T _main_scatterload
;0x07fc00a9 T __main_after_scatterload
;0x07fc00a9 T _main_clock
;0x07fc00a9 T _main_cpp_init
;0x07fc00a9 T _main_init
;0x07fc00b1 T __rt_final_cpp
;0x07fc00b1 T __rt_final_exit
;0x07fc00b5 T Reset_Handler
;0x07fc00bd T NMI_Handler
;0x07fc00bf T HardFault_Handler
;0x07fc00d9 T PendSV_Handler
;0x07fc00db T SysTick_Handler
;0x07fc00dd T ADC_Handler
;0x07fc00dd T BLE_RF_DIAG_Handler
;0x07fc00dd T DMA_Handler
;0x07fc00dd T GPIO0_Handler
;0x07fc00dd T GPIO1_Handler
;0x07fc00dd T GPIO2_Handler
;0x07fc00dd T GPIO3_Handler
;0x07fc00dd T GPIO4_Handler
;0x07fc00dd T I2C_Handler
;0x07fc00dd T KEYBRD_Handler
;0x07fc00dd T PCM_Handler
;0x07fc00dd T RESERVED21_Handler
;0x07fc00dd T RESERVED22_Handler
;0x07fc00dd T RESERVED23_Handler
;0x07fc00dd T RFCAL_Handler
;0x07fc00dd T SPI_Handler
;0x07fc00dd T SRCIN_Handler
;0x07fc00dd T SRCOUT_Handler
;0x07f0d7b9 T SWTIM_Handler
;0x07fc00dd T UART2_Handler
;0x07fc00dd T WKUP_QUADEC_Handler
;0x07fc00ed T __scatterload
;0x07fc00ed T __scatterload_rt2
;0x07fc0111 T __scatterload_copy
;0x07fc011f T __scatterload_null
;0x07fc0121 T __scatterload_zeroinit
;0x07fc0401 T dummyf
;0x07fc0405 T SVC_Handler_c
;0x07fc0407 T patch_func
;0x07fc0409 T init_pwr_and_clk_ble_1
;0x07fc04b9 T init_pwr_and_clk_ble
;0x07fc0583 T SetSystemVars_func
;0x07fc0617 T set_pad_functions
;0x07fc0619 T periph_init
;0x07fc0669 T conditionally_run_radio_cals
;0x07fc066b T assert_err
;0x07fc0677 T assert_param
;0x07fc0683 T assert_warn
;0x07fc0685 T main
;0x07fc07b3 T send_pkt_to_l2cc
;0x07fc07df T crypto_init_func
;0x07fc07e1 T ba431_get_rand_func
;0x07fc07f9 T dia_rand_func
;0x07fc09b9 T dbg_init_func
;0x07fc09cd T dbg_warning
;0x07fc09cf T dbg_platform_reset_complete_func
;0x07fc1089 T clr_ripple_spi_cs
;0x07fc1091 T set_ripple_spi_cs
;0x07fc1099 T rf_rpl_reg_wr
;0x07fc10fb T rf_rpl_reg_rd
;0x07fc1d59 T rf_init_func
;0x07fc1f21 T rf_reinit_func
;0x07fc2055 T prf_init_func
;0x07fc20af T prf_add_profile_func
;0x07fc20b3 T prf_create_func
;0x07fc20bf T prf_cleanup_func
;0x07fc20cb T prf_env_get
;0x07fc20f5 T prf_src_task_get
;0x07fc2105 T prf_dst_task_get
;0x07fc2115 T prf_get_id_from_task_func
;0x07fc2145 T prf_get_task_from_id_func
;0x07fc2178 D gap_cfg_user_var_struct
;0x07fc22bc D dbg_default_handler
;0x07fc24f0 D rom_func_addr_table_var
;0x07fc2674 D rom_cfg_table_var
;0x07fc2730 D SystemCoreClock
;0x07fc2734 D dbg_assert_block
;0x07fc2760 D dbg_state
;0x07fc2778 D rwip_heap_non_ret
;0x07fc284c D prf_env
;0x07fc2b4c D rwip_heap_env_ret
;0x07fc3e98 D rwip_heap_msg_ret
;0x07fc5984 D rwip_heap_db_ret
;0x07fcfa00 D __initial_sp
0x07fd7600 D ble_wakeup_executed
0x07fd7601 D rf_in_sleep
0x07fd7604 D custom_preinit
0x07fd7608 D custom_postinit
0x07fd760c D custom_appinit
0x07fd7610 D custom_preloop
0x07fd7614 D custom_preschedule
0x07fd7618 D custom_postschedule
0x07fd761c D custom_postschedule_async
0x07fd7620 D custom_presleepcheck
0x07fd7624 D custom_appsleepset
0x07fd7628 D custom_postsleepcheck
0x07fd762c D custom_presleepenter
0x07fd7630 D custom_postsleepexit
0x07fd7634 D custom_prewakeup
0x07fd7638 D custom_postwakeup
0x07fd763c D custom_preidlecheck
0x07fd7640 D custom_pti_set
0x07fd7644 D REG_BLE_EM_TX_BUFFER_SIZE
0x07fd7648 D REG_BLE_EM_RX_BUFFER_SIZE
0x07fd764c D _ble_base
0x07fd7650 D gap_cfg_user
0x07fd7654 D rom_func_addr_table
0x07fd7658 D rom_cfg_table
0x07fd7674 D length_exchange_needed
0x07fd7678 D enh_con_cmp_cnt
0x07fd7680 D rx_pkt_cnt
0x07fd7684 D rx_pkt_cnt_bad
0x07fd7688 D rx_pkt_cnt_bad_adv
0x07fd768c D rx_pkt_cnt_bad_scn
0x07fd7690 D rx_pkt_cnt_bad_oth
0x07fd7694 D rx_pkt_cnt_bad_wo_sync_err
0x07fd7698 D rx_pkt_cnt_bad_con
0x07fd769c D connect_req_cnt
0x07fd76a0 D last_status
0x07fd76a4 D llc_state
0x07fd76b0 D lld_wlcoex_enable
0x07fd76b4 D ble_duplicate_filter_max
0x07fd76b5 D ble_duplicate_filter_found
0x07fd76b8 D alter_conn_adv_all_cnt
0x07fd76bc D alter_conn_adv_dir_cnt
0x07fd76c0 D alter_conn_adv_cnt
0x07fd76c4 D create_conn_cnt
0x07fd76c8 D alter_conn_cnt
0x07fd76cc D alter_conn_restart_cnt
0x07fd76d0 D alter_conn_peer_addr
0x07fd76d6 D alter_conn_local_addr
0x07fd76dc D set_adv_data_discard_old
0x07fd76dd D llm_resolving_list_max
0x07fd76de D llm_bt_env
0x07fd76e8 D init_tx_cnt_cntl_cnt1
0x07fd76ec D init_tx_cnt_cntl_cnt
0x07fd76f0 D tx_cnt_cntl_cnt
0x07fd76f4 D llm_state
0x07fd76f8 D gtl_state
0x07fd76f9 D use_h4tl
0x07fd76fc D hci_cmd_desc_root_tab
0x07fd772c D gattc_state
0x07fd7734 D gattm_state
0x07fd7735 D l2cc_state
0x07fd773e D l2cm_env
0x07fd7744 D gapc_state
0x07fd774c D gapm_state
0x07fd7754 D ke_free_bad
0x07fd7758 D rwip_env
0x07fd7764 D ble_reg_save
0x07fd77b4 D sleep_env
0x07fd77b8 D uart_env
0x07fd77dc D ke_mem_heaps_used
0x07fd77e0 D co_buf_env
0x07fd7918 D llc_env
0x07fd7938 D lld_evt_env
0x07fd7964 D llm_le_env
0x07fd7a60 D llm_resolving_list
0x07fd7cf8 D gtl_env
0x07fd7d40 D hci_env
0x07fd7d68 D gattc_env
0x07fd7d88 D gattm_env
0x07fd7dac D l2cc_env
0x07fd7dcc D ecdh_key
0x07fd7e2c D gapc_env
0x07fd7e4c D gapm_env
0x07fd7e78 D ke_env
0x07fd7fb4 D rwip_rf
0x07f02983 T dia_srand  
0x07f1ec58 D rom_hci_cmd_desc_root_tab
0x07f0d595 T modulationGainCalibration_func
0x07f0d68b T DCoffsetCalibration_func
0x07f0d6d7 T PllLF_IFF_MIX_CapCalibration_func
0x07fd765c D BLE_TX_DESC_DATA_USER   
0x07fd7660 D BLE_TX_DESC_CNTL_USER  
0x07fd7664 D LLM_LE_ADV_DUMMY_IDX   
0x07fd7668 D LLM_LE_SCAN_CON_REQ_ADV_DIR_IDX 
0x07fd766c D LLM_LE_SCAN_RSP_IDX      
0x07fd7670 D LLM_LE_ADV_IDX
0x07f1b58f T smpc_public_key_exchange_start_func
0x07f1b62f T smpc_dhkey_calc_ind_func
0x07f1b6bf T smpm_ecdh_key_create_func
0x07fd7750 D ecdh_key_creation_in_progress

; additional symbols used by the SDK
0x07fd76ac D lld_sleep_env
0x07fd7d50 D h4tl_env

; other symbols
0x07fd7cb8 D ea_env

; rf calibrations
0x07f029b5 T modulationGainCalibration
0x07f029c1 T DCoffsetCalibration
0x07f029cd T PllLF_IFF_MIX_CapCalibration

; symbols used by patch library
0x07f10b75 T atts_mtu_exc_req
0x07f11957 T atts_allocate_pdu
0x07f11979 T atts_send_pdu
0x07f05fe3 T llcp_length_req_handler
0x07f19157 T smpc_construct_id_addr_info_pdu
0x07f07cc9 T lld_evt_channel_next
0x07f1f314 D l2cc_connor_pkt_format
0x07f1f31c D l2cc_signaling_pkt_format
0x07f1f378 D l2cc_security_pkt_format
0x07f1f3b4 D l2cc_attribute_pkt_format
