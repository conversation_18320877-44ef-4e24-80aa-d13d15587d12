// SET_FREEZE_REG
_WWORD(0x50003300, 8)

// WATCHDOG_CTRL_REG
_WWORD(0x50003102, 1)

// WATCHDOG_REG
_WWORD(0x50003100, 5)

RESET

// Remap to SysRAM1 (First 32K block)
_WWORD(0x50000012, 0xA2)

LOAD %L

FUNC void disp_heaplog(void)
{
    int idx;
    unsigned int min_delta;

    exec("log > Memlog.log");
    
    printf("\n\n*** Memory Logging Results ***\n\n");
    printf(">>> ENV HEAP <<<\n");
    printf("Used size in this HEAP  : %4d (current) - %4d (maximum)\n", heap_usage_env.used_sz, heap_usage_env.max_used_sz);
    printf("Used size in other HEAPs: %4d (current) - %4d (maximum)\n\n", heap_usage_env.used_other_sz, heap_usage_env.max_used_other_sz);

    printf(">>> DB HEAP <<<\n");
    printf("Used size in this HEAP  : %4d (current) - %4d (maximum)\n", heap_usage_db.used_sz, heap_usage_db.max_used_sz);
    printf("Used size in other HEAPs: %4d (current) - %4d (maximum)\n\n", heap_usage_db.used_other_sz, heap_usage_db.max_used_other_sz);

    printf(">>> MSG HEAP <<<\n");
    printf("Used size in this HEAP  : %4d (current) - %4d (maximum)\n", heap_usage_msg.used_sz, heap_usage_msg.max_used_sz);
    printf("Used size in other HEAPs: %4d (current) - %4d (maximum)\n\n", heap_usage_msg.used_other_sz, heap_usage_msg.max_used_other_sz);

    printf(">>> Non-Ret HEAP <<<\n");
    printf("Used size in this HEAP  : %4d (current) - %4d (maximum)\n", heap_usage_nonRet.used_sz, heap_usage_nonRet.max_used_sz);
    printf("Used size in other HEAPs: %4d (current) - %4d (maximum)\n\n", heap_usage_nonRet.used_other_sz, heap_usage_nonRet.max_used_other_sz);

    exec("log off");
} //disp_heaplog() ends
