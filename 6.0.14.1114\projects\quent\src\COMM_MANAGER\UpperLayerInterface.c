#include"Comm_Manager.h"
#include"UpperLayerInterface.h"

static uint8_t* buffer = NULL;
static uint32_t buffer_length = 0;

//TODO temp implemntation needs to be verified
uint16_t getMtuSize(enum comm_subid subid)
{
	(void) subid;
	return 3000;
}

//TODO temp implemntation needs to be verified
uint32_t getRequesterTimeStmap(void)
{
	return 1;
}

//TODO temp implemntation needs to be verified
vitals_request_origin_t getCommRequester(void)
{
	return Watch;
}
//TODO temp complete implementation
uint32_t CmdIsServicable(enum comm_subid subid)
{
	(void)subid;
	return 1;
}
//TODO temp complete implementation
uint32_t IndIsServicable(enum comm_subid subid)
{
	(void)subid;
    return 1;
}

void MakePacket(struct _Exchange *packet, uint8_t *data, uint16_t len)
{
	if(packet->SUB_ID == comm_wblemacid)
	{
		/*
				Index[5] might need change.
				rsend is updating the 5th byte of the payload with requester.
				It makes sense to have update and send requester from STM but not wifi and BLE.
		*/
		updateMacIDPayload(packet->Data + 5, &len); 
		packet->size = len + 5;
	}
	else
	{
		memcpy(packet->Data, data, len);
		packet->size = len;
	}
}
enum exec_req BLE_ExecuteCommand(enum comm_subid sub_id)
{
	switch(sub_id)
	{
		case comm_wblemacid:
		{
			return UL_C;
		}
	
	default:
		return UL_F;
	}
}
enum exec_req BLE_ExecuteIndication(enum comm_subid sub_id, uint8_t* data, uint16_t len)
{	
	/*
	if(sub_id == comm_wSPO2)
		return UL_S;
	*/
	
	sendOverBle(sub_id, data, len);
	return UL_C;

}

enum exec_req BLE_ExecuteResponse(enum comm_subid sub_id, uint8_t* data, uint16_t len)
{	
	sendOverBle(sub_id, data, len);
	return UL_C;

}

enum exec_req BLE_ExecuteBufferxfer(enum comm_subid sub_id)
{
	struct Buffer_Xfer* context = GetBufferXferContext();
	sendBuffOverBle(sub_id, buffer, buffer_length, context->current_segment, context->crc);
	return UL_C;
}

int32_t updateGattBuffer(uint8_t *data, uint16_t length)
{
	buffer = data;
  buffer_length = length;
	return 1;
}

void _platform_upd_requester(struct _Exchange *resp, int32_t cr_exec_status)
{
	(void)resp;
	(void)cr_exec_status;
}
