;#<FEEDBACK># ARM Linker, 5060750: Last Updated: Thu Mar 05 18:17:23 2020
;VERSION 0.2
;FILE adc_58x.o
adc_disable <= USED 0
adc_enable <= USED 0
adc_offset_calibrate <= USED 0
adc_register_interrupt <= USED 0
adc_reset <= USED 0
adc_unregister_interrupt <= USED 0
;FILE app.o
active_conhdl_to_conidx <= USED 0
active_conidx_to_conhdl <= USED 0
app_easy_gap_advertise_stop <= USED 0
app_easy_gap_advertise_with_timeout_stop <= USED 0
app_easy_gap_dev_config_get_active <= USED 0
app_easy_gap_directed_advertise_get_active <= USED 0
app_easy_gap_directed_advertise_start <= USED 0
app_easy_gap_disconnect <= USED 0
app_easy_gap_get_peer_features <= USED 0
app_easy_gap_non_connectable_advertise_get_active <= USED 0
app_easy_gap_non_connectable_advertise_start <= USED 0
app_easy_gap_param_update_get_active <= USED 0
app_easy_gap_set_data_packet_length <= USED 0
app_easy_gap_start_connection_to <= USED 0
app_easy_gap_start_connection_to_get_active <= USED 0
app_easy_gap_start_connection_to_set <= USED 0
app_easy_gap_undirected_advertise_with_timeout_start <= USED 0
app_gattc_svc_changed_cmd_send <= USED 0
app_set_prf_srv_perm <= USED 0
app_timer_set <= USED 0
;FILE app_customs.o
app_custs1_init <= USED 0
;FILE app_customs_common.o
app_custs1_val_wr_validate <= USED 0
;FILE app_customs_task.o
;FILE app_default_handlers.o
default_advertise_operation <= USED 0
default_app_generate_static_random_addr <= USED 0
default_app_generate_unique_static_random_addr <= USED 0
default_app_on_disconnect <= USED 0
;FILE app_diss.o
;FILE app_diss_task.o
;FILE app_easy_msg_utils.o
app_easy_msg_free_callback <= USED 0
app_easy_msg_modify <= USED 0
app_easy_msg_set <= USED 0
app_easy_wakeup <= USED 0
app_easy_wakeup_free <= USED 0
app_easy_wakeup_set <= USED 0
app_msg_utils_api_process_handler <= USED 0
;FILE app_easy_timer.o
app_easy_timer_cancel_all <= USED 0
app_easy_timer_modify <= USED 0
;FILE app_easy_whitelist.o
app_easy_manage_wlist <= USED 0
;FILE app_entry_point.o
;FILE app_msg_utils.o
app_msg_send_wakeup_ble <= USED 0
;FILE app_task.o
;FILE app_utils.o
app_fill_random_byte_array <= USED 0
app_get_address_type <= USED 0
;FILE arch_main.o
;FILE arch_rom.o
;FILE arch_sleep.o
arch_ble_ext_wakeup_off <= USED 0
arch_ble_ext_wakeup_on <= USED 0
arch_force_active_mode <= USED 0
arch_get_sleep_mode <= USED 0
arch_last_rwble_evt_get <= USED 0
arch_restore_sleep_mode <= USED 0
arch_set_deep_sleep <= USED 0
;FILE arch_system.o
__ARM_common_ll_muluu <= USED 0
arch_set_pxact_gpio <= USED 0
arch_wkupct_tweak_deb_time <= USED 0
calibrate_rcx20 <= USED 0
lld_sleep_lpcycles_2_us_rcx_func <= USED 0
lld_sleep_us_2_lpcycles_rcx_func <= USED 0
read_rcx_freq <= USED 0
;FILE attm_db_128.o
;FILE battery.o
battery_get_lvl <= USED 0
;FILE custom_common.o
;FILE custs1.o
;FILE custs1_task.o
;FILE diss.o
;FILE gpio.o
GPIO_ConfigurePinPower <= USED 0
GPIO_DisablePorPin <= USED 0
GPIO_EnableIRQ <= USED 0
GPIO_EnablePorPin <= USED 0
GPIO_GetIRQInputLevel <= USED 0
GPIO_GetPinFunction <= USED 0
GPIO_GetPinStatus <= USED 0
GPIO_GetPorTime <= USED 0
GPIO_RegisterCallback <= USED 0
GPIO_SetIRQInputLevel <= USED 0
GPIO_SetPorTime <= USED 0
GPIO_is_valid <= USED 0
;FILE hardfault_handler.o
;FILE hash.o
hash <= USED 0
;FILE hw_otpc_58x.o
hw_otpc_blank <= USED 0
hw_otpc_cancel_prepare <= USED 0
hw_otpc_dma_prog <= USED 0
hw_otpc_dma_read <= USED 0
hw_otpc_fifo_prog <= USED 0
hw_otpc_fifo_read <= USED 0
hw_otpc_manual_prog <= USED 0
hw_otpc_manual_read_off <= USED 0
hw_otpc_manual_word_prog <= USED 0
hw_otpc_num_of_rr <= USED 0
hw_otpc_power_save <= USED 0
hw_otpc_prepare <= USED 0
hw_otpc_set_speed <= USED 0
hw_otpc_tdec <= USED 0
hw_otpc_twr <= USED 0
hw_otpc_write_rr <= USED 0
;FILE i2c.o
i2c_init <= USED 0
i2c_master_receive_buffer_async <= USED 0
i2c_master_receive_buffer_sync <= USED 0
i2c_master_transmit_buffer_async <= USED 0
i2c_master_transmit_buffer_sync <= USED 0
i2c_register_int <= USED 0
i2c_release <= USED 0
i2c_setup_master <= USED 0
i2c_setup_slave <= USED 0
i2c_slave_receive_buffer_async <= USED 0
i2c_slave_receive_buffer_sync <= USED 0
i2c_slave_transmit_buffer_async <= USED 0
i2c_slave_transmit_buffer_sync <= USED 0
i2c_unregister_int <= USED 0
;FILE i2c_eeprom.o
i2c_eeprom_configure <= USED 0
i2c_eeprom_get_configuration <= USED 0
i2c_eeprom_initialize <= USED 0
i2c_eeprom_read_byte <= USED 0
i2c_eeprom_read_data <= USED 0
i2c_eeprom_release <= USED 0
i2c_eeprom_update_slave_address <= USED 0
i2c_eeprom_write_byte <= USED 0
i2c_eeprom_write_data <= USED 0
i2c_eeprom_write_page <= USED 0
i2c_wait_until_eeprom_ready <= USED 0
;FILE jump_table.o
dummyf <= USED 0
;FILE nmi_handler.o
;FILE nvds.o
;FILE otp_hdr.o
;FILE patch.o
;FILE pll_vcocal_lut.o
GPADC_init <= USED 0
MedianOfFive <= USED 0
check_pll_lock <= USED 0
clear_HW_LUT <= USED 0
find_initial_calcap_ranges <= USED 0
meas_precharge_freq <= USED 0
min <= USED 0
pll_vcocal_LUT_InitUpdate <= USED 0
save_configure_restore <= USED 0
set_rf_cal_cap <= USED 0
update_LUT <= USED 0
update_calcap_max_channel <= USED 0
update_calcap_min_channel <= USED 0
update_calcap_ranges <= USED 0
write_HW_LUT <= USED 0
write_one_SW_LUT_entry <= USED 0
;FILE prf.o
prf_reset_func <= USED 0
;FILE prf_utils.o
prf_pack_date_time <= USED 0
prf_unpack_date_time <= USED 0
;FILE rf_585.o
rf_nfm_disable <= USED 0
rf_nfm_enable <= USED 0
rf_nfm_is_enabled <= USED 0
;FILE rf_calibration.o
DCoffsetCalibration_580 <= USED 0
IffCalibration_580 <= USED 0
enable_rf_diag_irq <= USED 0
rf_calibration_580 <= USED 0
set_gauss_modgain <= USED 0
;FILE rwble.o
;FILE rwip.o
;FILE smpc_util.o
JT_llm_p256_start_func <= USED 0
;FILE spi_58x.o
spi_access <= USED 0
spi_cs_high <= USED 0
spi_cs_low <= USED 0
spi_disable <= USED 0
spi_enable <= USED 0
spi_initialize <= USED 0
spi_receive <= USED 0
spi_send <= USED 0
spi_set_bitmode <= USED 0
spi_set_cp_mode <= USED 0
spi_set_cs_pad <= USED 0
spi_set_irq_mode <= USED 0
spi_set_speed <= USED 0
spi_transaction <= USED 0
spi_transfer <= USED 0
;FILE spi_flash.o
spi_flash_auto_detect <= USED 0
spi_flash_block_erase <= USED 0
spi_flash_block_erase_no_wait <= USED 0
spi_flash_chip_erase <= USED 0
spi_flash_chip_erase_forced <= USED 0
spi_flash_configure_env <= USED 0
spi_flash_configure_memory_protection <= USED 0
spi_flash_enable <= USED 0
spi_flash_enable_with_autodetect <= USED 0
spi_flash_erase_fail <= USED 0
spi_flash_fill <= USED 0
spi_flash_get_power_mode <= USED 0
spi_flash_is_busy <= USED 0
spi_flash_is_empty <= USED 0
spi_flash_is_page_empty <= USED 0
spi_flash_is_sector_empty <= USED 0
spi_flash_page_erase <= USED 0
spi_flash_page_fill <= USED 0
spi_flash_page_program <= USED 0
spi_flash_page_program_buffer <= USED 0
spi_flash_power_down <= USED 0
spi_flash_program_fail <= USED 0
spi_flash_read_config_reg <= USED 0
spi_flash_read_data <= USED 0
spi_flash_read_data_buffer <= USED 0
spi_flash_read_jedec_id <= USED 0
spi_flash_read_rems_id <= USED 0
spi_flash_read_security_reg <= USED 0
spi_flash_read_status_reg <= USED 0
spi_flash_read_unique_id <= USED 0
spi_flash_release_from_power_down <= USED 0
spi_flash_set_power_mode <= USED 0
spi_flash_set_write_disable <= USED 0
spi_flash_set_write_enable <= USED 0
spi_flash_wait_till_ready <= USED 0
spi_flash_write_data <= USED 0
spi_flash_write_data_buffer <= USED 0
spi_flash_write_enable_volatile <= USED 0
spi_flash_write_status_config_reg <= USED 0
spi_flash_write_status_reg <= USED 0
;FILE startup_da14585_586.o
;FILE syscntl.o
syscntl_set_dcdc_vbat3v_level <= USED 0
;FILE system_da14585_586.o
SystemCoreClockUpdate <= USED 0
;FILE trng.o
;FILE uart.o
uart_baudrate_setf <= USED 0
uart_disable <= USED 0
uart_disable_flow_control <= USED 0
uart_enable <= USED 0
uart_enable_flow_control <= USED 0
uart_initialize <= USED 0
uart_read_buffer <= USED 0
uart_read_byte <= USED 0
uart_receive <= USED 0
uart_register_err_cb <= USED 0
uart_register_rx_cb <= USED 0
uart_register_tx_cb <= USED 0
uart_send <= USED 0
uart_wait_tx_finish <= USED 0
uart_write_buffer <= USED 0
uart_write_byte <= USED 0
;FILE user_custs1_impl.o
user_svc1_adc_val_1_ntf_cfm_handler <= USED 0
user_svc1_button_ntf_cfm_handler <= USED 0
user_svc1_indicateable_cfg_ind_handler <= USED 0
user_svc1_indicateable_ind_cfm_handler <= USED 0
user_svc1_long_val_ntf_cfm_handler <= USED 0
;FILE user_periph_setup.o
;FILE user_peripheral.o
;FILE wkupct_quadec.o
quad_decoder_disable_irq <= USED 0
quad_decoder_enable_irq <= USED 0
quad_decoder_init <= USED 0
quad_decoder_register_callback <= USED 0
quad_decoder_release <= USED 0
wkupct_disable_irq <= USED 0
wkupct_enable_irq <= USED 0
wkupct_register_callback <= USED 0
